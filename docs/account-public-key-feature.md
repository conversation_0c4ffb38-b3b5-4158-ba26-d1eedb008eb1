# Account Public Key Feature

This document describes the implementation of public key support for wallet accounts in the application.

## Overview

The application now supports storing and managing public keys (wallet addresses) for accounts with type `WA<PERSON>ET`. This enables better integration with blockchain data and crypto expense tracking.

## Features

- ✅ **Public Key Storage**: Store public keys for wallet accounts in the database
- ✅ **Frontend Integration**: UI form field for entering public keys when creating/editing wallet accounts
- ✅ **Validation**: Client-side and server-side validation for Solana public key format
- ✅ **Unique Constraint**: Database-level uniqueness constraint to prevent duplicate public keys
- ✅ **Conditional Display**: Public key field only appears for wallet account types
- ✅ **Data Table Integration**: Public key display in account listings with truncated format
- ✅ **Account Details**: Full public key display in account detail views
- ✅ **Copy to Clipboard**: One-click copy functionality for public keys in both table and detail views
- ✅ **Visual Feedback**: Copy buttons with success indicators and toast notifications

## Database Changes

### Schema Updates

```prisma
model Account {
  id String @id @default(uuid())
  name String
  type AccountType
  
  // NEW: Public key for wallet accounts (Solana, Ethereum, etc.)
  publicKey String? @unique
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  transactions Transaction[]
}
```

### Key Changes:
- Added `publicKey` field as optional string
- Added unique constraint to prevent duplicate public keys
- Field is nullable to support non-wallet account types

## Backend Changes

### Account Router (`src/modules/accounts/accountRouter.ts`)

#### Validation
- Added Solana public key format validation using regex: `/^[1-9A-HJ-NP-Za-km-z]{32,44}$/`
- Public key is required for `WALLET` type accounts
- Public key is optional for other account types
- Duplicate public key validation at application level

#### API Changes
```typescript
// Create Account Schema
const createAccountSchema = z.object({
  name: z.string().min(1).max(255),
  type: z.nativeEnum(AccountType),
  publicKey: z.string().optional()
    .refine((val) => !val || solanaPublicKeyRegex.test(val), "Invalid public key format")
});

// Update Account Schema  
const updateAccountSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(255).optional(),
  type: z.nativeEnum(AccountType).optional(),
  publicKey: z.string().optional()
    .refine((val) => !val || solanaPublicKeyRegex.test(val), "Invalid public key format")
});
```

#### Business Logic
- Validates public key requirement for wallet accounts
- Checks for duplicate public keys before creation/update
- Handles empty string to undefined conversion

## Frontend Changes

### Account Form (`src/components/AccountForm.tsx`)

#### New Features
- Conditional public key input field that appears only when account type is `WALLET`
- Real-time validation with error messages
- Monospace font for better readability
- Helpful placeholder and description text

#### Form Structure
```typescript
// Form values now include publicKey
const form = useForm({
  defaultValues: {
    name: "",
    type: "WALLET" as AccountType,
    publicKey: "", // NEW
  },
  // ...
});
```

#### Conditional Rendering
```tsx
{/* Public Key field - only show for WALLET type */}
<form.Subscribe selector={(state) => [state.values.type]}>
  {([type]) => 
    type === "WALLET" && (
      <form.Field name="publicKey" validators={{...}}>
        {/* Public key input field */}
      </form.Field>
    )
  }
</form.Subscribe>
```

### Account Data Table (`src/components/AccountDataTable.tsx`)

#### New Column with Copy Functionality
- Added "Public Key" column that shows truncated public keys for wallet accounts
- Format: `7ScYHk...zLj` (first 6 + last 4 characters) with copy button
- **Click-to-copy**: Each public key has a copy button for easy clipboard access
- Visual feedback: Copy button shows checkmark when successfully copied
- Toast notifications: Success/error messages when copying
- Shows "—" for non-wallet accounts or accounts without public keys
- Full public key available on hover (title attribute)

### Account Details Drawer (`src/components/AccountDetailsDrawer.tsx`)

#### Enhanced Details with Copy Functionality
- Shows full public key in a dedicated section for wallet accounts
- **One-click copy**: Dedicated copy button next to the full public key
- Styled with monospace font and selectable text
- Visual feedback: Copy button shows checkmark when successfully copied
- Toast notifications: Success/error messages when copying
- Only displays for wallet accounts that have a public key
- Copy-friendly formatting with background highlight

## Usage Examples

### Creating a Wallet Account

```typescript
// Via tRPC
const result = await trpc.accounts.create.mutate({
  name: "My Solana Wallet",
  type: "WALLET",
  publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj"
});

// Via Prisma (direct)
const account = await prisma.account.create({
  data: {
    name: "My Solana Wallet", 
    type: "WALLET",
    publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj"
  }
});
```

### Creating a Non-Wallet Account

```typescript
// Public key is optional for non-wallet accounts
const result = await trpc.accounts.create.mutate({
  name: "My Bank Account",
  type: "BANK_ACCOUNT"
  // publicKey not required
});
```

### Querying Accounts with Public Keys

```typescript
// Find all wallet accounts
const walletAccounts = await prisma.account.findMany({
  where: { type: "WALLET" }
});

// Find accounts with public keys
const accountsWithKeys = await prisma.account.findMany({
  where: { publicKey: { not: null } }
});

// Find account by public key
const account = await prisma.account.findUnique({
  where: { publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj" }
});
```

## Validation Rules

### Public Key Format
- Must be 32-44 characters long
- Must match Solana address format: `/^[1-9A-HJ-NP-Za-km-z]{32,44}$/`
- Cannot contain confusing characters (0, O, I, l)

### Business Rules
- Public key is **required** for `WALLET` type accounts
- Public key is **optional** for other account types (`BANK_ACCOUNT`, `EXCHANGE_ACCOUNT`, `CREDIT_CARD`)
- Public keys must be **unique** across all accounts
- Empty strings are converted to `undefined`

## Error Handling

### Common Errors
- `"Public key is required for wallet accounts"` - When creating wallet without public key
- `"Invalid public key format"` - When public key doesn't match expected format
- `"An account with this public key already exists"` - When trying to use duplicate public key

### Frontend Validation
- Real-time validation as user types
- Clear error messages below input field
- Form submission blocked until validation passes

### Backend Validation
- Server-side validation using Zod schemas
- Database constraint enforcement
- Proper error responses with meaningful messages

## Testing

### Automated Tests
Run the test suite to verify functionality:

```bash
pnpm tsx src/scripts/testAccountPublicKey.ts
```

### Test Coverage
- ✅ Create wallet account with public key
- ✅ Create non-wallet account without public key
- ✅ Duplicate public key prevention
- ✅ Account updates with new public key
- ✅ Query operations by type and public key
- ✅ Data cleanup and constraints

### Copy Functionality Testing
Create test accounts to verify copy functionality:

```bash
# Create test accounts with various public keys
pnpm tsx src/scripts/testCopyablePublicKey.ts

# Clean up test accounts when done
pnpm tsx src/scripts/testCopyablePublicKey.ts --cleanup
```

#### Manual Testing Steps
1. Open the accounts page in your browser
2. Look for wallet accounts with public keys
3. Verify truncated public keys show with copy buttons
4. Click copy buttons to test clipboard functionality
5. Verify toast notifications appear on successful copy
6. Click on wallet account to open details
7. Test full public key copy functionality
8. Verify non-wallet accounts show "—" in public key column

## Migration Notes

### Existing Data
- Existing accounts are not affected
- `publicKey` field is nullable, so existing accounts will have `null` values
- No data migration required

### Backward Compatibility
- All existing functionality continues to work
- API endpoints accept the new optional `publicKey` field
- Frontend gracefully handles accounts with and without public keys

## Future Enhancements

### Potential Improvements
- Support for multiple blockchain formats (Ethereum, Bitcoin, etc.)
- Public key validation for different blockchain types
- Integration with wallet connection libraries
- Automatic balance fetching for wallet accounts
- Transaction import based on public key

### Integration Opportunities
- Link with crypto expense tracking
- Automatic transaction discovery
- Portfolio tracking and analytics
- Multi-chain support
