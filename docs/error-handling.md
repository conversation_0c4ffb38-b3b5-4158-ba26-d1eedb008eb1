# Beautiful Error Handling for tRPC

This document explains the comprehensive error handling system implemented for tRPC queries and mutations in the SAC Accounting App.

## Overview

The error handling system provides:

- **Global error handling** with beautiful toast notifications
- **Custom error handlers** for specific use cases
- **Inline error displays** for forms and components
- **Error boundaries** for catching unhandled errors
- **Type-safe error handling** with proper TypeScript support

## Components

### 1. Global Error Handling

All tRPC queries and mutations automatically show beautiful toast notifications when errors occur. This is configured in `src/utils/trpc.ts`:

```typescript
// Automatic error toasts for all queries and mutations
queryClientConfig: {
  defaultOptions: {
    queries: {
      onError: (error) => {
        toast.error("Failed to load data", {
          description: error.message,
          duration: 4000,
        });
      },
    },
    mutations: {
      onError: (error) => {
        toast.error("Operation failed", {
          description: error.message,
          duration: 5000,
        });
      },
    },
  },
},
```

### 2. Custom Error Handler Hook

Use `useTrpcErrorHandler` for custom error handling:

```typescript
import { useTrpcErrorHandler } from "@/hooks/useTrpcErrorHandler";

function MyComponent() {
  const { handleError } = useTrpcErrorHandler();

  const mutation = trpc.example.create.useMutation({
    onError: (error) => {
      handleError(error, "Failed to create example");
    },
  });
}
```

### 3. Inline Error Display Components

#### ErrorDisplay Component

```typescript
import { ErrorDisplay } from "@/components/ui/error-display";

<ErrorDisplay 
  error={error}
  title="Custom Error Title"
  size="md"
  showRetry
  onRetry={() => refetch()}
/>
```

#### QueryErrorDisplay Component

```typescript
import { QueryErrorDisplay } from "@/components/ui/error-display";

const query = trpc.example.getAll.useQuery();

{query.error && (
  <QueryErrorDisplay 
    error={query.error} 
    refetch={query.refetch}
  />
)}
```

#### MutationErrorDisplay Component

```typescript
import { MutationErrorDisplay } from "@/components/ui/error-display";

const mutation = trpc.example.create.useMutation();

{mutation.error && (
  <MutationErrorDisplay 
    error={mutation.error}
    retry={() => mutation.reset()}
  />
)}
```

### 4. Error Boundary

The app is wrapped with an ErrorBoundary that catches unhandled JavaScript errors:

```typescript
// In _app.tsx
<ErrorBoundary>
  <Component {...pageProps} />
  <Toaster />
</ErrorBoundary>
```

## Usage Patterns

### Pattern 1: Automatic Error Handling (Recommended)

Let the global error handler show toast notifications:

```typescript
const query = trpc.invoices.getAll.useQuery();
const mutation = trpc.invoices.create.useMutation({
  onSuccess: () => {
    toast.success("Invoice created successfully!");
    query.refetch();
  },
  // No onError needed - global handler will show toast
});
```

### Pattern 2: Custom Error Messages

Override the default error message:

```typescript
const mutation = trpc.invoices.create.useMutation({
  onError: (error) => {
    const { handleError } = useTrpcErrorHandler();
    handleError(error, "Failed to create invoice");
  },
});
```

### Pattern 3: Inline Error Display

Show errors inline in forms:

```typescript
const mutation = trpc.invoices.create.useMutation({
  // Don't use onError to prevent toast
});

return (
  <form>
    <Button onClick={() => mutation.mutate(data)}>
      Create Invoice
    </Button>
    {mutation.error && (
      <MutationErrorDisplay error={mutation.error} />
    )}
  </form>
);
```

### Pattern 4: Loading Error States

For query loading errors:

```typescript
const query = trpc.invoices.getAll.useQuery();

if (query.isLoading) return <div>Loading...</div>;
if (query.error) {
  return (
    <LoadingError 
      message="Failed to load invoices"
      onRetry={query.refetch}
    />
  );
}
```

## Error Types and Messages

The system automatically provides user-friendly messages for different error types:

- **UNAUTHORIZED**: "Authentication required"
- **FORBIDDEN**: "Access denied"
- **NOT_FOUND**: "Resource not found"
- **TIMEOUT**: "Request timeout"
- **TOO_MANY_REQUESTS**: "Too many requests"
- **INTERNAL_SERVER_ERROR**: "Server error"
- **Validation errors**: Shows specific field errors

## Best Practices

1. **Use global error handling by default** - Let the system show toast notifications automatically
2. **Override only when needed** - Use custom error handlers for specific business logic
3. **Show inline errors for forms** - Use inline error displays for form validation
4. **Provide retry functionality** - Always offer users a way to retry failed operations
5. **Log errors in development** - The system automatically logs detailed errors in development mode

## Styling

All error components use your app's design system:

- Consistent with shadcn/ui components
- Supports light/dark themes
- Responsive design
- Accessible with proper ARIA labels

## Testing

To test error handling:

1. Use the ErrorHandlingExample component: `/components/examples/ErrorHandlingExample.tsx`
2. Simulate network errors by disconnecting internet
3. Test with invalid data to trigger validation errors
4. Use the "Simulate Error" button in the example component
