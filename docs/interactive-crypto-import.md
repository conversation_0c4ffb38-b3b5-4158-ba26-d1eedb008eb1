# Interactive Crypto Expense Import

This document describes the enhanced interactive crypto expense import script that allows users to select wallets from the database and configure import settings through an intuitive command-line interface.

## Overview

The import script now features two modes:
- **Interactive Mode** (default): Guided prompts for wallet selection and configuration
- **Non-Interactive Mode**: Traditional CLI arguments for automation

## Features

- ✅ **Database Integration**: Automatically fetches wallet accounts from the database
- ✅ **Interactive Wallet Selection**: Choose from all wallets, select specific ones, or enter custom addresses
- ✅ **Multi-Select Interface**: Use space to select/deselect, enter to continue
- ✅ **Date Range Configuration**: Interactive date input with validation
- ✅ **Action Selection**: Preview, import, or import with transaction creation
- ✅ **Account Selection**: Choose accounts for transaction records
- ✅ **Validation**: Real-time validation for dates and wallet addresses
- ✅ **Confirmation**: Final review before execution
- ✅ **Error Handling**: Graceful handling of user cancellation and errors

## Usage

### Interactive Mode (Default)

```bash
# Start interactive mode
pnpm tsx src/scripts/importCryptoExpenses.ts
```

#### Interactive Flow

1. **Wallet Selection**
   ```
   📋 Found 4 wallet account(s) in the database:
   
      1. EU Wallet (EUk3St1W...aPKB)
      2. Test Solana Wallet 1 (7ScYHk4V...kzLj)
      3. Test Solana Wallet 2 (G9tt98aY...jTRB)
      4. Test Solana Wallet 3 (EPjFWdd5...Dt1v)
   
   ? How would you like to select wallets?
   ❯ Import from all wallets
     Select specific wallets
     Enter custom wallet addresses
   ```

2. **Specific Wallet Selection** (if "Select specific wallets" chosen)
   ```
   ? Select wallets to import from (use space to select, enter to continue):
   ◯ EU Wallet (EUk3St1W...aPKB)
   ◉ Test Solana Wallet 1 (7ScYHk4V...kzLj)
   ◉ Test Solana Wallet 2 (G9tt98aY...jTRB)
   ◯ Test Solana Wallet 3 (EPjFWdd5...Dt1v)
   ```

3. **Date Range Configuration**
   ```
   ⚙️  Configuration Options
   
   ? From date (YYYY-MM-DD, leave empty for all time): 2024-01-01
   ? To date (YYYY-MM-DD, leave empty for now): 2024-12-31
   ```

4. **Action Selection**
   ```
   ? What would you like to do?
   ❯ Preview data (no import)
     Import crypto expenses only
     Import crypto expenses and create transactions
   ```

5. **Account Selection** (if creating transactions)
   ```
   ? Select account for transaction records:
   ❯ EU Wallet (WALLET)
     Revolut Company (BANK_ACCOUNT)
     Nexpay (BANK_ACCOUNT)
   ```

6. **Final Confirmation**
   ```
   📋 Final Configuration:
      Type: buybacks
      From: 2024-01-01
      To: 2024-12-31
      Wallets: 2 selected
      Target mints: default mints
      Create transactions: true
      Account ID: account-id-123
      Mode: import
   
   ? Proceed with these settings? (Y/n)
   ```

### Non-Interactive Mode

```bash
# Traditional CLI mode
pnpm tsx src/scripts/importCryptoExpenses.ts --non-interactive \
  --preview \
  --company-wallets "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj,G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB" \
  --from-date "2024-01-01" \
  --to-date "2024-12-31"
```

## Command Line Options

### Interactive Mode Options
```bash
pnpm tsx src/scripts/importCryptoExpenses.ts [options]
```

### Non-Interactive Mode Options
```bash
pnpm tsx src/scripts/importCryptoExpenses.ts --non-interactive [options]
```

#### Available Options
- `--type <type>` - Type of crypto expense (buybacks, other) [default: buybacks]
- `--from-date <date>` - Start date (YYYY-MM-DD format)
- `--to-date <date>` - End date (YYYY-MM-DD format)
- `--company-wallets <wallets>` - Comma-separated wallet addresses
- `--target-mints <mints>` - Comma-separated token mint addresses
- `--create-transactions` - Create transaction records
- `--account-id <id>` - Account ID for transactions
- `--preview` - Preview data without importing
- `--dry-run` - Show what would be imported
- `--delete-existing` - Delete existing crypto expenses first
- `--non-interactive` - Skip interactive prompts
- `--help` - Show help message

## Wallet Selection Options

### 1. Import from All Wallets
- Automatically selects all wallet accounts with public keys
- No additional input required
- Best for comprehensive imports

### 2. Select Specific Wallets
- Multi-select checkbox interface
- Use **Space** to select/deselect wallets
- Use **Enter** to continue with selection
- Must select at least one wallet

### 3. Enter Custom Wallet Addresses
- Manual entry of wallet addresses
- Comma-separated format
- Real-time validation of address format
- Useful for wallets not in the database

## Validation Features

### Date Validation
- **Format**: YYYY-MM-DD required
- **Optional**: Empty dates allowed (defaults to all time/now)
- **Range**: Validates logical date ranges

### Wallet Address Validation
- **Format**: Solana address format (32-44 characters)
- **Characters**: Base58 encoding validation
- **Uniqueness**: Prevents duplicate selections

### Selection Validation
- **Required**: At least one wallet must be selected
- **Account**: Account required when creating transactions
- **Confirmation**: Final confirmation required before execution

## Error Handling

### User Cancellation
```bash
# Ctrl+C handling
❌ Operation cancelled by user (Ctrl+C).
```

### Validation Errors
```bash
# Invalid date format
Invalid date format. Use YYYY-MM-DD

# Invalid wallet address
Invalid wallet address format: invalid-address

# No wallets selected
Please select at least one wallet
```

### Database Errors
```bash
# No wallet accounts found
❌ No wallet accounts found in database.
   Please create wallet accounts with public keys first.
```

## Examples

### Example 1: Preview All Wallets
```bash
pnpm tsx src/scripts/importCryptoExpenses.ts
# Select: "Import from all wallets"
# Dates: Leave empty for all time
# Action: "Preview data (no import)"
# Confirm: Yes
```

### Example 2: Import Specific Wallets with Transactions
```bash
pnpm tsx src/scripts/importCryptoExpenses.ts
# Select: "Select specific wallets"
# Choose: 2 specific wallets with space key
# Dates: 2024-01-01 to 2024-12-31
# Action: "Import crypto expenses and create transactions"
# Account: Choose target account
# Confirm: Yes
```

### Example 3: Custom Wallets Non-Interactive
```bash
pnpm tsx src/scripts/importCryptoExpenses.ts --non-interactive \
  --company-wallets "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj" \
  --from-date "2024-01-01" \
  --create-transactions \
  --account-id "account-123"
```

## Database Requirements

### Wallet Accounts
The script requires wallet accounts in the database with:
- `type: "WALLET"`
- `publicKey: string` (not null)

### Create Wallet Accounts
```typescript
await prisma.account.create({
  data: {
    name: "My Solana Wallet",
    type: "WALLET",
    publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj"
  }
});
```

## Testing

### Test Interactive Features
```bash
# Demonstrate interactive features
pnpm tsx src/scripts/testInteractiveImport.ts

# Create test wallet accounts
pnpm tsx src/scripts/testCopyablePublicKey.ts

# Test non-interactive mode
pnpm tsx src/scripts/importCryptoExpenses.ts --non-interactive --preview

# Clean up test accounts
pnpm tsx src/scripts/testCopyablePublicKey.ts --cleanup
```

## Dependencies

### Required Packages
- `inquirer` - Interactive command line prompts
- `@types/inquirer` - TypeScript definitions
- `yargs` - Command line argument parsing

### Installation
```bash
pnpm add inquirer @types/inquirer
```

## Benefits

### User Experience
- **Intuitive**: Guided prompts reduce complexity
- **Visual**: Clear display of available options
- **Flexible**: Multiple selection methods
- **Safe**: Confirmation before execution

### Developer Experience
- **Maintainable**: Separation of interactive and CLI logic
- **Testable**: Both modes can be tested independently
- **Extensible**: Easy to add new prompts and options

### Operational Benefits
- **Database Integration**: No need to manually specify wallets
- **Validation**: Prevents common input errors
- **Automation**: Non-interactive mode for scripts
- **Audit Trail**: Clear logging of selections and actions

## Future Enhancements

### Potential Improvements
- **Saved Configurations**: Save and load common configurations
- **Batch Operations**: Process multiple date ranges
- **Progress Indicators**: Show progress during long operations
- **Configuration Profiles**: Predefined settings for different scenarios
- **Wallet Grouping**: Group wallets by purpose or team

### Integration Opportunities
- **CI/CD Integration**: Automated imports in pipelines
- **Scheduling**: Cron job integration with saved configurations
- **Monitoring**: Integration with monitoring systems
- **Notifications**: Slack/email notifications for import results
