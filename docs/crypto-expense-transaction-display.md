# Crypto Expense Display in Transactions Table

This document describes the implementation of crypto expense amount display in the transactions table, showing the spent EUR amount for crypto expense transactions.

## Overview

Crypto expenses are now fully integrated into the transactions table and details view, displaying the amount spent in EUR (converted from USD) alongside other transaction types like transfers and trades.

## Features Implemented

### ✅ **Transaction Table Integration**
- **Amount Display**: Shows crypto expense amounts as EUR equivalent of USD spent
- **Type Badge**: Purple "Crypto Expense" badge for easy identification
- **Description**: Shows expense type and token swap details (e.g., "BUYBACK: USDC → TOKEN")
- **Consistent Formatting**: Uses same currency formatting as other transaction types

### ✅ **Transaction Details View**
- **Dedicated Section**: Crypto Expense Details card with comprehensive information
- **Amount Highlighting**: Spent amount displayed in red to indicate expense
- **Token Details**: Shows both from and to token amounts with symbols
- **Transaction ID**: Copyable blockchain transaction ID
- **Timestamp**: Block timestamp from the blockchain

### ✅ **Statistics Integration**
- **Crypto Expenses Card**: New stats card showing count of crypto expense transactions
- **Updated Calculations**: Other Actions count excludes crypto expenses for accuracy

### ✅ **Backend Data Integration**
- **Transaction Router**: Updated to include crypto expense data in all queries
- **Type Safety**: Full TypeScript support for crypto expense fields
- **Consistent Queries**: All transaction endpoints include crypto expense relations

## Implementation Details

### 1. Amount Display Logic

The crypto expense amount is displayed using the `swapFromAmountUsd` field, which represents the USD amount spent on the transaction. This is formatted as EUR for consistency with the application's currency display.

```typescript
const formatTransactionAmount = (transaction: any) => {
  if (transaction.transfer) {
    return currency.formatMonetary(transaction.transfer.amount, transaction.transfer.currencyCode);
  }
  if (transaction.trade) {
    return `${currency.formatMonetary(transaction.trade.amountFrom, transaction.trade.tokenFrom)} → ${currency.formatMonetary(
      transaction.trade.amountTo,
      transaction.trade.tokenTo
    )}`;
  }
  if (transaction.cryptoExpense) {
    // Show the USD amount spent (swapFromAmountUsd) as EUR equivalent
    const usdAmount = Number(transaction.cryptoExpense.swapFromAmountUsd);
    return currency.formatMonetary(usdAmount, "EUR");
  }
  return "N/A";
};
```

### 2. Transaction Type Display

Crypto expenses are identified with a purple badge and appropriate icon:

```typescript
const getTransactionTypeBadge = (transaction: any) => {
  if (transaction.cryptoExpense) {
    return (
      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
        Crypto Expense
      </Badge>
    );
  }
  // ... other types
};
```

### 3. Description Format

The description shows the expense type and token swap details:

```typescript
const getTransactionDescription = (transaction: any) => {
  if (transaction.cryptoExpense) {
    const expense = transaction.cryptoExpense;
    return `${expense.type}: ${expense.swapFromSymbol} → ${expense.swapToSymbol}`;
  }
  // ... other types
};
```

### 4. Backend Data Structure

The transaction router includes crypto expense data in all queries:

```typescript
include: {
  cryptoExpense: {
    select: {
      id: true,
      type: true,
      txId: true,
      swapFromSymbol: true,
      swapToSymbol: true,
      swapFromAmount: true,
      swapToAmount: true,
      swapFromAmountUsd: true,
      swapToAmountUsd: true,
      blockTimestamp: true,
    },
  },
  // ... other relations
}
```

## Display Examples

### Transaction Table Row
```
Type: [Crypto Expense] (purple badge)
Amount: €1,000.50
Description: BUYBACK: USDC → TOKEN
Account: Main Wallet
Date: 2024-01-15
```

### Transaction Details View
```
Crypto Expense Details
├── Type: BUYBACK
├── Amount Spent (EUR): €1,000.50 (red text)
├── From Token: 1,000.50 USDC
├── To Token: 500,000.25 TOKEN
├── Transaction ID: 7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj [copy]
└── Block Timestamp: 1/15/2024, 10:30:45 AM
```

### Statistics Cards
```
Total Transactions: 150
Transfers: 80
Trades: 45
Crypto Expenses: 15  ← New card
Other Actions: 10
```

## Currency Conversion

### USD to EUR Display
- **Source**: `swapFromAmountUsd` field from crypto expense
- **Display**: Formatted as EUR using the application's currency formatter
- **Rationale**: Maintains consistency with other transaction amounts shown in EUR
- **Note**: This is a display conversion, not a real-time exchange rate conversion

### Amount Significance
- **Represents**: The USD value of tokens spent in the swap
- **Context**: For buybacks, this is the amount spent to purchase tokens
- **Color Coding**: Displayed in red in details view to indicate expense/outflow

## Testing

### Test Data Creation
```bash
# Create test crypto expense transaction
pnpm tsx src/scripts/testCryptoExpenseDisplay.ts

# Clean up test data
pnpm tsx src/scripts/testCryptoExpenseDisplay.ts --cleanup
```

### Manual Testing Checklist
- [ ] Crypto expense transactions show purple "Crypto Expense" badge
- [ ] Amount displays as EUR formatted currency (e.g., €1,000.50)
- [ ] Description shows "TYPE: FROM → TO" format
- [ ] Transaction details show dedicated crypto expense section
- [ ] Amount in details is highlighted in red
- [ ] Token amounts show with correct symbols
- [ ] Transaction ID is copyable
- [ ] Block timestamp displays correctly
- [ ] Statistics card shows correct crypto expense count

### Expected Results
1. **Table View**: Clear identification and amount display
2. **Details View**: Comprehensive crypto expense information
3. **Statistics**: Accurate counts including crypto expenses
4. **Consistency**: Same formatting standards as other transaction types

## Data Flow

### 1. Import Process
```
Flipside API → CryptoExpense → Transaction (optional) → Frontend Display
```

### 2. Display Process
```
Database Query → Include CryptoExpense → Format Amount → Display in Table
```

### 3. Amount Calculation
```
swapFromAmountUsd (USD) → Format as EUR → Display €X,XXX.XX
```

## Benefits

### User Experience
- **Clear Identification**: Easy to spot crypto expense transactions
- **Consistent Display**: Same formatting as other transaction types
- **Detailed Information**: Comprehensive details in transaction view
- **Amount Clarity**: Clear display of amount spent in familiar currency

### Data Integrity
- **Source Accuracy**: Uses actual USD amounts from blockchain data
- **Type Safety**: Full TypeScript support prevents display errors
- **Consistent Queries**: All transaction endpoints include crypto expense data

### Operational Benefits
- **Expense Tracking**: Easy identification of crypto-related expenses
- **Reporting**: Crypto expenses included in transaction statistics
- **Audit Trail**: Complete transaction history including crypto activities

## Future Enhancements

### Potential Improvements
1. **Real-time Exchange Rates**: Convert USD to EUR using current rates
2. **Currency Preferences**: Allow users to choose display currency
3. **Expense Categories**: Categorize different types of crypto expenses
4. **Filtering**: Filter transactions by crypto expense type
5. **Aggregation**: Summary views of crypto expenses by period

### Integration Opportunities
- **Accounting Integration**: Export crypto expenses for accounting systems
- **Tax Reporting**: Generate tax reports including crypto expenses
- **Budget Tracking**: Include crypto expenses in budget calculations
- **Analytics**: Detailed analytics on crypto expense patterns

## Summary

The crypto expense display integration successfully brings crypto transactions into the main transaction flow with:

✅ **Seamless Integration**: Crypto expenses appear alongside transfers and trades
✅ **Clear Identification**: Purple badges and descriptive labels
✅ **Amount Display**: EUR-formatted amounts for consistency
✅ **Detailed Information**: Comprehensive details in transaction view
✅ **Statistics Integration**: Accurate counts and categorization
✅ **Type Safety**: Full TypeScript support throughout

This implementation provides users with a unified view of all their financial transactions, including crypto-related expenses, in a consistent and user-friendly format.
