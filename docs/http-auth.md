# HTTP Authentication Setup

This application includes HTTP Basic Authentication that is automatically enabled when deployed to Vercel.

## How it works

- **Development**: No authentication required (runs on localhost)
- **Vercel deployment**: HTTP Basic Auth is automatically enabled
- **Other deployments**: No authentication (unless you manually set the `VERCEL` environment variable)

## Environment Variables

Set these environment variables in your Vercel deployment:

```bash
HTTP_AUTH_USERNAME=your-username
HTTP_AUTH_PASSWORD=your-secure-password
```

If not set, defaults are:
- Username: `admin`
- Password: `password`

## Public Endpoints

The following API endpoints bypass authentication (useful for webhooks, health checks, etc.):

- `/api/health` - Health check endpoint
- `/api/webhook` - Webhook endpoints

You can modify the `publicPaths` array in `middleware.ts` to add more public endpoints.

## Testing Authentication

### Local Testing with Vercel Environment

To test the auth locally, set the VERCEL environment variable:

```bash
VERCEL=1 pnpm dev
```

### Health Check

The `/api/health` endpoint provides information about the auth status:

```bash
curl http://localhost:3000/api/health
```

Response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "development",
  "authEnabled": false
}
```

### Accessing Protected Pages

When auth is enabled, you'll need to provide credentials:

```bash
curl -u username:password http://your-app.vercel.app/
```

Or in the browser, you'll see a login dialog when accessing any protected page.

## Customization

### Adding More Public Paths

Edit `middleware.ts` and add paths to the `publicPaths` array:

```typescript
const publicPaths = [
  '/api/health',
  '/api/webhook',
  '/api/public-endpoint',  // Add your public endpoints here
];
```

### Changing Auth Logic

The authentication logic is in `middleware.ts`. You can modify it to:
- Use different authentication methods
- Add role-based access
- Integrate with external auth providers
- Add IP whitelisting

## Security Notes

1. **Use strong passwords** in production
2. **Use HTTPS** (Vercel provides this automatically)
3. **Rotate credentials** regularly
4. **Consider using environment-specific credentials** for staging vs production
5. **Monitor access logs** for suspicious activity

## Deployment

When deploying to Vercel:

1. Set environment variables in Vercel dashboard
2. Deploy your app
3. Authentication will be automatically enabled
4. Test with your credentials

The auth will protect all pages and API routes except those in the `publicPaths` array.
