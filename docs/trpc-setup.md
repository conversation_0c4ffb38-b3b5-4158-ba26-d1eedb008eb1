# tRPC Setup Documentation

This document explains the tRPC setup for the SAC Accounting App.

## Overview

tRPC has been configured to provide type-safe API routes with full TypeScript integration. The setup includes:

- **Server-side**: tRPC routers with Prisma integration
- **Client-side**: React Query integration with tRPC hooks
- **Error handling**: Integration with neverthrow for consistent error handling
- **Type safety**: Full end-to-end type safety from server to client

## Project Structure

```
src/
├── server/
│   ├── trpc.ts                 # Core tRPC configuration
│   └── routers/
│       ├── _app.ts            # Main app router
│       ├── example.ts         # Example router
│       └── invoices.ts        # Invoice management router
├── pages/
│   ├── api/
│   │   └── trpc/
│   │       └── [trpc].ts      # tRPC API handler
│   ├── _app.tsx               # App component with tRPC provider
│   ├── index.tsx              # Home page with tRPC demo
│   └── invoices.tsx           # Invoice management page
└── utils/
    └── trpc.ts                # Client-side tRPC configuration
```

## Key Features

### 1. Type-Safe API Calls

```typescript
// Client-side usage
const hello = trpc.example.hello.useQuery({ text: "World" });
const invoices = trpc.invoices.getAll.useQuery({ limit: 10 });
```

### 2. Error Handling with neverthrow

All procedures return `Result<T, Error>` types for consistent error handling:

```typescript
// Server-side procedure
export const getInvoices = publicProcedure
  .input(z.object({ limit: z.number() }))
  .query(async ({ input, ctx }) => {
    try {
      const invoices = await ctx.prisma.invoiceImportItem.findMany({
        take: input.limit,
      });
      return ok(invoices);
    } catch (error) {
      return err(new Error('Failed to fetch invoices'));
    }
  });

// Client-side handling
if (invoices.data?.isOk()) {
  // Handle success
  console.log(invoices.data.value);
} else if (invoices.data?.isErr()) {
  // Handle error
  console.error(invoices.data.error);
}
```

### 3. Prisma Integration

The tRPC context includes the Prisma client for database operations:

```typescript
// Available in all procedures
const { prisma } = ctx;
const users = await prisma.user.findMany();
```

### 4. Mutations with Optimistic Updates

```typescript
const markAsInvoiceMutation = trpc.invoices.markAsInvoice.useMutation({
  onSuccess: () => {
    // Refetch related queries
    invoices.refetch();
    stats.refetch();
  },
});
```

## Available Routers

### Example Router (`trpc.example.*`)

- `hello` - Simple greeting query
- `getAll` - Example query with neverthrow
- `create` - Example mutation

### Invoices Router (`trpc.invoices.*`)

- `getAll` - Fetch invoices with pagination and filtering
- `getById` - Fetch single invoice with relations
- `markAsInvoice` - Update invoice status
- `getStats` - Get invoice statistics

## Usage Examples

### Basic Query

```typescript
function MyComponent() {
  const hello = trpc.example.hello.useQuery({ text: "tRPC" });
  
  if (hello.isLoading) return <div>Loading...</div>;
  if (hello.error) return <div>Error: {hello.error.message}</div>;
  
  return <div>{hello.data?.greeting}</div>;
}
```

### Mutation with Error Handling

```typescript
function UpdateInvoice({ invoiceId }: { invoiceId: string }) {
  const utils = trpc.useContext();
  const mutation = trpc.invoices.markAsInvoice.useMutation({
    onSuccess: () => {
      utils.invoices.getAll.invalidate();
    },
  });

  const handleUpdate = async () => {
    try {
      const result = await mutation.mutateAsync({
        id: invoiceId,
        isInvoice: true,
      });
      
      if (result.isOk()) {
        console.log('Updated successfully');
      } else {
        console.error('Update failed:', result.error.message);
      }
    } catch (error) {
      console.error('Mutation failed:', error);
    }
  };

  return (
    <button 
      onClick={handleUpdate}
      disabled={mutation.isPending}
    >
      {mutation.isPending ? 'Updating...' : 'Mark as Invoice'}
    </button>
  );
}
```

### Pagination

```typescript
function InvoicesList() {
  const [cursor, setCursor] = useState<string | undefined>();
  
  const invoices = trpc.invoices.getAll.useQuery({
    limit: 10,
    cursor,
  });

  const loadMore = () => {
    if (invoices.data?.isOk() && invoices.data.value.nextCursor) {
      setCursor(invoices.data.value.nextCursor);
    }
  };

  return (
    <div>
      {invoices.data?.isOk() && (
        <>
          {invoices.data.value.items.map(invoice => (
            <div key={invoice.id}>{invoice.text}</div>
          ))}
          {invoices.data.value.nextCursor && (
            <button onClick={loadMore}>Load More</button>
          )}
        </>
      )}
    </div>
  );
}
```

## Adding New Routers

1. Create a new router file in `src/server/routers/`
2. Define your procedures with proper input validation
3. Add the router to `src/server/routers/_app.ts`
4. Use the new procedures in your components

Example:

```typescript
// src/server/routers/users.ts
export const usersRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    // Implementation
  }),
});

// src/server/routers/_app.ts
export const appRouter = createTRPCRouter({
  example: exampleRouter,
  invoices: invoicesRouter,
  users: usersRouter, // Add new router
});
```

## Environment Setup

The tRPC setup automatically handles:
- Development vs production URLs
- SSR configuration
- Error logging in development
- Request batching for performance

## Testing

You can test the tRPC setup by:

1. Starting the development server: `pnpm dev`
2. Visiting `http://localhost:3000` for the basic demo
3. Visiting `http://localhost:3000/invoices` for the invoice management interface
4. Checking the browser console for tRPC logs in development mode

## Next Steps

- Add authentication middleware to `protectedProcedure`
- Implement real-time subscriptions if needed
- Add input/output validation schemas
- Set up automated testing for procedures
