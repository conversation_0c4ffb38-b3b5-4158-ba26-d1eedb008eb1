# Liquidity Pool Actions as Crypto Expenses

This document describes the implementation of liquidity pool actions as crypto expenses, including the unified expense amount field for aggregations across all crypto expense types.

## Overview

Liquidity pool actions (add_liquidity, remove_liquidity, swap) are now tracked as crypto expenses using the `solana.defi.ez_liquidity_pool_actions` table from Flipside. The `token_b_amount_usd` column serves as the expense amount, providing a unified way to track DeFi-related costs.

## Features Implemented

### ✅ **Database Schema Updates**
- **New Expense Type**: Added `LIQUIDITY_POOL` to `CryptoExpenseType` enum
- **LP-Specific Fields**: Added comprehensive liquidity pool data fields
- **Unified Amount Field**: `amountUsd` field for consistent aggregations across all expense types

### ✅ **Flipside Integration**
- **Query Structure**: Uses `solana.defi.ez_liquidity_pool_actions` table
- **Token Filtering**: Filters by `token_a_mint` for specific tokens
- **Wallet Filtering**: Filters by `signer` for company wallets
- **Expense Amount**: Uses `token_b_amount_usd` as the main expense amount

### ✅ **Frontend Integration**
- **Transaction Table**: Shows LP actions with token pairs and EUR amounts
- **Transaction Details**: Comprehensive LP action details with token information
- **Statistics**: LP actions included in crypto expense counts

### ✅ **Import Capabilities**
- **CLI Support**: Import script supports liquidity pool actions
- **Preview Mode**: Preview LP actions before importing
- **Validation**: Validates token mints and wallet addresses

## Implementation Details

### Database Schema

**New Fields in CryptoExpense Model**:
```prisma
// Liquidity Pool details (for LIQUIDITY_POOL type)
lpAction String? // Action type (e.g., "add_liquidity", "remove_liquidity", "swap")
lpPoolAddress String? // Liquidity pool address
lpTokenAMint String? // Token A mint address
lpTokenBMint String? // Token B mint address
lpTokenASymbol String? // Token A symbol
lpTokenBSymbol String? // Token B symbol
lpTokenAAmount Decimal? // Token A amount
lpTokenBAmount Decimal? // Token B amount
lpTokenAAmountUsd Decimal? // Token A amount in USD
lpTokenBAmountUsd Decimal? // Token B amount in USD (main expense amount)

// Common amount field (maps to different sources based on type)
amountUsd Decimal? // Main amount field for display and aggregations
```

### Flipside Query Structure

```sql
SELECT
  token_b_amount_usd as amount_usd,
  block_timestamp,
  tx_id,
  signer,
  action,
  pool_address,
  token_a_mint,
  token_b_mint,
  token_a_symbol,
  token_b_symbol,
  token_a_amount,
  token_b_amount,
  token_a_amount_usd,
  token_b_amount_usd
FROM
  solana.defi.ez_liquidity_pool_actions
WHERE
  token_a_mint IN ('{mint_list}')
  AND signer IN ('{wallet_list}')
ORDER BY
  block_timestamp DESC;
```

### Unified Amount Field Mapping

**Amount Field Logic by Type**:
```typescript
// BUYBACK: swapFromAmountUsd (amount spent to buy tokens)
// FLOOR_SWEEP: priceUsd (amount spent on NFT)
// LIQUIDITY_POOL: token_b_amount_usd (Token B expense amount)
// OTHER: configurable based on use case

const amountUsd = 
  type === "BUYBACK" ? record.swap_from_amount_usd :
  type === "FLOOR_SWEEP" ? record.price_usd :
  type === "LIQUIDITY_POOL" ? record.token_b_amount_usd :
  record.amount_usd; // fallback
```

### Frontend Display Logic

**Transaction Table Description**:
```typescript
if (expense.type === "LIQUIDITY_POOL") {
  return `${expense.type}: ${expense.lpAction} (${expense.lpTokenASymbol}/${expense.lpTokenBSymbol})`;
}
```

**Amount Display**:
```typescript
// Unified amount display across all crypto expense types
const usdAmount = Number(transaction.cryptoExpense.amountUsd || 0);
return currency.formatMonetary(usdAmount, "EUR");
```

## Display Examples

### Transaction Table
```
Type: [Crypto Expense] (purple badge)
Amount: €2,100.75
Description: LIQUIDITY_POOL: add_liquidity (SOL/USDC)
Account: Main Wallet
Date: 2024-01-15
```

### Transaction Details View
```
Crypto Expense Details
├── Type: LIQUIDITY_POOL
├── Amount Spent (EUR): €2,100.75 (red text)
├── Action: add_liquidity
├── Token Pair: SOL/USDC
├── Token A Amount: 10.5 SOL ($2,100.75)
├── Token B Amount: 2,100.75 USDC ($2,100.75)
├── Pool Address: TestPoolAddress1_123456... [copy]
├── Token A Mint: TestTokenAMint1_123456... [copy]
├── Token B Mint: TestTokenBMint1_123456... [copy]
├── Transaction ID: TestLPTx1_1234567890_abc123def [copy] [🔗]
└── Block Timestamp: 7/6/2025, 10:30:45 AM
```

## Liquidity Pool Action Types

### Supported Actions
1. **add_liquidity**: Adding tokens to a liquidity pool
2. **remove_liquidity**: Removing tokens from a liquidity pool  
3. **swap**: Swapping one token for another through a pool

### Token Information
- **Token A**: Primary token (filtered by mint address)
- **Token B**: Secondary token in the pair
- **Amounts**: Both token amounts and USD values
- **Pool Address**: Unique identifier for the liquidity pool

## Configuration

### Token Mints Configuration
```typescript
// src/modules/crypto-expenses/config.ts
export const LIQUIDITY_POOL_TOKEN_MINTS = [
  "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj", // ALL token
  "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB", // PUFF token
  // Add your actual token mint addresses here
];
```

### Import Script Usage
```bash
# Preview liquidity pool actions
pnpm tsx src/scripts/importCryptoExpenses.ts \
  --type liquidity-pools \
  --liquidity-pool-token-mints "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj,G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB" \
  --preview

# Import liquidity pool actions
pnpm tsx src/scripts/importCryptoExpenses.ts \
  --type liquidity-pools \
  --liquidity-pool-token-mints "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj" \
  --from-date 2024-01-01 \
  --to-date 2024-12-31
```

## Unified Expense Amount Field

### Purpose
The `amountUsd` field provides a consistent way to:
- **Aggregate**: Sum all crypto expenses regardless of type
- **Display**: Show amounts consistently in the UI
- **Report**: Generate unified expense reports
- **Analyze**: Perform analytics across all expense types

### Benefits
1. **Simplified Queries**: Single field for all expense amounts
2. **Consistent Display**: Uniform formatting across expense types
3. **Easy Aggregation**: Sum expenses without type-specific logic
4. **Future-Proof**: Easy to add new expense types

### Migration
Existing records are automatically migrated to populate the `amountUsd` field:
- **BUYBACK**: `amountUsd = swapFromAmountUsd`
- **FLOOR_SWEEP**: `amountUsd = priceUsd`
- **LIQUIDITY_POOL**: `amountUsd = token_b_amount_usd`

## Testing

### Test Data Creation
```bash
# Create test liquidity pool expense transactions
pnpm tsx src/scripts/testLiquidityPoolExpenseDisplay.ts

# Clean up test data
pnpm tsx src/scripts/testLiquidityPoolExpenseDisplay.ts --cleanup
```

### Manual Testing Checklist
- [ ] LP expense transactions show purple "Crypto Expense" badge
- [ ] Amount displays as EUR formatted currency
- [ ] Description shows "LIQUIDITY_POOL: action (TOKEN_A/TOKEN_B)" format
- [ ] Transaction details show dedicated LP section
- [ ] Action type displays correctly (add_liquidity, remove_liquidity, swap)
- [ ] Token pair information shows correctly
- [ ] Token amounts show with symbols and USD values
- [ ] Pool address is copyable
- [ ] Token mint addresses are copyable
- [ ] Transaction ID is copyable with Solscan link
- [ ] Statistics include LP expense counts

### Expected Results
1. **Table View**: Clear identification of LP actions with token pairs
2. **Details View**: Comprehensive LP action information
3. **Statistics**: Accurate counts including LP expenses
4. **Aggregation**: Unified expense amounts for reporting

## Data Flow

### Import Process
```
Flipside API → getLiquidityPoolActions() → CryptoExpense → Transaction (optional) → Frontend Display
```

### Amount Calculation
```
token_b_amount_usd (USD) → amountUsd field → Format as EUR → Display €X,XXX.XX
```

### Query Filtering
```
Company Wallets → signer filter
Token Mints → token_a_mint filter
Date Range → block_timestamp filter
```

## Benefits

### Financial Tracking
- **DeFi Costs**: Track costs associated with liquidity provision
- **Token Expenses**: Monitor expenses related to specific tokens
- **Pool Performance**: Understand costs of different pool interactions
- **Unified Reporting**: All crypto expenses in one place

### Operational Benefits
- **Compliance**: Complete record of DeFi activities
- **Tax Reporting**: Detailed transaction history for tax purposes
- **Cost Analysis**: Analyze costs of different DeFi strategies
- **Audit Trail**: Full blockchain verification available

### Technical Benefits
- **Unified Schema**: Consistent data structure across expense types
- **Scalable Design**: Easy to add new DeFi action types
- **Type Safety**: Full TypeScript support throughout
- **Performance**: Efficient queries with proper indexing

## Future Enhancements

### Potential Improvements
1. **More Action Types**: Support for staking, lending, borrowing
2. **Pool Analytics**: Detailed pool performance metrics
3. **Cost Optimization**: Identify most cost-effective pools
4. **Real-time Tracking**: Live monitoring of LP positions

### Integration Opportunities
- **DeFi Protocols**: Direct integration with major DeFi protocols
- **Portfolio Tracking**: Integration with portfolio management tools
- **Tax Software**: Direct export to tax preparation software
- **Analytics Dashboards**: Advanced DeFi analytics and insights

## Summary

The liquidity pool crypto expense implementation provides:

✅ **Complete DeFi Tracking**: Full support for liquidity pool actions
✅ **Unified Amount Field**: Consistent expense amounts across all types
✅ **Comprehensive Data**: Token pairs, amounts, pool addresses, and more
✅ **Easy Aggregation**: Simple queries for total expense calculations
✅ **Scalable Design**: Ready for additional DeFi action types
✅ **Full Integration**: Seamless integration with existing crypto expense system

This enhancement significantly expands the crypto expense tracking capabilities to include DeFi activities while maintaining a unified and consistent data structure for all expense types.
