# AI Chatbot Feature

## Overview

The AI chatbot feature provides an intelligent interface to query and analyze invoice data using natural language. It's built using the AI SDK with tool calling capabilities to fetch real-time data from the database.

## Features

### Available Tools

The chatbot has access to the following tools to fetch invoice statistics:

1. **getInvoiceStats** - Get basic invoice statistics (total count, amounts, VAT info)
2. **getInvoiceStatsByYear** - Get statistics for a specific accounting year
3. **getTopVendorsByCount** - Get top vendors by number of invoices
4. **getTopVendorsByAmount** - Get top vendors by total invoice amount
5. **getAvailableYears** - Get list of years that have invoices
6. **getMonthlyInvoiceSummary** - Get monthly breakdown for a specific year
7. **getInvoiceStatsByCurrency** - Get statistics grouped by currency
8. **searchInvoices** - Search invoices by vendor name or invoice reference
9. **getRecentInvoices** - Get the most recent invoices

### Example Queries

You can ask the chatbot questions like:

- "Show me overall invoice statistics"
- "What are the top 5 vendors by amount?"
- "Get invoice stats for 2024"
- "Show monthly summary for 2024"
- "What currencies do we have invoices in?"
- "Show me recent invoices"
- "Search for invoices from 'Microsoft'"
- "Who are our biggest vendors?"
- "How many invoices do we have with VAT?"

## Technical Implementation

### Backend (API Route)

- **File**: `src/pages/api/chat.ts`
- **Framework**: AI SDK with OpenAI GPT-4o
- **Tools**: Server-side tools that execute database queries using Prisma
- **Features**: Multi-step tool calling, error handling, streaming responses

### Frontend (Chat Interface)

- **File**: `src/pages/chat.tsx`
- **Framework**: React with AI SDK's `useChat` hook
- **UI**: shadcn/ui components with custom tool result rendering
- **Features**: Real-time streaming, tool invocation display, suggestion buttons

### Navigation

The chatbot is accessible via the "AI Assistant" link in the main navigation sidebar.

## Usage

1. Navigate to the AI Assistant page via the sidebar
2. Type your question about invoices in natural language
3. The AI will automatically call the appropriate tools to fetch data
4. Results are displayed in a structured format with visual components
5. Use the suggestion buttons for common queries

## Tool Result Display

Each tool result is displayed with appropriate formatting:

- **Statistics**: Cards with badges showing counts and amounts
- **Vendor Lists**: Structured cards with ranking and details
- **Invoice Lists**: Detailed cards with amounts, dates, and status badges
- **Monthly Data**: Timeline view with counts and totals
- **Currency Data**: Summary cards with totals per currency

## Error Handling

- Database connection errors are handled gracefully
- Invalid queries are processed by the AI and clarified with the user
- Tool execution errors are logged and displayed to the user
- Streaming failures are handled with appropriate fallbacks

## Security

- All database queries use Prisma with proper type safety
- No direct SQL injection vulnerabilities
- Environment variables are properly validated
- API rate limiting through OpenAI's built-in limits

## Future Enhancements

Potential improvements could include:

1. **Chart Generation**: Visual charts for statistical data
2. **Export Functionality**: Export query results to CSV/PDF
3. **Saved Queries**: Save and reuse common queries
4. **Advanced Filters**: More complex filtering options
5. **Audit Trail**: Track chatbot usage and queries
6. **Custom Tools**: Add more specialized analysis tools
7. **Multi-language Support**: Support for different languages
8. **Voice Interface**: Voice input/output capabilities

## Dependencies

- `ai` - AI SDK for React and streaming
- `@ai-sdk/openai` - OpenAI provider for AI SDK
- `@radix-ui/react-scroll-area` - Scroll area component
- OpenAI API key in environment variables

## Configuration

Ensure the following environment variable is set:

```
OPENAI_API_KEY=your_openai_api_key_here
```

The chatbot uses GPT-4o by default but can be configured to use other models by modifying the API route.
