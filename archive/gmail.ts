import fs from "fs";
import PQueue from "p-queue";
import * as R from "remeda";
import pMap from "p-map";
import { measureTime } from "@/utils";
import { google } from "@/modules/integration/googleClient";
import { gmail_v1 } from "googleapis";

// ---- service-account credentials JSON ----

async function fetchMessageAndAttachments(msg: gmail_v1.Schema$Message) {
  const message = await gmail.users.messages.get({
    userId: "me",
    id: msg.id!,
  });

  const revelantAttachments = message.data.payload?.parts?.filter((part) => ["application/pdf", "application/octet-stream"].includes(part.mimeType!));

  const attachments = await pMap(
    revelantAttachments ?? [],
    async (part) => {
      const attachment = await gmail.users.messages.attachments.get({
        userId: "me",
        messageId: msg.id!,
        id: part.body!.attachmentId!,
      });
      return attachment;
    },
    { concurrency: 4 }
  );

  const bodyParts = message.data.payload?.parts?.find((p) => p.mimeType === "multipart/alternative");

  function getPart(parts: gmail_v1.Schema$MessagePart[], mimeType: string) {
    const part = parts.find((p) => p.mimeType === mimeType);
    return part ? Buffer.from(part.body?.data ?? "", "base64url").toString("utf8") : undefined;
  }

  let bodyPlain = getPart(bodyParts?.parts ?? [], "text/plain");
  let bodyHtml = getPart(bodyParts?.parts ?? [], "text/html");

  const bodyText = bodyHtml ?? bodyPlain ?? "";

  const messageParsed = {
    subject: message.data.payload?.headers?.find((h) => h.name === "Subject")?.value ?? "",
    from: message.data.payload?.headers?.find((h) => h.name === "From")?.value ?? "",
    to: message.data.payload?.headers?.find((h) => h.name === "To")?.value ?? "",
    body: {
      bodyText,
      bodyPlain,
      bodyHtml,
    },
  };

  return attachments.map((a) => ({
    attachment: a.data,
    message: { ...message.data, ...messageParsed },
  }));
}

async function getAllMail() {
  // 1️⃣ List every active user in the domain
  const userResp = await google.directory.users.list({ customer: "my_customer", maxResults: 500 });
  const users = userResp.data.users ?? [];

  const allMessagesWithAttachments: Awaited<ReturnType<typeof fetchMessageAndAttachments>> = [];

  const gmail = google.getGmailClient("<EMAIL>");

  await measureTime("fetching messages with attachments", async () => {
    let pageToken: string | undefined;
    do {
      const res = await gmail.users.messages.list({
        userId: "me",
        maxResults: 500,
        pageToken,
        q: "has:attachment",
      });

      const attachments = await pMap(
        res.data.messages ?? [],
        async (msg) => {
          return fetchMessageAndAttachments(msg);
        },
        { concurrency: 4 }
      );

      allMessagesWithAttachments.push(...attachments.flat());
      // <store message IDs, enqueue for fetch, etc.>
      pageToken = res.data.nextPageToken ?? undefined;
    } while (pageToken);

    const uniqueMessageIds = R.uniqueBy(allMessagesWithAttachments, (m) => m.message.id!);
    console.log(`found ${uniqueMessageIds.length} unique messages`);

    console.log(`found ${allMessagesWithAttachments.length} messages with attachments`);

    const emailWithPlainText = R.filter(uniqueMessageIds, (m) => !!m.message.body.bodyPlain);
    const emailWithHtml = R.filter(uniqueMessageIds, (m) => !!m.message.body.bodyHtml);
    const emailWithText = R.filter(uniqueMessageIds, (m) => !!m.message.body.bodyText);

    console.log(`found ${emailWithPlainText.length} messages with plain text`);
    console.log(`found ${emailWithHtml.length} messages with html`);
    console.log(`found ${emailWithText.length} messages with text`);
  });

  // const withAttachments = R.filter(allMessagesWithAttachments, (m) => {
  //   // filter case insensitive if subject or body contain invoice or rechnung
  //   return m.message.payload?.().includes("invoice") || m.message?.data?.toLowerCase().includes("invoice");
  // });

  const debug = 2;
}

getAllMail().catch(console.error);
