{"cells": [{"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ColumnTable [Table: 14 cols x 798 rows] {\n", "  _names: [\n", "    'size',       'data',\n", "    'motlFile',   'buffer',\n", "    'partId',     'mimeType',\n", "    'filename',   'headers',\n", "    'body',       'message',\n", "    'fileType',   'parsedText',\n", "    'pagesCount', 'type'\n", "  ],\n", "  _data: {\n", "    size: Column {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    data: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    },\n", "    motlFile: Column {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    buffer: Column {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    partId: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    },\n", "    mimeType: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    },\n", "    filename: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    },\n", "    headers: Column {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    body: Column {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    message: <PERSON>umn {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    fileType: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    },\n", "    parsedText: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    },\n", "    pagesCount: Column {\n", "      type: [Object],\n", "      length: 798,\n", "      nullCount: 0,\n", "      data: [Array],\n", "      at: [Function (anonymous)],\n", "      offsets: [Int32Array]\n", "    },\n", "    type: {\n", "      length: 798,\n", "      nullCount: 0,\n", "      at: [Function: at],\n", "      key: [Function: key],\n", "      keyFor: [Function: keyFor],\n", "      groups: [Function: groups],\n", "      toArray: [Function: toArray],\n", "      [Symbol(Symbol.iterator)]: [Function: [Symbol.iterator]]\n", "    }\n", "  },\n", "  _total: 798,\n", "  _nrows: 798,\n", "  _mask: null,\n", "  _group: null,\n", "  _order: null,\n", "  _params: undefined,\n", "  _index: null,\n", "  _partitions: null\n", "}\n"]}], "source": [";const aq = require('arquero') as typeof import('arquero');\n", "const fs = require('fs') as typeof import('fs');\n", "const path = require('path') as typeof import('path');\n", "\n", "const dataPath = \"../data/<EMAIL>\";\n", "\n", "// Read the arrow file\n", "const arrowPath = path.join(dataPath , \"attachments.arrow\");\n", "const arrowBuffer = fs.readFileSync(arrowPath)\n", "const datatable = aq.fromArrow(arrowBuffer);\n", "datatable"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ColumnTable [Table: 3 cols x 1 row] {\n", "  _names: [ 'min', 'max', 'avg' ],\n", "  _data: { min: [ 228 ], max: [ 24760 ], avg: [ 1172.6528822055134 ] },\n", "  _total: 1,\n", "  _nrows: 1,\n", "  _mask: null,\n", "  _group: null,\n", "  _order: null,\n", "  _params: undefined,\n", "  _index: null,\n", "  _partitions: null\n", "}\n"]}], "source": ["datatable.derive({textLength: d => d.parsedText.length}).rollup({\n", "    min: aq.op.min(\"textLength\"),\n", "    max: aq.op.max(\"textLength\"),\n", "    avg: aq.op.mean(\"textLength\")\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["const samples = datatable.sample(3)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  'size',       'data',\n", "  'motlFile',   'buffer',\n", "  'partId',     'mimeType',\n", "  'filename',   'headers',\n", "  'body',       'message',\n", "  'fileType',   'parsedText',\n", "  'pagesCount', 'type'\n", "]\n"]}], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["require(\"dotenv\").config()\n", "const openRouterSdk = require('@openrouter/ai-sdk-provider') as typeof import(\"@openrouter/ai-sdk-provider\")\n", "\n", "const openrouter = openRouterSdk.createOpenRouter({\n", "  apiKey: process.env.OPENROUTER_API_KEY ,\n", "});"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?\n"]}], "source": ["const ai = require(\"ai\") as typeof import(\"ai\")\n", "\n", "const textRes = await ai.generateText({\n", "    model: openrouter(\"openai/gpt-4o\"),\n", "    messages: [{\n", "      role: \"user\",\n", "      content: \"Hello world\"\n", "    }]\n", "  });\n", "  \n", "console.log(textRes.text);"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  'IMG_20241116_230622.pdf',\n", "  'MX-2630N_20241001_155030.pdf',\n", "  'Sept. 17, Dok. 1.pdf'\n", "]\n"]}], "source": ["const imagePdfs = fs.readdirSync(path.join(dataPath, \"imagePdfs\"))\n", "imagePdfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["application/pdf\n"]}], "source": ["const motlFileMod = require(\"./modules/core/motlFile\") as typeof import(\"./modules/core/motlFile\");\n", "\n", "const examplePdf = imagePdfs[0]\n", "const pdfPath = path.join(dataPath, \"imagePdfs\", examplePdf)\n", "const motlFile = await motlFileMod.MotlFile.parse(pdfPath);\n", "\n", "await motlFile.getBase64FileUrl()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["APICallError [AI_APICallError]: Invalid JSON response\n", "    at /Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:822:11\n", "    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n", "    ... 4 lines matching cause stack trace ...\n", "    at async _retryWithExponentialBackoff (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:357:12)\n", "    at async fn (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:4314:32)\n", "    at async /Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:556:22\n", "    at async evalmachine.<anonymous>:7:20 {\n", "  cause: _TypeValidationError [AI_TypeValidationError]: Type validation failed: Value: {\"id\":\"gen-**********-8VECVjfQhtrytKzNJoEn\",\"provider\":\"Chutes\",\"model\":\"deepseek/deepseek-chat-v3-0324:free\",\"object\":\"chat.completion\",\"created\":**********,\"choices\":[{\"logprobs\":null,\"finish_reason\":\"stop\",\"native_finish_reason\":\"stop\",\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"The **total invoice value** is **$50.00**, as indicated in the \\\"Total\\\" row under the **Amount** column in the invoice table.  \\n\\n### Extracted Details:\\n- **Invoice No:** ********  \\n- **Date:** 16/11/2024  \\n- **Total Amount:** $50.00  \\n\\nLet me know if you need further parsing or analysis!\",\"refusal\":null,\"reasoning\":null,\"annotations\":[{\"type\":\"file\",\"file\":{\"hash\":\"f674da2c7202104779f64a10d985d3b3df879f9f29d02425549353f9ee0a7779\",\"name\":\"undefined\",\"content\":[{\"type\":\"text\",\"text\":\"<file name=\\\"undefined\\\">\"},{\"type\":\"text\",\"text\":\"Imrad <NAME_EMAIL> Solana A5YXMmzwoi4UwHocB3o7qHyJtvFhtHpjyh8Frb36eKdh\\n\\nBill To Fomo GmbH Mauthnergasse 4/32 1090 Wien UID: ATU78244918\\n\\nInvoice no ******** Date 16/11/2024\\n\\n|  Description | Quantity | Unit Price | Amount  |\\n| --- | --- | --- | --- |\\n|  Meme video project | 1 | $\\\\$ 50.00$ | $\\\\$ 50.00$  |\\n|   |  | Total | $\\\\$ 50.00$  |\"},{\"type\":\"text\",\"text\":\"</file>\"}]}}]}}],\"usage\":{\"prompt_tokens\":184,\"completion_tokens\":82,\"total_tokens\":266,\"prompt_tokens_details\":null}}.\n", "  Error message: [\n", "    {\n", "      \"code\": \"invalid_type\",\n", "      \"expected\": \"object\",\n", "      \"received\": \"null\",\n", "      \"path\": [\n", "        \"usage\",\n", "        \"prompt_tokens_details\"\n", "      ],\n", "      \"message\": \"Expected object, received null\"\n", "    }\n", "  ]\n", "      at _TypeValidationError.wrap (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.js:367:86)\n", "      at safeValidateTypes (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:492:51)\n", "      at safeParseJSON (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:529:30)\n", "      ... 5 lines matching cause stack trace ...\n", "      at async /Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:556:22\n", "      at async _retryWithExponentialBackoff (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:357:12) {\n", "    cause: <PERSON>od<PERSON><PERSON>r: [\n", "      {\n", "        \"code\": \"invalid_type\",\n", "        \"expected\": \"object\",\n", "        \"received\": \"null\",\n", "        \"path\": [\n", "          \"usage\",\n", "          \"prompt_tokens_details\"\n", "        ],\n", "        \"message\": \"Expected object, received null\"\n", "      }\n", "    ]\n", "        at get error (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/zod@3.25.42/node_modules/zod/dist/cjs/v3/types.js:45:31)\n", "        at Object.validate (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:462:101)\n", "        at safeValidateTypes (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:486:31)\n", "        at safeParseJSON (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:529:30)\n", "        at /Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:816:24\n", "        at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n", "        at async postToApi (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.42/node_modules/@ai-sdk/provider-utils/dist/index.js:658:14)\n", "        at async OpenRouterChatLanguageModel.doGenerate (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/@openrouter+ai-sdk-provider@0.7.0_ai@4.3.16_react@19.1.0_zod@3.25.42__zod@3.25.42/node_modules/@openrouter/ai-sdk-provider/dist/index.js:389:50)\n", "        at async fn (/Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:4358:30)\n", "        at async /Users/<USER>/desc/sac-accounting/sac-accounting-app/node_modules/.pnpm/ai@4.3.16_react@19.1.0_zod@3.25.42/node_modules/ai/dist/index.js:556:22 {\n", "      issues: [<PERSON><PERSON><PERSON>],\n", "      addIssue: [Function (anonymous)],\n", "      addIssues: [Function (anonymous)],\n", "      errors: [<PERSON><PERSON><PERSON>]\n", "    },\n", "    value: {\n", "      id: 'gen-**********-8VECVjfQhtrytKzNJoEn',\n", "      provider: 'Chutes',\n", "      model: 'deepseek/deepseek-chat-v3-0324:free',\n", "      object: 'chat.completion',\n", "      created: **********,\n", "      choices: [<PERSON><PERSON><PERSON>],\n", "      usage: [Object]\n", "    },\n", "    [Symbol(vercel.ai.error)]: true,\n", "    [Symbol(vercel.ai.error.AI_TypeValidationError)]: true\n", "  },\n", "  url: 'https://openrouter.ai/api/v1/chat/completions',\n", "  requestBodyValues: {\n", "    model: 'deepseek/deepseek-chat-v3-0324:free',\n", "    models: undefined,\n", "    logit_bias: undefined,\n", "    logprobs: undefined,\n", "    top_logprobs: undefined,\n", "    user: undefined,\n", "    parallel_tool_calls: undefined,\n", "    max_tokens: undefined,\n", "    temperature: 0,\n", "    top_p: undefined,\n", "    frequency_penalty: undefined,\n", "    presence_penalty: undefined,\n", "    seed: undefined,\n", "    stop: undefined,\n", "    response_format: undefined,\n", "    top_k: undefined,\n", "    messages: [ [Object] ],\n", "    include_reasoning: undefined,\n", "    reasoning: undefined,\n", "    usage: undefined,\n", "    tools: undefined,\n", "    tool_choice: undefined\n", "  },\n", "  statusCode: 200,\n", "  responseHeaders: {\n", "    'access-control-allow-origin': '*',\n", "    'cf-ray': '94ad4049c849325b-VIE',\n", "    connection: 'keep-alive',\n", "    'content-encoding': 'br',\n", "    'content-type': 'application/json',\n", "    date: 'Thu, 05 Jun 2025 05:25:48 GMT',\n", "    server: 'cloudflare',\n", "    'transfer-encoding': 'chunked',\n", "    vary: 'Accept-Encoding',\n", "    'x-clerk-auth-message': 'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)',\n", "    'x-clerk-auth-reason': 'token-invalid',\n", "    'x-clerk-auth-status': 'signed-out'\n", "  },\n", "  responseBody: '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '\\n' +\n", "    '         \\n' +\n", "    '{\"id\":\"gen-**********-8VECVjfQhtrytKzNJoEn\",\"provider\":\"Chutes\",\"model\":\"deepseek/deepseek-chat-v3-0324:free\",\"object\":\"chat.completion\",\"created\":**********,\"choices\":[{\"logprobs\":null,\"finish_reason\":\"stop\",\"native_finish_reason\":\"stop\",\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"The **total invoice value** is **$50.00**, as indicated in the \\\\\"Total\\\\\" row under the **Amount** column in the invoice table.  \\\\n\\\\n### Extracted Details:\\\\n- **Invoice No:** ********  \\\\n- **Date:** 16/11/2024  \\\\n- **Total Amount:** $50.00  \\\\n\\\\nLet me know if you need further parsing or analysis!\",\"refusal\":null,\"reasoning\":null,\"annotations\":[{\"type\":\"file\",\"file\":{\"hash\":\"f674da2c7202104779f64a10d985d3b3df879f9f29d02425549353f9ee0a7779\",\"name\":\"undefined\",\"content\":[{\"type\":\"text\",\"text\":\"<file name=\\\\\"undefined\\\\\">\"},{\"type\":\"text\",\"text\":\"Imrad <NAME_EMAIL> Solana A5YXMmzwoi4UwHocB3o7qHyJtvFhtHpjyh8Frb36eKdh\\\\n\\\\nBill To Fomo GmbH Mauthnergasse 4/32 1090 Wien UID: ATU78244918\\\\n\\\\nInvoice no ******** Date 16/11/2024\\\\n\\\\n|  Description | Quantity | Unit Price | Amount  |\\\\n| --- | --- | --- | --- |\\\\n|  Meme video project | 1 | $\\\\\\\\$ 50.00$ | $\\\\\\\\$ 50.00$  |\\\\n|   |  | Total | $\\\\\\\\$ 50.00$  |\"},{\"type\":\"text\",\"text\":\"</file>\"}]}}]}}],\"usage\":{\"prompt_tokens\":184,\"completion_tokens\":82,\"total_tokens\":266,\"prompt_tokens_details\":null}}',\n", "  isRetryable: false,\n", "  data: undefined,\n", "  [Symbol(vercel.ai.error)]: true,\n", "  [Symbol(vercel.ai.error.AI_APICallError)]: true\n", "}\n"]}], "source": ["const deepseekReasoner = \"deepseek/deepseek-r1-0528:free\";\n", "const deepseekV1 = \"deepseek/deepseek-chat-v3-0324:free\";\n", "\n", "const messageRes = await ai.generateText({\n", "    model: openrouter(deepseekV1),\n", "    messages: [\n", "        {\n", "            role: \"user\",\n", "            content: [{\n", "                type: \"text\",\n", "                text: \"can you parse the total invoice value from this invoice.\"\n", "            },\n", "            {\n", "                type: \"file\",\n", "                mimeType: motlFile.mimeType,\n", "                data: motlFile.buffer,\n", "            }\n", "            ]\n", "        },\n", "    ]\n", "  });\n", "  messageRes"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["33:25 - No overload matches this call.\n", "33:25 - Overload 1 of 3, '(body: ChatCompletionCreateParamsNonStreaming, options?: RequestOptions): APIPromise<ChatCompletion>', gave the following error.\n", "33:25 - Type '\"file\"' is not assignable to type '\"refusal\"'.\n", "33:25 - Overload 2 of 3, '(body: ChatCompletionCreateParamsStreaming, options?: RequestOptions): APIPromise<Stream<ChatCompletionChunk>>', gave the following error.\n", "33:25 - Type '\"file\"' is not assignable to type '\"refusal\"'.\n", "33:25 - Overload 3 of 3, '(body: ChatCompletionCreateParamsBase, options?: RequestOptions): APIPromise<ChatCompletion | Stream<ChatCompletionChunk>>', gave the following error.\n", "33:25 - Type '\"file\"' is not assignable to type '\"refusal\"'.\n"]}], "source": ["const openaiMod = require(\"openai\") as typeof import(\"openai\");\n", "const braintrustMod = require(\"braintrust\") as typeof import(\"braintrust\");\n", "\n", "const openai = braintrustMod.wrapOpenAI(\n", "    new openaiMod.OpenAI({\n", "      apiKey: process.env.OPENAI_API_KEY,\n", "    }),\n", "    \n", ");\n", "\n", "const logger = braintrustMod.initLogger({\n", "    projectName: \"Invoice Parsing\",\n", "    apiKey: process.env.BRAINTRUST_API_KEY,\n", "});\n", "\n", "\n", "const messageRes = await logger.traced(async () => {\n", "\n", "    for(const sample of samples.objects() as any[]) {\n", "\n", "        console.log(\"sample\",sample)\n", "\n", "        const res = await openai.chat.completions.create({\n", "            model: \"gpt-4o\",\n", "            messages: [\n", "                {\n", "                    role: \"user\",\n", "                    content: [{\n", "                        type: \"text\",\n", "                        text: \"can you parse the total invoice value from this invoice.\"\n", "                    },\n", "                    {\n", "                        type: \"file\",\n", "                        mimeType: sample.motlFile.mimeType,\n", "                        data: sample.motlFile.buffer,\n", "                    }\n", "                    ]\n", "                },\n", "            ]\n", "        });\n", "    }\n", "});\n", "messageRes"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["1:12 - Property 'reasoning' does not exist on type 'ChatCompletion & { _request_id?: string; }'.\n"]}], "source": ["messageRes.reasoning.split(\" \").length"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}