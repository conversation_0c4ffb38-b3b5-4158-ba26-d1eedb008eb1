{"cells": [{"language": "typescript", "source": ["const aq = require('arquero') as typeof import('arquero');\nconst fs = require('fs') as typeof import('fs');\nconst path = require('path') as typeof import('path');\n\nconst dataPath = \"../data/<EMAIL>\";\n\n// Read the arrow file\nconst arrowPath = path.join(dataPath , \"attachments.arrow\");\nconst arrowBuffer = fs.readFileSync(arrowPath)\nconst datatable = aq.fromArrow(arrowBuffer);\ndatatable"], "outputs": []}]}