{"cells": [{"cell_type": "code", "execution_count": null, "id": "3e024d4f", "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["const aq = require('arquero') as typeof import('arquero');\n", "const fs = require('fs') as typeof import('fs');\n", "const path = require('path') as typeof import('path');\n", "\n", "const dataPath = \"../data/<EMAIL>\";\n", "\n", "// Read the arrow file\n", "const arrowPath = path.join(dataPath , \"attachments.arrow\");\n", "const arrowBuffer = fs.readFileSync(arrowPath)\n", "const datatable = aq.fromArrow(arrowBuffer);\n", "datatable"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}