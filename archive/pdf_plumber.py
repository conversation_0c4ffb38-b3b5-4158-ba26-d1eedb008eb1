from typing import Optional
from pydantic import BaseModel
import json
import pdfplumber
import sys


def parse_pdf(file_path: str):
    content = ""
    try:
        # Read the PDF file
        with pdfplumber.open(file_path) as pdf:
            texts = []
            for page in pdf.pages[:4]:  # Limit to first 4 pages
                # Extract text from each page
                filtered = page.dedupe_chars().filter(lambda obj: obj.get("text") != " ")
                texts.append(filtered.extract_text(layout=False, x_tolerance=1.5))
            content = "\n\n".join(texts)
    except Exception as e:
        return {"error": str(e), "status_code": 500}

    return {"content": content}


if __name__ == "__main__":
    res = parse_pdf(
        sys.argv[1]
    )
    print("res", json.dumps(res, indent=2))