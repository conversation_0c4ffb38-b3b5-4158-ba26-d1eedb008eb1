import { GenericFile, MotlFile } from "../src/modules/core/motlFile";
import { promisify } from "util";
import { exec as execOriginal } from "child_process";
import path from "path";
import fs from "fs/promises";
import { findAndParseJson } from "../src/modules/core/utils/jsonUtils";
import PQueue from "p-queue";

const exec = promisify(execOriginal);

export type RawDocument = {
  name: string;
  file: MotlFile;
};

function removeNullBytes(input: string): string {
  // Replace null byte (0x00) with an empty string
  return input.replace(/\0/g, "");
}

const pdfPlumberQueue = new PQueue({
  concurrency: 10,
});

export namespace pdfPlumber {
  export async function parse(genericFile: GenericFile) {
    const file = await MotlFile.parse(genericFile);
    try {
      // const formData = new FormData();
      // const blob = await file.toBlob();
      // formData.append("pdf_file", blob, file.fileName);

      const scriptPath = path.join(__dirname, "pdf_plumber.py");
      if (!(await fs.stat(scriptPath)).isFile()) throw new Error("pdf_plumber.py script not found");

      const filePath = await file.getTempFilePath();

      // call python pdf_plumber.py and return the content
      const res = await pdfPlumberQueue.add(() => exec(`python3 ${scriptPath} ${filePath}`)).then((res) => res!);

      await fs.rm(filePath, { force: true });

      const parsed = findAndParseJson<{ content: string }>(res.stdout);

      if (!parsed?.parsed) throw new Error("Could not parse pdf");

      if (parsed.parsed.content === "") {
        return null;
      }

      const utf8 = removeNullBytes(parsed.parsed.content);

      return utf8;
    } catch (e) {
      console.error(`error parsing ${file.fileName}, `, e);
      throw e;
    }
  }
}

async function test() {
  const dataPath = path.join(__dirname, "data", "slack_invoice_SBIE-8692888.pdf-attachment.pdf");

  const res = await pdfPlumber.parse(await MotlFile.parse(dataPath));

  console.log(res);
}

if (require.main === module && module.filename.endsWith(".ts")) {
  test();
}
