{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "src/modules/ai/llm", ".next/types/**/*.ts"], "exclude": ["node_modules", "archive", "src/modules/integration/gmailLfi.ts", "src/modules/invoices/playground", "src/modules/invoices/detectInvoices.ts", "src/modules/extractInvoice.ts", "src/playground", "**/*.test.*", "**/test*", "**/*cli*", "**/*<PERSON><PERSON>*", "src/scripts"]}