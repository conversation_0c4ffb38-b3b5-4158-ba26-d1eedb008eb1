import { PrismaClient } from "../src/prisma/generated";
import { spawn, exec } from "child_process";
import { promisify } from "util";
import * as fs from "fs/promises";
import * as path from "path";
import { envVars } from "../src/envVars";

const execAsync = promisify(exec);

interface MigrationConfig {
  sourceDbUrl: string;
  targetDbUrl: string;
  backupFile?: string;
  skipVerification?: boolean;
}

async function migrateToLocalPostgres(config: MigrationConfig) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-").split("T")[0];
  const backupFile = config.backupFile || `backups/neon-backup-${timestamp}.sql`;

  console.log("🚀 Starting PostgreSQL migration from Neon to Local...");
  console.log(`📊 Source: ${config.sourceDbUrl.replace(/\/\/.*@/, "//***@")}`);
  console.log(`📊 Target: ${config.targetDbUrl.replace(/\/\/.*@/, "//***@")}`);

  try {
    // Ensure backups directory exists
    await fs.mkdir("backups", { recursive: true });

    // Step 1: Check if Docker container is running
    console.log("🐳 Checking Docker container status...");
    // await checkDockerContainer();

    // Step 2: Create backup from Neon database
    console.log("📦 Creating database backup from Neon...");
    await createBackup(config.sourceDbUrl, backupFile);
    console.log(`✅ Backup created: ${backupFile}`);

    // Step 3: Prepare local database
    console.log("🔧 Preparing local database...");
    await prepareLocalDatabase(config.targetDbUrl);

    // Step 4: Restore to local database
    console.log("🔄 Restoring data to local database...");
    await restoreBackup(config.targetDbUrl, backupFile);
    console.log("✅ Data restored successfully");

    // Step 5: Run Prisma migrations to ensure schema is up to date
    console.log("🔧 Running Prisma migrations...");
    process.env.DATABASE_URL = config.targetDbUrl;

    // Step 6: Generate Prisma client
    console.log("🔧 Generating Prisma client...");
    await generatePrismaClient();

    // Step 7: Verify data integrity
    if (!config.skipVerification) {
      console.log("🔍 Verifying data integrity...");
      await verifyMigration(config.sourceDbUrl, config.targetDbUrl);
    }

    console.log("🎉 Migration completed successfully!");
    console.log(`📁 Backup file: ${backupFile}`);
    console.log(`🔗 Your local database is ready!`);
    console.log(`   DATABASE_URL="${config.targetDbUrl}"`);
    console.log(`   DATABASE_URL_NEON="${config.sourceDbUrl}"`);
  } catch (error) {
    console.error("❌ Migration failed:", error);
    console.log("🔧 Troubleshooting tips:");
    console.log("   1. Ensure local PostgreSQL is running: npm run db:start");
    console.log("   2. Check backup file exists and is valid");
    console.log('   3. Try manual restore: psql "your-local-url" < backups/neon-backup-*.sql');
    console.log(`   4. Check backup file: ${backupFile}`);
    throw error;
  }
}

async function checkDockerContainer() {
  try {
    const { stdout } = await execAsync("docker-compose ps postgres");
    if (!stdout.includes("Up")) {
      console.log("🐳 Starting Docker container...");
      await execAsync("docker-compose up -d postgres");

      // Wait for container to be ready
      console.log("⏳ Waiting for PostgreSQL to be ready...");
      let retries = 30;
      while (retries > 0) {
        try {
          await execAsync("docker-compose exec postgres pg_isready -U sac_user -d sac_accounting");
          break;
        } catch {
          retries--;
          if (retries === 0) throw new Error("PostgreSQL container failed to start");
          await new Promise((resolve) => setTimeout(resolve, 2000));
          process.stdout.write(".");
        }
      }
      console.log("\n✅ PostgreSQL container is ready");
    } else {
      console.log("✅ PostgreSQL container is already running");
    }
  } catch (error) {
    throw new Error(`Failed to start Docker container: ${error}`);
  }
}

async function filterNeonSpecificConfig(backupFile: string) {
  try {
    const content = await fs.readFile(backupFile, "utf8");

    // Filter out Neon-specific configuration parameters and problematic statements
    const filteredContent = content
      .split("\n")
      .filter((line) => {
        const trimmedLine = line.trim();

        // Skip empty lines and comments
        if (!trimmedLine || trimmedLine.startsWith("--")) {
          return true;
        }

        // Remove Neon-specific SET statements
        const neonSpecificParams = [
          "transaction_timeout",
          "neon.max_response_size",
          "neon.storage_timeout",
          "neon.query_timeout",
          "neon.page_cache_size",
          "shared_preload_libraries",
          "pg_stat_statements",
          "neon",
        ];

        // Check if line contains any Neon-specific parameters
        const hasNeonParam = neonSpecificParams.some(
          (param) =>
            trimmedLine.toLowerCase().includes(`set ${param.toLowerCase()}`) ||
            trimmedLine.toLowerCase().includes(`select pg_catalog.set_config('${param.toLowerCase()}`)
        );

        return !hasNeonParam;
      })
      .join("\n");

    await fs.writeFile(backupFile, filteredContent, "utf8");
    console.log("   🧹 Filtered Neon-specific configuration");
  } catch (error) {
    console.log("   ⚠️  Could not filter backup file:", error);
    // Don't fail the migration for this
  }
}

async function createBackup(sourceDbUrl: string, backupFile: string) {
  return new Promise<void>((resolve, reject) => {
    const args = [
      sourceDbUrl,
      "-f",
      backupFile,
      "--verbose",
      "--no-acl",
      "--no-owner",
      "--clean",
      "--if-exists",
      "--no-privileges",
      "--exclude-table-data=pg_stat_statements", // Exclude Neon-specific stats
    ];

    const child = spawn("pg_dump", args, {
      stdio: ["pipe", "pipe", "pipe"],
    });

    let stderr = "";
    child.stderr.on("data", (data) => {
      stderr += data.toString();
      // pg_dump writes progress to stderr, so we show it
      process.stdout.write(".");
    });

    child.stdout.on("data", (data) => {
      // Optional: log stdout if needed
    });

    child.on("close", (code) => {
      console.log(); // New line after dots
      if (code === 0) {
        // Filter out Neon-specific configuration from the backup file
        filterNeonSpecificConfig(backupFile).then(resolve).catch(reject);
      } else {
        reject(new Error(`pg_dump failed with code ${code}: ${stderr}`));
      }
    });

    child.on("error", (error) => {
      reject(new Error(`pg_dump error: ${error.message}`));
    });
  });
}

async function prepareLocalDatabase(targetDbUrl: string) {
  // Extract database name from URL
  const url = new URL(targetDbUrl);
  const dbName = url.pathname.slice(1);
  const baseUrl = `${url.protocol}//${url.username}:${url.password}@${url.host}/postgres`;

  // Quote database name to handle special characters like hyphens
  const quotedDbName = `"${dbName}"`;

  try {
    // Drop and recreate database to ensure clean state
    console.log(`🗑️  Dropping existing database: ${dbName}`);
    await execAsync(`psql "${baseUrl}" -c "DROP DATABASE IF EXISTS ${quotedDbName};"`);

    console.log(`🆕 Creating fresh database: ${dbName}`);
    await execAsync(`psql "${baseUrl}" -c "CREATE DATABASE ${quotedDbName};"`);
  } catch (error) {
    console.log(`⚠️  Could not recreate database (this is often normal): ${error}`);
  }
}

async function restoreBackup(targetDbUrl: string, backupFile: string) {
  return new Promise<void>((resolve, reject) => {
    const args = [
      targetDbUrl,
      "-f",
      backupFile,
      "-v",
      "ON_ERROR_STOP=1",
      "--single-transaction",
      "--no-password", // Don't prompt for password
    ];

    const child = spawn("psql", args, {
      stdio: ["pipe", "pipe", "pipe"],
      env: { ...process.env, PGPASSWORD: "" }, // Ensure no password prompt
    });

    let stderr = "";
    let stdout = "";

    child.stderr.on("data", (data) => {
      const output = data.toString();
      stderr += output;

      // Show progress but filter out common warnings
      if (!output.includes("NOTICE:") && !output.includes("WARNING:")) {
        process.stdout.write(".");
      }
    });

    child.stdout.on("data", (data) => {
      stdout += data.toString();
    });

    child.on("close", (code) => {
      console.log(); // New line after dots
      if (code === 0) {
        resolve();
      } else {
        console.log("   📋 Restore stderr output:");
        console.log("   " + stderr.split("\n").slice(-10).join("\n   ")); // Show last 10 lines
        reject(new Error(`psql restore failed with code ${code}: ${stderr.split("\n").slice(-3).join(" ")}`));
      }
    });

    child.on("error", (error) => {
      reject(new Error(`psql error: ${error.message}`));
    });
  });
}

async function runPrismaMigrations() {
  try {
    const { stdout, stderr } = await execAsync("npx prisma migrate deploy");
    console.log("✅ Prisma migrations completed");
    if (stderr) console.log("Migration warnings:", stderr);
  } catch (error) {
    console.error("⚠️  Prisma migration failed:", error);
    throw error;
  }
}

async function generatePrismaClient() {
  try {
    const { stdout } = await execAsync("npx prisma generate");
    console.log("✅ Prisma client generated");
  } catch (error) {
    console.error("⚠️  Prisma client generation failed:", error);
    throw error;
  }
}

async function verifyMigration(sourceDbUrl: string, targetDbUrl: string) {
  const sourcePrisma = new PrismaClient({ datasourceUrl: sourceDbUrl });
  const targetPrisma = new PrismaClient({ datasourceUrl: targetDbUrl });

  try {
    // Count records in key tables
    const tables = ["vendor", "invoice", "invoiceImportItem", "transaction", "transfer", "trade", "cryptoExpense", "account", "reconciliation"];

    console.log("📊 Verifying record counts:");
    let totalMismatch = 0;

    for (const table of tables) {
      try {
        const sourceCount = await (sourcePrisma as any)[table].count();
        const targetCount = await (targetPrisma as any)[table].count();

        const status = sourceCount === targetCount ? "✅" : "❌";
        console.log(`   ${status} ${table}: ${sourceCount} → ${targetCount}`);

        if (sourceCount !== targetCount) {
          totalMismatch++;
          console.log(`     ⚠️  Mismatch detected!`);
        }
      } catch (error) {
        console.log(`   ⚠️  ${table}: Could not verify (table may not exist)`);
      }
    }

    if (totalMismatch > 0) {
      throw new Error(`❌ Found ${totalMismatch} table(s) with record count mismatches`);
    }

    console.log("✅ All data integrity checks passed!");
  } finally {
    await sourcePrisma.$disconnect();
    await targetPrisma.$disconnect();
  }
}

// CLI Interface
async function main() {
  try {
    // Check for required environment variables
    if (!envVars.DATABASE_URL_NEON) {
      console.error("❌ DATABASE_URL_NEON environment variable is required");
      console.log("   Add your Neon database URL as DATABASE_URL_NEON in .env");
      process.exit(1);
    }

    const config: MigrationConfig = {
      sourceDbUrl: envVars.DATABASE_URL_NEON,
      targetDbUrl: envVars.DATABASE_URL,
      skipVerification: process.argv.includes("--skip-verification"),
    };

    console.log("🏗️  SAC Accounting - Neon to Local PostgreSQL Migration");
    console.log("=".repeat(60));
    console.log("📋 This migration will:");
    console.log("   • Create a backup of your Neon database");
    console.log("   • Filter out Neon-specific configuration");
    console.log("   • Restore data to local PostgreSQL");
    console.log("   • Run Prisma migrations");
    console.log("   • Verify data integrity");
    console.log();

    await migrateToLocalPostgres(config);

    console.log("=".repeat(60));
    console.log("🎯 Next steps:");
    console.log("   1. Your .env DATABASE_URL is already configured for local PostgreSQL");
    console.log("   2. Restart your development server: npm run dev");
    console.log("   3. Test database connection:");
    console.log(`      psql "${config.targetDbUrl}" -c "SELECT COUNT(*) FROM vendor;"`);
    console.log("   4. Enjoy blazing fast local database performance! 🚀");
  } catch (error) {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { migrateToLocalPostgres };
