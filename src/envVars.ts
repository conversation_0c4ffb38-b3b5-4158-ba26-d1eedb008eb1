import "dotenv/config";
import { z } from "zod";

export const envSchema = z.object({
  MISTRAL_API_KEY: z.string(),
  S3_ENDPOINT: z.string(),
  S3_ACCESS_ID: z.string(),
  S3_ACCESS_KEY: z.string(),
  DATABASE_URL: z.string(),
  DATABASE_URL_NEON: z.string().optional(), // Neon cloud database for backup/sync
  OPENAI_API_KEY: z.string(),
  BRAINTRUST_API_KEY: z.string(),
  OPENROUTER_API_KEY: z.string(),
  GEMINI_API_KEY: z.string(),
  MOTL_CACHER_URL: z.string().default("https://motl-cacher.matt-22b.workers.dev"),
  MOTL_CACHER_API_KEY: z.string(),
  FLIPSIDE_API_KEY: z.string(),
  // HTTP Auth credentials for Vercel deployment
  HTTP_AUTH_USERNAME: z.string(),
  HTTP_AUTH_PASSWORD: z.string(),
  CRYPTO_COMPARE_API_KEY: z.string(),
});

export const envVars = envSchema.parse(process.env);
