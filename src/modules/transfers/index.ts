/**
 * Transfer Analytics Module
 * 
 * This module provides utilities for analyzing and aggregating transfer data
 * from the database, particularly for understanding counterparty relationships
 * and token flow patterns.
 */

export { TransferAnalytics } from './transferAnalytics';
export type { 
  CounterpartyTransferSummary, 
  TransferAnalyticsOptions 
} from './transferAnalytics';

/**
 * Usage examples and common patterns
 */
export namespace TransferAnalyticsExamples {
  
  /**
   * Example: Basic counterparty analysis
   */
  export const basicAnalysis = `
    import { TransferAnalytics } from '@/modules/transfers';
    import { PrismaClient } from '@/prisma/generated';
    
    const prisma = new PrismaClient();
    const analytics = new TransferAnalytics(prisma);
    
    // Get top counterparties by transfer count
    const topCounterparties = await analytics.getTopCounterpartiesByCount(10);
    
    console.log('Top counterparties:');
    topCounterparties.forEach((cp, index) => {
      console.log(\`\${index + 1}. \${cp.counterparty}: \${cp.totalTransfers} transfers\`);
      
      Object.entries(cp.tokens).forEach(([token, data]) => {
        console.log(\`   \${token}: \${data.count} transfers, \${data.totalAmount} total\`);
      });
    });
  `;

  /**
   * Example: Token-specific analysis
   */
  export const tokenAnalysis = `
    // Get top USDC recipients
    const topUsdcRecipients = await analytics.getTopCounterpartiesByTokenAmount('USDC', 10);
    
    console.log('Top USDC recipients:');
    topUsdcRecipients.forEach((cp, index) => {
      const usdcData = cp.tokens['USDC'];
      console.log(\`\${index + 1}. \${cp.counterparty}: \${usdcData.totalAmount} USDC in \${usdcData.count} transfers\`);
    });
  `;

  /**
   * Example: Wallet-specific analysis
   */
  export const walletAnalysis = `
    // Analyze transfers from a specific wallet
    const walletAddress = 'your-wallet-address-here';
    const walletSummary = await analytics.getWalletTransferSummary(walletAddress, {
      fromDate: new Date('2024-01-01'),
      toDate: new Date('2024-12-31')
    });
    
    console.log(\`Wallet \${walletAddress} sent transfers to \${walletSummary.length} counterparties\`);
  `;

  /**
   * Example: Export to CSV
   */
  export const csvExport = `
    // Export counterparty data to CSV
    const csvData = await analytics.exportCounterpartyDataToCsv({
      fromDate: new Date('2024-01-01'),
      minTransferCount: 5
    });
    
    // Save to file
    import fs from 'fs';
    fs.writeFileSync('counterparty-analysis.csv', csvData);
  `;

  /**
   * Example: Overall statistics
   */
  export const overallStats = `
    // Get overall transfer statistics
    const stats = await analytics.getOverallTransferStats({
      fromDate: new Date('2024-01-01')
    });
    
    console.log(\`Total transfers: \${stats.totalTransfers}\`);
    console.log(\`Unique counterparties: \${stats.uniqueCounterparties}\`);
    console.log('Token breakdown:');
    
    Object.entries(stats.tokenBreakdown).forEach(([token, data]) => {
      console.log(\`  \${token}: \${data.count} transfers, \${data.totalAmount} total, \${data.uniqueCounterparties} counterparties\`);
    });
  `;
}

/**
 * Configuration and setup information
 */
export namespace TransferAnalyticsConfig {
  export const SUPPORTED_TOKENS = ['USDC', 'USDT', 'SOL'] as const;
  
  export const DEFAULT_OPTIONS = {
    minTransferCount: 1,
    limit: 10
  };
  
  export const CSV_HEADERS = [
    'Counterparty',
    'Total Transfers', 
    'USDC Count',
    'USDC Total',
    'USDT Count', 
    'USDT Total',
    'SOL Count',
    'SOL Total',
    'First Transfer',
    'Last Transfer'
  ] as const;
}
