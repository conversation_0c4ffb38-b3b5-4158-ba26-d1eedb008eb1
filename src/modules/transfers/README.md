# Transfer Analytics Module

This module provides comprehensive analytics and aggregation capabilities for analyzing transfer data from company wallets. It helps you understand counterparty relationships, token flow patterns, and transfer statistics.

## Features

- 🔍 **Counterparty Analysis**: Group transfers by recipient and analyze patterns
- 💰 **Token Aggregation**: Aggregate amounts by token type (USDC, USDT, SOL)
- 📊 **Statistical Analysis**: Generate comprehensive transfer statistics
- 📄 **Export Capabilities**: Export data to CSV and JSON formats
- 🎯 **Filtering Options**: Filter by date range, wallet addresses, and token types
- 📈 **Top Recipients**: Find top recipients by transfer count or amount
- 🔄 **Self-Transfer Detection**: Automatically identify transfers between your own accounts
- 🏢 **Account Linking**: Link counterparties to known accounts for better insights

## Quick Start

### 1. Import Transfers from Flipside

First, import transfer data using the crypto expenses script:

```bash
# Preview transfers from all company wallets
npm run script:import-crypto-expenses -- --type transfers --preview

# Import transfers for a specific date range
npm run script:import-crypto-expenses -- --type transfers --from-date 2024-01-01 --to-date 2024-12-31

# Import transfers from specific wallets
npm run script:import-crypto-expenses -- --type transfers --company-wallets "wallet1,wallet2"
```

### 2. Analyze Transfer Data

Use the analytics module to analyze the imported data:

```typescript
import { TransferAnalytics } from '@/modules/transfers';
import { PrismaClient } from '@/prisma/generated';

const prisma = new PrismaClient();
const analytics = new TransferAnalytics(prisma);

// Get top counterparties by transfer count
const topCounterparties = await analytics.getTopCounterpartiesByCount(10);

console.log('Top counterparties:');
topCounterparties.forEach((cp, index) => {
  console.log(`${index + 1}. ${cp.counterparty}: ${cp.totalTransfers} transfers`);
  
  Object.entries(cp.tokens).forEach(([token, data]) => {
    console.log(`   ${token}: ${data.count} transfers, ${data.totalAmount} total`);
  });
});
```

### 3. Use CLI Tools

Use the command-line tools for quick analysis:

```bash
# Analyze counterparty patterns
npm run script:analyze-transfers counterparties --limit 20

# Generate overall statistics
npm run script:analyze-transfers stats

# Find top USDC recipients
npm run script:analyze-transfers top-recipients --tokens USDC --limit 10

# Analyze self-transfers vs external transfers
npm run script:analyze-transfers transfer-types

# Export to CSV
npm run script:analyze-transfers counterparties --format csv --output counterparties.csv
```

## API Reference

### TransferAnalytics Class

#### `getCounterpartyAggregation(options)`

Get aggregated transfer data grouped by counterparty.

**Parameters:**
- `options.walletAddresses?: string[]` - Filter by specific wallet addresses
- `options.fromDate?: Date` - Start date filter
- `options.toDate?: Date` - End date filter
- `options.minTransferCount?: number` - Minimum transfers to include counterparty
- `options.tokens?: string[]` - Filter by specific tokens

**Returns:** `CounterpartyTransferSummary[]`

#### `getTopCounterpartiesByCount(limit, options)`

Get top counterparties by transfer count.

#### `getTopCounterpartiesByTokenAmount(token, limit, options)`

Get top counterparties by total amount for a specific token.

#### `getOverallTransferStats(options)`

Get comprehensive transfer statistics.

#### `getSelfTransfers(options)`

Get only transfers between your own accounts (self-transfers).

#### `getExternalTransfers(options)`

Get only transfers to external counterparties.

#### `getTransferTypeBreakdown(options)`

Get a breakdown of self-transfers vs external transfers with statistics.

#### `exportCounterpartyDataToCsv(options)`

Export counterparty data to CSV format (includes self-transfer indicators).

### Data Types

```typescript
interface CounterpartyTransferSummary {
  counterparty: string;
  totalTransfers: number;
  tokens: {
    [token: string]: {
      count: number;
      totalAmount: Decimal;
      averageAmount: Decimal;
    };
  };
  firstTransferDate: Date;
  lastTransferDate: Date;
  isSelfTransfer: boolean;
  counterpartyAccountName?: string;
}
```

## CLI Commands

### Analyze Counterparties

```bash
npm run script:analyze-transfers counterparties [options]
```

**Options:**
- `--wallets` - Comma-separated wallet addresses
- `--from-date` - Start date (YYYY-MM-DD)
- `--to-date` - End date (YYYY-MM-DD)
- `--min-transfers` - Minimum transfer count (default: 1)
- `--tokens` - Comma-separated token list
- `--limit` - Maximum results (default: 10)
- `--format` - Output format: console, csv, json
- `--output` - Output file path

### Generate Statistics

```bash
npm run script:analyze-transfers stats [options]
```

### Find Top Recipients

```bash
npm run script:analyze-transfers top-recipients [options]
```

### Analyze Transfer Types

```bash
npm run script:analyze-transfers transfer-types [options]
```

Analyzes the breakdown between self-transfers (between your own accounts) and external transfers.

## Examples

### Basic Counterparty Analysis

```typescript
const analytics = new TransferAnalytics(prisma);

// Get all counterparties with at least 5 transfers
const counterparties = await analytics.getCounterpartyAggregation({
  minTransferCount: 5
});

counterparties.forEach(cp => {
  console.log(`${cp.counterparty}: ${cp.totalTransfers} transfers`);
  
  // Show USDC transfers
  if (cp.tokens.USDC) {
    console.log(`  USDC: ${cp.tokens.USDC.totalAmount} in ${cp.tokens.USDC.count} transfers`);
  }
});
```

### Token-Specific Analysis

```typescript
// Find top USDC recipients
const topUsdcRecipients = await analytics.getTopCounterpartiesByTokenAmount('USDC', 10);

console.log('Top USDC recipients:');
topUsdcRecipients.forEach((cp, index) => {
  const usdcData = cp.tokens['USDC'];
  console.log(`${index + 1}. ${cp.counterparty}: ${usdcData.totalAmount} USDC`);
});
```

### Date Range Analysis

```typescript
// Analyze transfers from last 30 days
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

const recentTransfers = await analytics.getCounterpartyAggregation({
  fromDate: thirtyDaysAgo,
  minTransferCount: 2
});
```

### Self-Transfer Analysis

```typescript
// Get only self-transfers
const selfTransfers = await analytics.getSelfTransfers({
  fromDate: new Date('2024-01-01')
});

console.log('Self-transfers:');
selfTransfers.forEach(transfer => {
  console.log(`${transfer.counterpartyAccountName}: ${transfer.totalTransfers} transfers`);
});

// Get transfer type breakdown
const breakdown = await analytics.getTransferTypeBreakdown();
console.log(`Self-transfers: ${breakdown.selfTransfers.count}`);
console.log(`External transfers: ${breakdown.externalTransfers.count}`);
```

### Export Data

```typescript
// Export to CSV (includes self-transfer indicators)
const csvData = await analytics.exportCounterpartyDataToCsv({
  fromDate: new Date('2024-01-01'),
  minTransferCount: 5
});

// Save to file
import fs from 'fs';
fs.writeFileSync('transfer-analysis.csv', csvData);
```

## Configuration

### Supported Tokens

The module supports analysis of these tokens:
- USDC
- USDT  
- SOL

### Default Settings

```typescript
const DEFAULT_OPTIONS = {
  minTransferCount: 1,
  limit: 10
};
```

## Integration with Flipside Data

This module works with transfer data imported from Flipside Crypto using the `getAllNativeTransfers` function. The import process:

1. Queries Solana transfer data from Flipside
2. Filters for outbound transfers from company wallets
3. Excludes DEX swap transactions
4. Stores transfers in the database with proper account relationships

## Performance Considerations

- Use date range filters for large datasets
- Set appropriate `minTransferCount` to reduce noise
- Consider pagination for very large result sets
- Database indexes on `counterparty` and `currencyCode` improve query performance

## Troubleshooting

### No Data Found

1. Ensure transfers have been imported using the import script
2. Check that company wallets are configured correctly
3. Verify date ranges include actual transfer activity

### Performance Issues

1. Add date range filters to limit query scope
2. Increase `minTransferCount` to reduce result set size
3. Consider adding database indexes if needed
