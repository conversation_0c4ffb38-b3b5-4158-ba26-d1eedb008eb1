import { PrismaClient } from "@/prisma/generated";
import { Decimal } from "@prisma/client/runtime/library";

/**
 * Transfer analytics and aggregation utilities
 */

export interface CounterpartyTransferSummary {
  counterparty: string;
  totalTransfers: number;
  tokens: {
    [token: string]: {
      count: number;
      totalAmount: Decimal;
      averageAmount: Decimal;
    };
  };
  totalUsdValue?: Decimal;
  firstTransferDate: Date;
  lastTransferDate: Date;
  isSelfTransfer: boolean;
  counterpartyAccountName?: string;
}

export interface TransferAnalyticsOptions {
  walletAddresses?: string[];
  fromDate?: Date;
  toDate?: Date;
  minTransferCount?: number;
  tokens?: string[];
}

export class TransferAnalytics {
  constructor(private prisma: PrismaClient) {}

  /**
   * Get aggregated transfer data grouped by counterparty
   */
  async getCounterpartyAggregation(
    options: TransferAnalyticsOptions = {}
  ): Promise<CounterpartyTransferSummary[]> {
    const {
      walletAddresses,
      fromDate,
      toDate,
      minTransferCount = 1,
      tokens
    } = options;

    // Build the where clause
    const whereClause: any = {};

    if (walletAddresses && walletAddresses.length > 0) {
      whereClause.transaction = {
        account: {
          publicKey: {
            in: walletAddresses
          }
        }
      };
    }

    if (fromDate || toDate) {
      whereClause.transaction = {
        ...whereClause.transaction,
        executedAt: {}
      };
      
      if (fromDate) {
        whereClause.transaction.executedAt.gte = fromDate;
      }
      
      if (toDate) {
        whereClause.transaction.executedAt.lte = toDate;
      }
    }

    if (tokens && tokens.length > 0) {
      whereClause.currencyCode = {
        in: tokens
      };
    }

    // Get all transfers matching the criteria
    const transfers = await this.prisma.transfer.findMany({
      where: whereClause,
      include: {
        transaction: {
          include: {
            account: true
          }
        },
        counterpartyAccount: true
      },
      orderBy: {
        transaction: {
          executedAt: 'asc'
        }
      }
    });

    // Group by counterparty and aggregate
    const counterpartyMap = new Map<string, {
      transfers: typeof transfers;
      tokens: Map<string, { count: number; totalAmount: Decimal }>;
    }>();

    for (const transfer of transfers) {
      const counterparty = transfer.counterparty;
      
      if (!counterpartyMap.has(counterparty)) {
        counterpartyMap.set(counterparty, {
          transfers: [],
          tokens: new Map()
        });
      }

      const counterpartyData = counterpartyMap.get(counterparty)!;
      counterpartyData.transfers.push(transfer);

      const token = transfer.currencyCode;
      if (!counterpartyData.tokens.has(token)) {
        counterpartyData.tokens.set(token, {
          count: 0,
          totalAmount: new Decimal(0)
        });
      }

      const tokenData = counterpartyData.tokens.get(token)!;
      tokenData.count++;
      tokenData.totalAmount = tokenData.totalAmount.add(transfer.amount);
    }

    // Convert to result format and filter by minimum transfer count
    const results: CounterpartyTransferSummary[] = [];

    for (const [counterparty, data] of counterpartyMap.entries()) {
      if (data.transfers.length < minTransferCount) {
        continue;
      }

      const tokens: CounterpartyTransferSummary['tokens'] = {};
      
      for (const [token, tokenData] of data.tokens.entries()) {
        tokens[token] = {
          count: tokenData.count,
          totalAmount: tokenData.totalAmount,
          averageAmount: tokenData.totalAmount.div(tokenData.count)
        };
      }

      const transferDates = data.transfers.map(t => t.transaction.executedAt);
      const firstTransfer = data.transfers[0];
      const isSelfTransfer = !!firstTransfer.counterpartyAccount;

      results.push({
        counterparty,
        totalTransfers: data.transfers.length,
        tokens,
        firstTransferDate: new Date(Math.min(...transferDates.map(d => d.getTime()))),
        lastTransferDate: new Date(Math.max(...transferDates.map(d => d.getTime()))),
        isSelfTransfer,
        counterpartyAccountName: firstTransfer.counterpartyAccount?.name
      });
    }

    // Sort by total transfer count descending
    return results.sort((a, b) => b.totalTransfers - a.totalTransfers);
  }

  /**
   * Get transfer summary for a specific wallet
   */
  async getWalletTransferSummary(
    walletAddress: string,
    options: Omit<TransferAnalyticsOptions, 'walletAddresses'> = {}
  ) {
    return this.getCounterpartyAggregation({
      ...options,
      walletAddresses: [walletAddress]
    });
  }

  /**
   * Get top counterparties by transfer count
   */
  async getTopCounterpartiesByCount(
    limit: number = 10,
    options: TransferAnalyticsOptions = {}
  ): Promise<CounterpartyTransferSummary[]> {
    const results = await this.getCounterpartyAggregation(options);
    return results.slice(0, limit);
  }

  /**
   * Get top counterparties by total amount for a specific token
   */
  async getTopCounterpartiesByTokenAmount(
    token: string,
    limit: number = 10,
    options: TransferAnalyticsOptions = {}
  ): Promise<CounterpartyTransferSummary[]> {
    const results = await this.getCounterpartyAggregation({
      ...options,
      tokens: [token]
    });

    return results
      .filter(r => r.tokens[token])
      .sort((a, b) => b.tokens[token].totalAmount.comparedTo(a.tokens[token].totalAmount))
      .slice(0, limit);
  }

  /**
   * Get transfer statistics for all company wallets
   */
  async getOverallTransferStats(
    options: TransferAnalyticsOptions = {}
  ): Promise<{
    totalTransfers: number;
    uniqueCounterparties: number;
    tokenBreakdown: {
      [token: string]: {
        count: number;
        totalAmount: Decimal;
        uniqueCounterparties: number;
      };
    };
    dateRange: {
      from: Date | null;
      to: Date | null;
    };
  }> {
    const results = await this.getCounterpartyAggregation(options);
    
    const tokenBreakdown: any = {};
    let totalTransfers = 0;
    const uniqueCounterparties = results.length;
    let earliestDate: Date | null = null;
    let latestDate: Date | null = null;

    for (const result of results) {
      totalTransfers += result.totalTransfers;
      
      // Update date range
      if (!earliestDate || result.firstTransferDate < earliestDate) {
        earliestDate = result.firstTransferDate;
      }
      if (!latestDate || result.lastTransferDate > latestDate) {
        latestDate = result.lastTransferDate;
      }

      // Aggregate token data
      for (const [token, tokenData] of Object.entries(result.tokens)) {
        if (!tokenBreakdown[token]) {
          tokenBreakdown[token] = {
            count: 0,
            totalAmount: new Decimal(0),
            uniqueCounterparties: 0
          };
        }
        
        tokenBreakdown[token].count += tokenData.count;
        tokenBreakdown[token].totalAmount = tokenBreakdown[token].totalAmount.add(tokenData.totalAmount);
        tokenBreakdown[token].uniqueCounterparties++;
      }
    }

    return {
      totalTransfers,
      uniqueCounterparties,
      tokenBreakdown,
      dateRange: {
        from: earliestDate,
        to: latestDate
      }
    };
  }

  /**
   * Get only self-transfers (transfers between our own accounts)
   */
  async getSelfTransfers(
    options: TransferAnalyticsOptions = {}
  ): Promise<CounterpartyTransferSummary[]> {
    const results = await this.getCounterpartyAggregation(options);
    return results.filter(r => r.isSelfTransfer);
  }

  /**
   * Get only external transfers (transfers to external counterparties)
   */
  async getExternalTransfers(
    options: TransferAnalyticsOptions = {}
  ): Promise<CounterpartyTransferSummary[]> {
    const results = await this.getCounterpartyAggregation(options);
    return results.filter(r => !r.isSelfTransfer);
  }

  /**
   * Get transfer breakdown by type (self vs external)
   */
  async getTransferTypeBreakdown(
    options: TransferAnalyticsOptions = {}
  ): Promise<{
    selfTransfers: {
      count: number;
      counterparties: number;
      totalAmount: { [token: string]: Decimal };
    };
    externalTransfers: {
      count: number;
      counterparties: number;
      totalAmount: { [token: string]: Decimal };
    };
  }> {
    const results = await this.getCounterpartyAggregation(options);

    const selfTransfers = results.filter(r => r.isSelfTransfer);
    const externalTransfers = results.filter(r => !r.isSelfTransfer);

    const aggregateTokens = (transfers: CounterpartyTransferSummary[]) => {
      const tokenTotals: { [token: string]: Decimal } = {};
      for (const transfer of transfers) {
        for (const [token, data] of Object.entries(transfer.tokens)) {
          if (!tokenTotals[token]) {
            tokenTotals[token] = new Decimal(0);
          }
          tokenTotals[token] = tokenTotals[token].add(data.totalAmount);
        }
      }
      return tokenTotals;
    };

    return {
      selfTransfers: {
        count: selfTransfers.reduce((sum, t) => sum + t.totalTransfers, 0),
        counterparties: selfTransfers.length,
        totalAmount: aggregateTokens(selfTransfers)
      },
      externalTransfers: {
        count: externalTransfers.reduce((sum, t) => sum + t.totalTransfers, 0),
        counterparties: externalTransfers.length,
        totalAmount: aggregateTokens(externalTransfers)
      }
    };
  }

  /**
   * Export counterparty data to CSV format
   */
  async exportCounterpartyDataToCsv(
    options: TransferAnalyticsOptions = {}
  ): Promise<string> {
    const results = await this.getCounterpartyAggregation(options);

    const csvLines = [
      'Counterparty,Total Transfers,USDC Count,USDC Total,USDT Count,USDT Total,SOL Count,SOL Total,First Transfer,Last Transfer,Is Self Transfer,Account Name'
    ];

    for (const result of results) {
      const usdcData = result.tokens['USDC'] || { count: 0, totalAmount: new Decimal(0) };
      const usdtData = result.tokens['USDT'] || { count: 0, totalAmount: new Decimal(0) };
      const solData = result.tokens['SOL'] || { count: 0, totalAmount: new Decimal(0) };

      csvLines.push([
        result.counterparty,
        result.totalTransfers,
        usdcData.count,
        usdcData.totalAmount.toString(),
        usdtData.count,
        usdtData.totalAmount.toString(),
        solData.count,
        solData.totalAmount.toString(),
        result.firstTransferDate.toISOString().split('T')[0],
        result.lastTransferDate.toISOString().split('T')[0],
        result.isSelfTransfer ? 'Yes' : 'No',
        result.counterpartyAccountName || ''
      ].join(','));
    }

    return csvLines.join('\n');
  }
}
