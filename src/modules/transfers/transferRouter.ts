import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";

const getTransferByIdSchema = z.object({
  id: z.string(),
});

export const transferRouter = createTRPCRouter({
  getById: publicProcedure.input(getTransferByIdSchema).query(async ({ input, ctx }) => {
    try {
      const transfer = await ctx.prisma.transfer.findUnique({
        where: { id: input.id },
        include: {
          transaction: {
            select: {
              id: true,
              executedAt: true,
              account: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
            },
          },
          reconciliations: {
            where: {
              status: {
                in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
              },
            },
            select: {
              id: true,
              status: true,
              confidenceScore: true,
              invoice: {
                select: {
                  id: true,
                  invoiceReference: true,
                  amountGross: true,
                  currencyCode: true,
                  vendor: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!transfer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transfer not found",
        });
      }

      return transfer;
    } catch (error) {
      console.error("Failed to fetch transfer:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch transfer",
        cause: error,
      });
    }
  }),
});
