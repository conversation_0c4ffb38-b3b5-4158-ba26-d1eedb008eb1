# Reconciliation Module

This module provides functionality for reconciling invoices with transfers in the accounting system.

## Reconciliation CLI

The Reconciliation CLI provides tools for finding and managing reconciliations between invoices and transfers. It allows you to:

- Find potential matches between invoices and transfers
- Reconcile a specific invoice with matching transfers
- Auto-reconcile all unmatched invoices
- View reconciliation statistics

### Usage

```bash
# Show reconciliation statistics
pnpm tsx src/modules/reconciliation/reconciliationCli.ts stats

# Find potential matches (preview only)
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-matches --limit 10

# Find and store reconciliations with custom confidence threshold
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-matches --store --min-confidence 70

# Find matches for a specific invoice
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-invoice <invoice-id> --limit 5

# Find and store reconciliation for a specific invoice
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-invoice <invoice-id> --store --min-confidence 50

# Auto-reconcile all unmatched invoices (preview)
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-all-invoices

# Auto-reconcile all unmatched invoices (store)
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-all-invoices --store

# Output in JSON format
pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-matches --format json --limit 5
```

### Options

- `--store`: Store reconciliations in the database (default: false)
- `--min-confidence`: Minimum confidence score for auto-matching (0-100)
- `--max-days`: Maximum days difference between invoice and transfer dates
- `--amount-tolerance`: Amount tolerance as a fraction (e.g., 0.02 for 2%)
- `--limit`: Maximum number of matches to display (default: 10)
- `--format`: Output format (table or json, default: table)

## Reconciliation Logic

The reconciliation system uses a confidence-based matching algorithm that considers:

1. **Amount Matching**: Exact matches get the highest score, with tolerance for small differences
2. **Currency Matching**: Same currency transactions get additional points
3. **Date Proximity**: Transactions on the same day or within a configurable window get points

The confidence score ranges from 0-100, with higher scores indicating better matches.

### Matching Criteria

- **Exact Amount Match**: +50 points
- **Amount Within Tolerance**: +40 points
- **Currency Match**: +20 points
- **Same Date**: +30 points
- **Within Max Days Difference**: +20 points

### Auto-Matching

By default, only matches with a confidence score of 80 or higher are automatically reconciled when using the `--store` option. You can adjust this threshold with the `--min-confidence` parameter.

## Reconciliation Service

The reconciliation service provides the following functions:

- `findPotentialMatches`: Find potential matches between unmatched invoices and transfers
- `autoReconcile`: Automatically reconcile invoices and transfers based on confidence score
- `autoReconcileForInvoice`: Auto-reconcile for a specific invoice
- `autoReconcileForTransfer`: Auto-reconcile for a specific transfer
- `manualMatch`: Manually match an invoice with a transfer
- `manualUnmatch`: Manually unmatch an invoice from a transfer
- `getReconciliationStats`: Get reconciliation statistics

These functions are used by both the CLI and the web application.
