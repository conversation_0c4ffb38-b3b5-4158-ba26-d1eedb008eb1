import fs from "fs";
import { gmail_v1, google as googleApi } from "googleapis";
import { JWT } from "google-auth-library";

const accountFilePath = "../misc/strange-bloom-377908-065d7380e1ef.json";

const serviceAccount = JSON.parse(fs.readFileSync(accountFilePath, "utf8"));

const SCOPES = ["https://www.googleapis.com/auth/gmail.readonly", "https://www.googleapis.com/auth/admin.directory.user.readonly"];

// const adminUser = "<EMAIL>"; // the account that granted delegation
const adminUser = "<EMAIL>"; // the account that granted delegation

export namespace google {
  export const googleSdk = googleApi;

  export const auth = new JWT({
    email: serviceAccount.client_email,
    key: serviceAccount.private_key,
    scopes: SCOPES,
    subject: adminUser, // will be overwritten per-user below
  });

  export const getAuthForUser = (userEmail: string) => {
    return new JWT({
      email: serviceAccount.client_email,
      key: serviceAccount.private_key,
      scopes: SCOPES,
      subject: userEmail, // set the user for delegation
    });
  };

  export const directory = googleApi.admin({ version: "directory_v1", auth });

  export const getGmailClient = (userEmail: string) => {
    return googleApi.gmail({
      version: "v1",
      auth: getAuthForUser(userEmail),
    });
  };
}
