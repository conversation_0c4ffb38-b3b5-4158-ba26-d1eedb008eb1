import * as lfi from "lfi";
import { cli } from "../core/utils/cliUtils";
import { gmail_v1 } from "googleapis";
import { google } from "@/modules/integration/googleClient";
import PQueue from "p-queue";
import { date } from "../core/utils/dateUtils";

const getMessageQueue = new PQueue({
  interval: 1000,
  intervalCap: 50,
});

async function main() {
  const gmailClient = google.getGmailClient("<EMAIL>");

  const mails = await lfi.pipe(
    lfi.asConcur(
      await (async function* () {
        console.log("in fetch all emails");

        let pageToken: string | undefined = undefined;
        while (true) {
          const searchKeywordQuery = ["rechnung", "invoice", "honorar"].join(" OR ");
          // googleClient.auth.subject = financeUserEmail!.primaryEmail!; // set the user for delegation
          const messageRes: gmail_v1.Schema$ListMessagesResponse = await gmailClient.users.messages
            .list({
              userId: "me",
              maxResults: 100,
              pageToken,
              q: `has:attachment (${searchKeywordQuery})`,
            })

            .then((res) => res.data);

          for (const msg of messageRes.messages ?? []) yield msg;

          // if (!args.testArgs?.limit) pageToken = (messageRes as any).nextPageToken;

          if (!pageToken) break;
        }
      })()
    ),
    lfi.mapConcur(async (msg) => {
      const message = await getMessageQueue.add(async () =>
        gmailClient.users.messages.get({
          userId: "me",
          id: msg.id!,
        })
      );
      if (!message) throw new Error("Message not found");

      const bodyParts = message.data.payload?.parts?.find((p) => p.mimeType === "multipart/alternative");

      function getPart(mimeType: string) {
        const part = (bodyParts?.parts ?? []).find((p) => p.mimeType === mimeType);
        return part ? Buffer.from(part.body?.data ?? "", "base64url").toString("utf8") : undefined;
      }

      let bodyPlain = getPart("text/plain");
      let bodyHtml = getPart("text/html");

      const bodyText = bodyHtml ?? bodyPlain ?? "";

      const messageParsed = {
        subject: message.data.payload?.headers?.find((h) => h.name === "Subject")?.value ?? "",
        from: message.data.payload?.headers?.find((h) => h.name === "From")?.value ?? "",
        to: message.data.payload?.headers?.find((h) => h.name === "To")?.value ?? "",
        body: {
          bodyText,
          bodyPlain,
          bodyHtml,
        },
      };

      return { ...message.data, ...messageParsed };
    }),
    lfi.reduceConcur(lfi.toArray())
  );

  console.log("first", date.parseUnix(mails[0].internalDate!));
  console.log("last", date.parseUnix(mails[mails.length - 1].internalDate!));
}

cli.scafold({
  main,
});
