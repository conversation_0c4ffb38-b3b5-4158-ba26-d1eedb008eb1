import { envVars } from "@/envVars";
import { <PERSON><PERSON><PERSON>, observe } from "@lmnr-ai/lmnr";
import OpenAI from "openai";
Laminar.initialize({
  projectApiKey: "plcj7PCjAUMdHj3zXYPMWWqZ7vrtznAeS8H6E2hnHGR1yOVHyaGy8BB2kNbZmi5P",
  instrumentModules: {
    openAI: OpenAI,
  },
});

const openai = new OpenAI({
  apiKey: envVars.OPENAI_API_KEY,
});

async function main() {
  await observe({ name: "main" }, async () => {
    const res = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{ role: "user", content: "Hello, how are you?" }],
    });

    console.log(res);
  });

  await Laminar.flush();
}

main();
