import { Readable } from "stream";
import { fileTypeFromBuffer } from "file-type";
import { createHash } from "crypto";
import path from "path";
import fs from "fs/promises";

export type Base64String = string;
export type PathString = string;
export type UrlString = string;
export type GenericFile = ReadableStream | Buffer | PathString | Base64String | MotlFile;

export class MotlFile {
  static async parse(file: GenericFile, fileName?: string): Promise<MotlFile> {
    if (file instanceof MotlFile) return file;

    const fileData = await MotlFile.parseGenericFile(file);

    const fileType = await fileTypeFromBuffer(fileData);

    const ext = fileType?.ext ?? "unkown";

    if (!fileName) fileName = "file." + ext;

    const motlFile = new MotlFile(fileData, fileName, fileType?.mime!);
    return motlFile;
  }

  // initially called for storing the file as a buffers
  static async parseGenericFile(file: GenericFile) {
    if (file instanceof Buffer) return file;
    else if (file instanceof ReadableStream) {
      // Logic to convert ReadableStream to Buffer

      if (typeof file.getReader === "function") {
        return MotlFile.webStreamToBuffer(file as any);
      }

      return MotlFile.streamToBuffer(file as any);
    }
    // url
    else if (typeof file === "string" && file.startsWith("http")) {
      const res = await fetch(file);
      const buffer = Buffer.from(await res.arrayBuffer());
      return buffer;
    }
    // base64
    else if (typeof file === "string" && file.length > 1000) {
      const splits = file.split(",");
      const base64 = splits[splits.length - 1];
      return Buffer.from(base64, "base64url");
    }
    // path
    else if (typeof file === "string" && file.includes("/")) {
      const buffer = await fs.readFile(file);
      return buffer;
    } else {
      throw new Error("Unsupported file type", { file } as any);
    }
  }

  constructor(public buffer: Buffer, public fileName: string, public mimeType: string) {}

  async toBuffer(): Promise<Buffer> {
    return this.buffer;
  }

  async toReadableStream(): Promise<ReadableStream> {
    // Logic to convert this.fileData to ReadableStream
    return MotlFile.bufferToReadableStream(this.buffer) as any;
  }

  async getBase64(): Promise<Base64String> {
    return this.buffer.toString("base64");
  }

  async getBase64FileUrl() {
    return `data:${(await this.getFileType())?.mime!};base64,` + this.buffer.toString("base64");
  }

  async toBlob() {
    return new Blob([this.buffer]);
  }

  async getFileType() {
    const fileType = await fileTypeFromBuffer(this.buffer);

    return fileType;
  }

  private hash: string | undefined;

  async getHash() {
    if (!this.hash) {
      // Create a hash and update it with the file content
      const hashSum = createHash("sha256");
      hashSum.update(this.buffer);

      // Convert the hash to a hexadecimal string
      this.hash = hashSum.digest("hex");
    }

    return this.hash;
  }

  getMbSize(): number {
    return this.buffer.length / (1024 * 1024);
  }

  async getTempFilePath() {
    const tempFilePath = path.dirname(__filename) + "/tmp/" + Math.random().toString(24).substring(7) + "/" + this.fileName;
    await fs.mkdir(path.dirname(tempFilePath), { recursive: true });
    await fs.writeFile(tempFilePath, this.buffer);
    return tempFilePath;
  }

  // static async blobToBuffer(blob: Blob): Promise<Buffer> {
  //   return new Promise((resolve, reject) => {
  //     const reader = new FileReader();
  //     reader.onload = () => {
  //       const arrayBuffer = reader.result as ArrayBuffer;
  //       resolve(Buffer.from(arrayBuffer));
  //     };
  //     reader.onerror = reject;
  //     reader.readAsArrayBuffer(blob);
  //   });
  // }

  static async streamToBuffer(stream: Readable): Promise<Buffer> {
    const chunks: Buffer[] = [];
    return new Promise((resolve, reject) => {
      stream.on("data", (chunk) => chunks.push(Buffer.from(chunk)));
      stream.on("error", (err) => reject(err));
      stream.on("end", () => resolve(Buffer.concat(chunks)));
    });
  }

  static async webStreamToBuffer(stream: ReadableStream<Uint8Array>): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const reader = stream.getReader();
      const chunks: Uint8Array[] = [];

      function read() {
        reader
          .read()
          .then(({ done, value }) => {
            if (done) {
              resolve(Buffer.concat(chunks));
              return;
            }
            chunks.push(value);
            read();
          })
          .catch(reject);
      }

      read();
    });
  }

  static bufferToReadableStream(buffer: Buffer): Readable {
    const readable = new Readable();
    readable.push(buffer); // Push the buffer to the stream
    readable.push(null); // Indicates the end of the stream
    return readable;
  }
}
