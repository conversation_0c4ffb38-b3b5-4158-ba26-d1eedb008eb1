import { test } from "vitest";
import { s3 } from "./s3";
import path from "path";
import { MotlFile } from "./motlFile";

test("same file should not be uploaded again", async () => {
  const absolutFile = path.join(__dirname, "data", "slack_invoice_SBIE-8692888.pdf-attachment.pdf");

  const file = await MotlFile.parse(absolutFile);

  const uploadRes1 = await s3.upload({
    file,
    // public: true,
  });

  console.log("url", uploadRes1.url);

  const debug = 1;
});
