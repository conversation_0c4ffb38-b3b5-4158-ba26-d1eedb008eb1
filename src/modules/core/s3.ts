import { envVars } from "@/envVars";
import { GetObjectCommand, PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { GenericFile, MotlFile } from "./motlFile";

const defaultBucketName = "albert";

const publicDevHost = "https://pub-da9a66ddb6b5467c965f355f1bbe927d.r2.dev";

export namespace s3 {
  export const client = new S3Client({
    forcePathStyle: true,
    region: "auto",
    endpoint: envVars.S3_ENDPOINT!,
    credentials: {
      accessKeyId: envVars.S3_ACCESS_ID!,
      secretAccessKey: envVars.S3_ACCESS_KEY!,
    },
  });

  export async function upload(args: { file: GenericFile; key?: string; public?: boolean }) {
    const file = await MotlFile.parse(args.file);
    const key = args?.key ?? (await file.getHash());
    const res = await client.send(
      new PutObjectCommand({
        Bucket: defaultBucketName,
        Key: key,
        ContentType: file.mimeType,
        Body: file.buffer,
        ACL: args.public ? "public-read" : undefined,
      })
    );

    const url = publicDevHost + "/" + key;

    return { ...res, url };
  }
}
