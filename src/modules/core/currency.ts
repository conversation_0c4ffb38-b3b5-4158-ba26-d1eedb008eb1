// @ts-nocheck
import cc from "cryptocompare";
import fiatCurrencies from "currency-symbol-map/map";
import { mapToObj, merge, reduce } from "remeda";
import { cli } from "./utils/cliUtils";
import { createMotlCacher } from "./motlCacher";


export namespace currency {
  export const additionalCurrencies = reduce(
    ["USDC", "SOL", "USDT"],
    (acc, symbol) => {
      acc[symbol] = symbol;
      return acc;
    },
    {} as Record<string, string>
  );

  export const fiatCurrenciesMap = fiatCurrencies;

  export const allCurrencies = merge(fiatCurrenciesMap, additionalCurrencies);

  export function formatMonetary(amount: any, currencyCode?: string) {
    if (amount == null) return "-";

    if (!currencyCode) return "no currency code";

    const currencySymbol = currency.allCurrencies[currencyCode] ?? currencyCode;

    if (!currencySymbol) return "no currency symbol";

    // Handle Decimal types properly
    const numericAmount = typeof amount === "object" && amount.toNumber ? amount.toNumber() : Number(amount);

    return `${currencySymbol} ${numericAmount.toLocaleString("de-DE", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 10,
    })}`;
  }
}

async function main() {
  const currentPrice = await currency.getPrice("BTC", "USD");
  console.log(currentPrice);

  const now = new Date();

  const priceHistoryNormal = await currency.getPriceHistory("EUR", "USD", now);
  console.log(now, priceHistoryNormal);

  const nowStripped = new Date();
  nowStripped.setHours(0, 0, 0, 0);
  // nowStripped.setUTCHours(23)

  const priceHistoryNoTime = await currency.getPriceHistory("EUR", "USD", nowStripped);
  console.log(nowStripped, priceHistoryNoTime);

  const nowUtcStripped = new Date();
  nowUtcStripped.setUTCHours(0, 0, 0, 0);

  const priceHistoryNoTimeUtc = await currency.getPriceHistory("EUR", "USD", nowUtcStripped);
  console.log(nowUtcStripped, priceHistoryNoTimeUtc);
}

// cli.scafold({
//   main,
// });
