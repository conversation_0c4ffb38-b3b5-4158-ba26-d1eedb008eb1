import { envVars } from "@/envVars";
import ky from "ky";

export function createMotlCacher(args?: { url?: string; headers?: Record<string, string> }) {
  return ky.create({
    prefixUrl: args?.url,
    headers: {
      "x-api-key": envVars.MOTL_CACHER_API_KEY,
      ...args?.headers,
    },
    hooks: {
      beforeRequest: [
        async (req) => {
          const headers = new Headers(req.headers);
          headers.set("x-upstream-url", req.url);
          headers.set("x-api-key", envVars.MOTL_CACHER_API_KEY);

          const body = req.method === "GET" || req.method === "HEAD" ? undefined : await req.clone().arrayBuffer();

          // 2. Re-create a Request, copying over the pieces we actually need
          return new Request(envVars.MOTL_CACHER_URL, {
            method: req.method,
            headers,
            body: body, // safe: still unread at this point
            redirect: req.redirect, // keep original redirect policy
            mode: req.mode,
            // duplex: (req as any).duplex as any,

            // copy more props if you rely on them (mode, credentials, cf, etc.)
          });
        },
      ],
    },
  });
}
