import { Decimal } from "decimal.js-light";

export namespace monetary {
  /**
   * Compare two monetary values for equality within a precision threshold
   */
  export function equals(a: number, b: number, precision: number = 0.001) {
    return Math.abs(a - b) < precision;
  }

  /**
   * Round a monetary value to 2 decimal places (standard for currency)
   */
  export function roundToMonetary(value: number | string | Decimal): Decimal {
    return new Decimal(value).toDecimalPlaces(2);
  }

  /**
   * Convert a Decimal to a number rounded to 2 decimal places
   */
  export function toNumber(value: Decimal): number {
    return value.toDecimalPlaces(2).toNumber();
  }

  /**
   * Convert any monetary value to a properly formatted Decimal
   */
  export function toDecimal(value: number | string | Decimal): Decimal {
    return new Decimal(value).toDecimalPlaces(2);
  }

  /**
   * Format a monetary value for database storage (ensures 2 decimal places)
   */
  export function forDatabase(value: number | string | Decimal): Decimal {
    return new Decimal(value).toDecimalPlaces(2);
  }

  /**
   * Round a VAT rate to 2 decimal places
   */
  export function roundVatRate(value: number | string | Decimal): Decimal {
    return new Decimal(value).toDecimalPlaces(2);
  }

  /**
   * Round a conversion rate to 6 decimal places (for currency conversion)
   */
  export function roundConversionRate(value: number | string | Decimal): Decimal {
    return new Decimal(value).toDecimalPlaces(6);
  }

  /**
   * Round a crypto amount to 8 decimal places
   */
  export function roundCryptoAmount(value: number | string | Decimal): Decimal {
    return new Decimal(value).toDecimalPlaces(8);
  }
}
