import { braintrust } from "@/modules/tracing/braintrust";
import yargs from "yargs";

export namespace cli {
  export async function scafold(functions: Record<string, Function>) {
    const req = require;
    // parse with yargs
    const cliArgs = await yargs(process.argv.slice(2)).argv;

    // console.log("inside cli.scafold", {
    //   cliArgsPath: cliArgs.$0,
    //   requireMainPath: require.main?.id,
    // });

    if (!require.main?.id.includes(cliArgs.$0)) return;

    const command = cliArgs._[0];

    if (!command || !functions[command]) {
      console.error(`command ${command} not found`);
      console.error("Available commands:", Object.keys(functions).join(", "));
      return;
    }

    const commandFn = functions[command];
    try {
      await commandFn(cliArgs);
    } catch (err) {
      console.error("cli.scafold error running command " + command, err);
    } finally {
      const logger = await braintrust.getLogger();
      await logger?.flush();
    }
  }
}
