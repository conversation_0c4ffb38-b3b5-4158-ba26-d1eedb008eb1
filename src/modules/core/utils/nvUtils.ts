import { Result, ResultAsync } from "neverthrow";

export namespace nvUtils {
  export async function retry<TResult extends Result<unknown, unknown>>(args: { maxRetries: number }, fn: (retry: number) => Promise<TResult>) {
    let res = await fn(0);
    for (let i = 0; i < args.maxRetries; i++) {
      if (res.isOk()) {
        return res;
      }

      res = await fn(i);
    }
    return res;
  }
}

async function main() {
  async function test() {
    return ResultAsync.fromPromise(Promise.resolve("test"), (err) => err);
  }

  const res = await nvUtils.retry({ maxRetries: 3 }, async (retry) => {
    return await test();
  });
}

main();
