import { err, ok } from "neverthrow";
import z from "zod";

export namespace zodUtils {
  export function saveParse<TSchema extends z.ZodTypeAny>(schema: TSchema, value: unknown) {
    const parsed = schema.safeParse(value);
    if (!parsed.success) {
      return err({
        type: "ZodParseError",
        message: "Failed to parse value with zod",
        value,
        zodError: parsed.error,
      } as const);
    }

    return ok(parsed.data as z.infer<TSchema>);
  }

  export function optional<T extends z.ZodTypeAny>(schema: T) {
    return z.union([schema, z.null()]);
  }
}
