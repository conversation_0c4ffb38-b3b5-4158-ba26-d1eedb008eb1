export namespace date {
  export function now() {
    return new Date();
  }

  export function nowSeconds() {
    return Math.floor(Date.now() / 1000);
  }

  export function nowMilliseconds() {
    return Date.now();
  }

  export function parseUnix(_value: number | string) {
    const value = typeof _value === "string" ? parseInt(_value) : _value;

    const threshold = 32503680000; // year 3000 in miliseconds

    if (value < threshold) {
      return new Date(value * 1000); // interpret as seconds
    } else {
      return new Date(value); // interpret as milliseconds
    }
  }
}
