import JSON5 from "json5";
import { Result } from "neverthrow";

export namespace json {
  export const parseSave = Result.fromThrowable(
    JSON.parse,
    (e) =>
      ({
        type: "JSONParseError",
        message: e instanceof Error ? e.message : String(e),
        rawError: e,
      } as const)
  );

  export function findAndParseJson<T extends any>(text: string) {
    const jsonRegex = /{[\s\S]*}/;
    const match = text.match(jsonRegex);
    if (!match) return { parsed: null, error: new Error("No JSON found") };

    try {
      const json = JSON5.parse(match[0]);

      return { parsed: json as T };
    } catch (error: any) {
      // console.error("Failed to parse JSON output", {
      // 	text,
      // 	error,
      // });
      return { parsed: null, error };
    }
  }

  export function tryParseJson(str: string) {
    try {
      const parsed = JSON.parse(str);
      return parsed;
    } catch {
      return null;
    }
  }

  export function prettyPrint(obj: any) {
    return JSON.stringify(obj, null, 2);
  }
}
function test() {
  const text = `
{
	"totalAmountGross": 1.11,
	"totalAmountNet": 1.11,
	"currency": "USD",
	"items": [
		{
			"name": "DiscoResearch_DiscoLM-mixtral-87b-v2",
			"cost": { "cost": 0.01, "grossOrNet": "net" },
			"quantity": 1.3765,
			"unit": "input tokens"
		},
		{
			"name": "DiscoResearch_DiscoLM-mixtral-87b-v2",
			"cost": { "cost": 0.01, "grossOrNet": "net" },
			"quantity": 0.0765,
			"unit": "output tokens"
		},
		// ... (remaining items)
		{
			"name": "zero-one-ai_Yi-34BChat",
			"cost": { "cost": 0.01, "grossOrNet": "net" },
			"quantity": 0.32575,
			"unit": "output tokens"
		}
	],
	"discounts": []
}
`;

  const parsed = json.findAndParseJson(text);

  console.log(parsed);
}
