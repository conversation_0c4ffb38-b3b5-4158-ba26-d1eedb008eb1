import { describe, it, expect } from "vitest";
import { csvUtils } from "./csvUtils";
import { binanceUtils } from "./binanceUtils";

describe("csvUtils", () => {
  const sampleBinanceCSV = `Timestamp,Description,Paid OUT (EUR),Paid IN (EUR),Transaction Fee (EUR),Assets Used,Exchange Rates
Thu Oct 27 16:55:51 UTC 2022,JASPERAI 15125371524,,29.17,0.00,EUR 29.17,
Mon Aug 29 09:50:01 UTC 2022,RENDERCOM 14158304762,,1.01,0.00,EUR 1.01,
Sat Jul 23 05:51:49 UTC 2022,CLOUDFLARE,0.04,,0.00,USDT 0.04115926,1.00 EUR = 1.02898150 USDT
Fri May 27 05:48:11 UTC 2022,CODESUBMIT LLC,0.08,,0.00,USDT 0.08685501,1.00 EUR = 1.08568762 USDT`;

  describe("parseCSV", () => {
    it("should parse basic CSV content", () => {
      const result = csvUtils.parseCSV(sampleBinanceCSV);
      expect(result.isOk()).toBe(true);
      
      if (result.isOk()) {
        const parsed = result.value;
        expect(parsed.rowCount).toBe(4);
        expect(parsed.headers).toContain("Timestamp");
        expect(parsed.headers).toContain("Description");
        expect(parsed.headers).toContain("Assets Used");
      }
    });
  });

  describe("parseBinanceCSV", () => {
    it("should parse Binance CSV format", () => {
      const result = csvUtils.parseBinanceCSV(sampleBinanceCSV);
      expect(result.isOk()).toBe(true);
      
      if (result.isOk()) {
        const transactions = result.value;
        expect(transactions).toHaveLength(4);
        
        // Test first transaction (paid in)
        const firstTx = transactions[0];
        expect(firstTx.description).toBe("JASPERAI 15125371524");
        expect(firstTx.paidIn).toBe(29.17);
        expect(firstTx.paidOut).toBeUndefined();
        expect(firstTx.assetsUsed).toBe("EUR 29.17");
        
        // Test third transaction (paid out with crypto)
        const thirdTx = transactions[2];
        expect(thirdTx.description).toBe("CLOUDFLARE");
        expect(thirdTx.paidOut).toBe(0.04);
        expect(thirdTx.paidIn).toBeUndefined();
        expect(thirdTx.assetsUsed).toBe("USDT 0.04115926");
        expect(thirdTx.exchangeRates).toBe("1.00 EUR = 1.02898150 USDT");
      }
    });
  });
});

describe("binanceUtils", () => {
  describe("parseAssetsUsed", () => {
    it("should parse single asset", () => {
      const result = binanceUtils.parseAssetsUsed("EUR 29.17");
      expect(result).toEqual([{ currency: "EUR", amount: 29.17 }]);
    });

    it("should parse multiple assets", () => {
      const result = binanceUtils.parseAssetsUsed("BNB 0.00010402; BUSD 7.32473810; EUR 3.77");
      expect(result).toEqual([
        { currency: "BNB", amount: 0.00010402 },
        { currency: "BUSD", amount: 7.32473810 },
        { currency: "EUR", amount: 3.77 },
      ]);
    });

    it("should handle empty string", () => {
      const result = binanceUtils.parseAssetsUsed("");
      expect(result).toEqual([]);
    });
  });

  describe("parseExchangeRates", () => {
    it("should parse single exchange rate", () => {
      const result = binanceUtils.parseExchangeRates("1.00 EUR = 1.02898150 USDT");
      expect(result).toEqual([
        { fromCurrency: "EUR", toCurrency: "USDT", rate: 1.02898150 },
      ]);
    });

    it("should parse multiple exchange rates", () => {
      const result = binanceUtils.parseExchangeRates("1.00 EUR = 0.00385432 BNB; 1.00 EUR = 1.04788517 BUSD");
      expect(result).toEqual([
        { fromCurrency: "EUR", toCurrency: "BNB", rate: 0.00385432 },
        { fromCurrency: "EUR", toCurrency: "BUSD", rate: 1.04788517 },
      ]);
    });

    it("should handle empty string", () => {
      const result = binanceUtils.parseExchangeRates("");
      expect(result).toEqual([]);
    });
  });

  describe("processBinanceTransaction", () => {
    it("should process paid out transaction with crypto", () => {
      const transaction: csvUtils.BinanceTransaction = {
        timestamp: new Date("2022-07-23T05:51:49.000Z"),
        description: "CLOUDFLARE",
        paidOut: 0.04,
        paidIn: undefined,
        transactionFee: 0.00,
        assetsUsed: "USDT 0.04115926",
        exchangeRates: "1.00 EUR = 1.02898150 USDT",
      };

      const result = binanceUtils.processBinanceTransaction(transaction, "Binance Card");
      expect(result.isOk()).toBe(true);
      
      if (result.isOk()) {
        const processed = result.value;
        
        // Check transfer
        expect(processed.transfer.from).toBe("Binance Card");
        expect(processed.transfer.to).toBe("External");
        expect(processed.transfer.amount.toString()).toBe("0.04");
        expect(processed.transfer.currencyCode).toBe("EUR");
        
        // Check trades
        expect(processed.trades).toHaveLength(1);
        const trade = processed.trades[0];
        expect(trade.tokenFrom).toBe("EUR");
        expect(trade.tokenTo).toBe("USDT");
        expect(trade.amountFrom.toString()).toBe("0.04");
        expect(trade.amountTo.toString()).toBe("0.04115926");
        expect(trade.poolId).toBe("binance-card");
      }
    });

    it("should process paid in transaction", () => {
      const transaction: csvUtils.BinanceTransaction = {
        timestamp: new Date("2022-10-27T16:55:51.000Z"),
        description: "JASPERAI 15125371524",
        paidOut: undefined,
        paidIn: 29.17,
        transactionFee: 0.00,
        assetsUsed: "EUR 29.17",
        exchangeRates: "",
      };

      const result = binanceUtils.processBinanceTransaction(transaction, "Binance Card");
      expect(result.isOk()).toBe(true);
      
      if (result.isOk()) {
        const processed = result.value;
        
        // Check transfer
        expect(processed.transfer.from).toBe("External");
        expect(processed.transfer.to).toBe("Binance Card");
        expect(processed.transfer.amount.toString()).toBe("29.17");
        expect(processed.transfer.currencyCode).toBe("EUR");
        
        // Check trades (should be empty for EUR-only transactions)
        expect(processed.trades).toHaveLength(0);
      }
    });
  });
});
