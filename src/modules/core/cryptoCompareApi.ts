import { err, ok, ResultAsync } from "neverthrow";
import { createMotlCacher } from "./motlCacher";
import { envVars } from "@/envVars";

const baseUrl = "https://min-api.cryptocompare.com/data";

export namespace cryptoCompareApi {
  const motlCacher = createMotlCacher();

  function formateTimestamp(date: Date) {
    return Math.floor(date.getTime() / 1000);
  }

  export async function getPrice(base: string, quote: string) {
    const response = (await motlCacher.get(`${baseUrl}/price?fsym=${base}&tsyms=${quote}&ts=${formateTimestamp(new Date())}`).json()) as any;
    return response[quote] as number;
  }

  export function getPriceHistory(base: string, quote: string, date: Date) {
    return ResultAsync.fromPromise(
      motlCacher.get(`${baseUrl}/pricehistorical?fsym=${base}&tsyms=${quote}&ts=${formateTimestamp(date)}&api_key=${envVars.CRYPTO_COMPARE_API_KEY}`),
      (error) =>
        ({
          type: "error",
          code: "cryptoCompareApi.getPriceHistory",
          error,
          context: {
            base,
            quote,
            date,
          },
        } as const)
    ).andThen((response) => {
      // console.log("cryptoCompareApi cache hit", response.headers.get("x-edge-cache"));
      return ResultAsync.fromPromise(response.json(), (error) => ({
        type: "error",
        code: "cryptoCompareApi.getPriceHistory",
        raw: error,
        context: {
          base,
          quote,
          date,
        },
      })).andThen((json: any) => {
        if (!json[base] || !json[base][quote]) {
          return err({
            type: "error",
            code: "cryptoCompareApi.getPriceHistory.json",
            message: "No price found",
            res: json,
            context: {
              base,
              quote,
              date,
            },
          });
        }
        return ok(json[base][quote] as number);
      });
    });
  }

  export function getPriceForDay(base: string, quote: string, date: Date) {
    const dateCloned = new Date(date);
    dateCloned.setHours(0, 0, 0, 0);
    return getPriceHistory(base, quote, dateCloned);
  }
}
