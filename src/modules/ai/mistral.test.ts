import { describe, it, expect } from "vitest";
import { mistral } from "./mistral";
import path from "path";
import fs from "fs";
import { Mistral } from "@mistralai/mistralai";
import { envVars } from "@/envVars";
import ky from "ky";
import { measureTime } from "@/utils";
import { makeS3ObjectPublic, s3Client, uploadS3 } from "../core/s3";

it("should ocr FI20971070544.pdf with OCR", async () => {
  // Load the PDF file
  const pdfPath = path.join(__dirname, "data", "FI20971070544.pdf");
  const pdfBuffer = fs.readFileSync(pdfPath);
  const base64Data = pdfBuffer!.toString("base64");
  const base64Url = `data:application/pdf;base64,${base64Data}`;

  // Test the OCR functionality
  const result = await mistral.ocrDocument({ base64url: base64Url });

  // Basic assertions
  expect(result).toBeDefined();
  expect(typeof result.text).toBe("string");
  expect(result.text.length).toBeGreaterThan(0);
  expect(result.pages).toBeDefined();
  expect(Array.isArray(result.pages)).toBe(true);
  expect(result.pages.length).toBeGreaterThan(0);

  // Log the extracted text for manual verification
  console.log("Extracted OCR text:", result.text);
});

it("complete mistral cloudflare gateway llm", async () => {
  // Load the PDF file
  const pdfPath = path.join(__dirname, "data", "FI20971070544.pdf");

  const mistralClient = new Mistral({
    apiKey: envVars.MISTRAL_API_KEY,
    serverURL: "https://gateway.ai.cloudflare.com/v1/22ba25ef9ff60135ec1864f7a085fbac/mistral-ocr/mistral",
  });

  const mistralRes = await mistralClient.chat.complete({
    model: "mistral-large-latest",
    messages: [
      {
        role: "user",
        content: "What is the capital of France?",
      },
    ],
  });

  console.log(mistralRes);
});

it("complete mistral ocr cloudflare gateway", async () => {
  // Load the PDF file
  const pdfPath = path.join(__dirname, "data", "FI20971070544.pdf");
  const pdfBuffer = fs.readFileSync(pdfPath);

  console.log("base64Url.length", base64Url.length);

  const mistralRes = await mistral.ocrDocument({
    file: pdfBuffer,
  });

  console.log(mistralRes);
  console.log("response headers", mistralRes);
});

it("complete mistral ocr ky cloudflare gateway", async () => {
  // Load the PDF file
  const pdfPath = path.join(__dirname, "data", "FI20971070544.pdf");
  // const pdfPath = path.join(__dirname, "data", "DB Rechnung 284424987660.pdf.pdf");
  const pdfBuffer = fs.readFileSync(pdfPath);
  const base64Data = pdfBuffer!.toString("base64");
  const base64Url = `data:application/pdf;base64,${base64Data}`;
  console.log("base64Url.length", base64Url.length);

  const kyClient = ky.create({
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${envVars.MISTRAL_API_KEY}`,
    },
    prefixUrl: "https://gateway.ai.cloudflare.com/v1/22ba25ef9ff60135ec1864f7a085fbac/mistral-ocr/mistral/v1",
    // prefixUrl: "https://api.mistral.ai/",
  });

  const mistralRes = await measureTime("mistral ocr api call", async () => {
    return await kyClient.post("ocr", {
      body: JSON.stringify({
        model: "mistral-ocr-latest",
        document: {
          type: "document_url",
          document_url: base64Url,
        },
        include_image_base64: true,
      }),
    });
  });

  console.log(mistralRes);
});

it("mistral ocr clemens invoice", async () => {
  // Load the PDF file
  const pdfPath = path.join(__dirname, "data", "Clemens Allinger Feb Rechnung 3.pdf-attachment.pdf");
  const pdfBuffer = fs.readFileSync(pdfPath);

  const key = `clemens-invoice-${Date.now()}.pdf`;
  const uploadedFile = await uploadS3({
    body: pdfBuffer,
    contentType: "application/pdf",
    key,
  });

  const publicUrl = await makeS3ObjectPublic({
    key,
    expiresInSeconds: 60 * 60 * 24 * 7,
  });

  console.log("publicUrl", publicUrl);

  const mistralRes = await mistral.client.ocr.process({
    model: "mistral-ocr-latest",
    document: {
      type: "document_url",
      documentUrl: publicUrl,
    },
  });

  console.log(mistralRes);
  console.log("response headers", mistralRes);
});
