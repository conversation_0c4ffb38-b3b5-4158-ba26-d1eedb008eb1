# AI Agent Account Management

This module provides AI agent capabilities for managing accounts using Prisma to OpenAPI transformation.

## Features

- **Single Account Creation**: AI agent can create individual accounts with proper validation
- **Multiple Account Creation**: Create multiple accounts in a single operation (up to 10 accounts)
- **Account Listing**: Get all accounts with filtering and pagination
- **Account Statistics**: Get account statistics and type information
- **OpenAPI Integration**: Automatic schema generation from Prisma models
- **Partial Success Handling**: Robust error handling for bulk operations

## Usage

The AI agent now supports the following account-related commands:

### Creating Accounts

The AI can create accounts using natural language. Examples:

**Single Account Creation:**
```
"Create a new wallet account called 'My Bitcoin Wallet'"
"Add a bank account named 'Main Checking Account'"
"Create an exchange account for 'Binance Trading'"
"Set up a credit card account called 'Business Credit Card'"
```

**Multiple Account Creation:**
```
"Create multiple accounts: a wallet called 'Trading Wallet', a bank account 'Savings Account', and an exchange account 'Coinbase Pro'"
"Set up my complete account structure: main wallet, checking account, savings account, trading exchange, and business credit card"
"Add these accounts: Bitcoin Wallet (wallet), Ethereum Wallet (wallet), Main Bank (bank account)"
```

### Listing Accounts

```
"Show me all accounts"
"List all wallet accounts"
"Get accounts with their transaction counts"
```

### Account Statistics

```
"Show account statistics"
"How many accounts do I have by type?"
"What account types are available?"
```

## Technical Implementation

### Prisma to OpenAPI Transformer

The `prismaToOpenAPI` utility converts Prisma model schemas to OpenAPI function definitions:

```typescript
import { prismaToOpenAPI } from "@/modules/ai/prismaToOpenAPI";

// Get OpenAPI function definition
const createAccountFunction = prismaToOpenAPI.getCreateAccountFunction();

// Validate input
const validation = prismaToOpenAPI.validateCreateAccountInput(input);
```

### Account Tools

The `accountTools` module provides AI-compatible tool functions:

```typescript
import { accountTools } from "@/modules/ai/tools/accountTools";

// Available tools
const tools = accountTools.getAllAccountTools();
```

### Chat API Integration

The account tools are automatically integrated into the chat API at `/api/chat`. The AI model can call these functions directly based on user requests.

## Account Types

The system supports four account types:

- **WALLET**: Cryptocurrency wallet for digital assets
- **BANK_ACCOUNT**: Traditional bank account for fiat currency  
- **EXCHANGE_ACCOUNT**: Cryptocurrency exchange account
- **CREDIT_CARD**: Credit card account for payments

## Error Handling

All account operations use proper error handling with user-friendly messages:

- Input validation errors
- Database constraint violations
- Network/connection errors
- Permission errors

## Testing

Run the tests to verify functionality:

```bash
npm run test src/modules/ai/prismaToOpenAPI.test.ts
```

## Extension

To add more AI capabilities:

1. Create new tool functions in the appropriate module
2. Add OpenAPI schema definitions using `prismaToOpenAPI`
3. Register tools in the chat API
4. Add tests for new functionality

## Dependencies

- `zod`: Schema validation
- `zod-to-json-schema`: OpenAPI schema generation
- `@prisma/client`: Database operations
- `ai`: AI SDK for function calling
