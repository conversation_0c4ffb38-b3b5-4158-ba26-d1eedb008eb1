import { zodToJsonSchema } from "zod-to-json-schema";
import { z } from "zod";
import { AccountType } from "@/prisma/generated";

/**
 * Utility to transform Prisma models and Zod schemas to OpenAPI function definitions
 * for use with AI models that support function calling.
 */
export namespace prismaToOpenAPI {
  
  /**
   * Single account creation schema
   */
  export const singleAccountSchema = z.object({
    name: z.string().min(1, "Account name is required").max(255, "Account name too long"),
    type: z.nativeEnum(AccountType),
    publicKey: z.string().optional(),
  });

  /**
   * Multiple accounts creation schema - supports both single and bulk creation
   */
  export const createAccountsSchema = z.object({
    accounts: z.array(singleAccountSchema).min(1, "At least one account is required"),
  });

  /**
   * Convert a Zod schema to OpenAPI function definition format
   */
  export function zodSchemaToOpenAPIFunction(
    schema: z.ZodTypeAny,
    functionName: string,
    description: string
  ) {
    const jsonSchema = zodToJsonSchema(schema, { 
      $refStrategy: "none",
      target: "openApi3" 
    });

    // Remove the $schema property as it's not needed for function calling
    if (jsonSchema.$schema) {
      delete jsonSchema.$schema;
    }

    return {
      name: functionName,
      description,
      parameters: jsonSchema,
    };
  }

  /**
   * Get OpenAPI function definition for creating multiple accounts
   */
  export function getCreateAccountsFunction() {
    return zodSchemaToOpenAPIFunction(
      createAccountsSchema,
      "createAccounts",
      "Create one or more accounts. Each account needs a name and type. Account types can be: WALLET (cryptocurrency wallet), BANK_ACCOUNT (traditional bank account), EXCHANGE_ACCOUNT (cryptocurrency exchange), or CREDIT_CARD (credit card account). You can create up to 10 accounts in a single call."
    );
  }

  /**
   * Get OpenAPI function definition for creating a single account (legacy support)
   */
  export function getCreateAccountFunction() {
    return zodSchemaToOpenAPIFunction(
      singleAccountSchema,
      "createAccount",
      "Create a single account with the specified name and type. Account types can be: WALLET, BANK_ACCOUNT, EXCHANGE_ACCOUNT, or CREDIT_CARD. For creating multiple accounts, use createAccounts instead."
    );
  }

  /**
   * Get all available account-related function definitions
   */
  export function getAccountFunctions() {
    return {
      createAccount: getCreateAccountFunction(),
      createAccounts: getCreateAccountsFunction(),
    };
  }

  /**
   * Validate single account creation input
   */
  export function validateCreateAccountInput(input: unknown) {
    return singleAccountSchema.safeParse(input);
  }

  /**
   * Validate multiple accounts creation input
   */
  export function validateCreateAccountsInput(input: unknown) {
    return createAccountsSchema.safeParse(input);
  }

  /**
   * Get available account types for documentation/help
   */
  export function getAccountTypes() {
    return Object.values(AccountType).map(type => ({
      value: type,
      description: getAccountTypeDescription(type)
    }));
  }

  /**
   * Get human-readable description for account types
   */
  function getAccountTypeDescription(type: AccountType): string {
    switch (type) {
      case AccountType.WALLET:
        return "Cryptocurrency wallet for digital assets";
      case AccountType.BANK_ACCOUNT:
        return "Traditional bank account for fiat currency";
      case AccountType.EXCHANGE_ACCOUNT:
        return "Cryptocurrency exchange account";
      case AccountType.CREDIT_CARD:
        return "Credit card account for payments";
      default:
        return "Account type";
    }
  }
}
