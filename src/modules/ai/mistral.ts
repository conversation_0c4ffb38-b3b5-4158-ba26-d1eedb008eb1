import { envVars } from "@/envVars";
import { Mistral } from "@mistralai/mistralai";
import { ResultAsync } from "neverthrow";
import PQueue from "p-queue";

const mistralQueue = new PQueue({
  interval: 1000,
  intervalCap: 5,
});

export namespace mistral {
  export const client = new Mistral({
    apiKey: envVars.MISTRAL_API_KEY,
    serverURL: "https://gateway.ai.cloudflare.com/v1/22ba25ef9ff60135ec1864f7a085fbac/mistral-ocr/mistral",
  });

  export function ocrDocument(args: { file: Buffer }) {
    return ResultAsync.fromPromise(
      (async () => {
        const base64Data = args.file.toString("base64");
        const base64Url = `data:application/pdf;base64,${base64Data}`;

        const ocrResponse = await mistralQueue
          .add(() =>
            client.ocr.process({
              model: "mistral-ocr-latest",
              document: {
                type: "document_url",
                documentUrl: base64Url,
              },
              includeImageBase64: true,
            })
          )
          .then((res) => res!);

        const text = ocrResponse.pages.map((page) => page.markdown).join("\n\n");

        return {
          ...ocrResponse,
          text: text,
        };
      })(),
      (err) => ({ type: "mistralOcrError", err } as const)
    );
  }
}
