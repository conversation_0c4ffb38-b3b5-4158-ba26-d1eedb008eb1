import { describe, test, expect } from "vitest";
import { prismaToOpenAPI } from "./prismaToOpenAPI";
import { AccountType } from "@/prisma/generated";

describe("prismaToOpenAPI", () => {
  test("should create valid OpenAPI function definition for createAccount", () => {
    const functionDef = prismaToOpenAPI.getCreateAccountFunction();

    expect(functionDef.name).toBe("createAccount");
    expect(functionDef.description).toContain("Create a single account");
    expect(functionDef.parameters).toBeDefined();

    // Cast to any to access properties since zod-to-json-schema returns generic object
    const params = functionDef.parameters as any;
    expect(params.type).toBe("object");
    expect(params.properties).toBeDefined();

    // Check that name and type properties exist
    expect(params.properties.name).toBeDefined();
    expect(params.properties.type).toBeDefined();

    // Check required fields
    expect(params.required).toContain("name");
    expect(params.required).toContain("type");
  });

  test("should create valid OpenAPI function definition for createAccounts", () => {
    const functionDef = prismaToOpenAPI.getCreateAccountsFunction();

    expect(functionDef.name).toBe("createAccounts");
    expect(functionDef.description).toContain("Create one or more accounts");
    expect(functionDef.parameters).toBeDefined();

    // Cast to any to access properties since zod-to-json-schema returns generic object
    const params = functionDef.parameters as any;
    expect(params.type).toBe("object");
    expect(params.properties).toBeDefined();

    // Check that accounts property exists and is an array
    expect(params.properties.accounts).toBeDefined();
    expect(params.properties.accounts.type).toBe("array");
    expect(params.properties.accounts.items).toBeDefined();

    // Check required fields
    expect(params.required).toContain("accounts");
  });

  test("should validate single account creation input correctly", () => {
    // Valid input
    const validInput = {
      name: "Test Account",
      type: AccountType.WALLET
    };
    const validResult = prismaToOpenAPI.validateCreateAccountInput(validInput);
    expect(validResult.success).toBe(true);

    // Invalid input - missing name
    const invalidInput1 = {
      type: AccountType.WALLET
    };
    const invalidResult1 = prismaToOpenAPI.validateCreateAccountInput(invalidInput1);
    expect(invalidResult1.success).toBe(false);

    // Invalid input - empty name
    const invalidInput2 = {
      name: "",
      type: AccountType.WALLET
    };
    const invalidResult2 = prismaToOpenAPI.validateCreateAccountInput(invalidInput2);
    expect(invalidResult2.success).toBe(false);

    // Invalid input - invalid type
    const invalidInput3 = {
      name: "Test Account",
      type: "INVALID_TYPE"
    };
    const invalidResult3 = prismaToOpenAPI.validateCreateAccountInput(invalidInput3);
    expect(invalidResult3.success).toBe(false);
  });

  test("should validate multiple accounts creation input correctly", () => {
    // Valid input - single account
    const validInput1 = {
      accounts: [{
        name: "Test Account",
        type: AccountType.WALLET
      }]
    };
    const validResult1 = prismaToOpenAPI.validateCreateAccountsInput(validInput1);
    expect(validResult1.success).toBe(true);

    // Valid input - multiple accounts
    const validInput2 = {
      accounts: [
        { name: "Wallet 1", type: AccountType.WALLET },
        { name: "Bank Account", type: AccountType.BANK_ACCOUNT },
        { name: "Exchange", type: AccountType.EXCHANGE_ACCOUNT }
      ]
    };
    const validResult2 = prismaToOpenAPI.validateCreateAccountsInput(validInput2);
    expect(validResult2.success).toBe(true);

    // Invalid input - empty accounts array
    const invalidInput1 = { accounts: [] };
    const invalidResult1 = prismaToOpenAPI.validateCreateAccountsInput(invalidInput1);
    expect(invalidResult1.success).toBe(false);

    // Invalid input - too many accounts (more than 10)
    const invalidInput2 = {
      accounts: Array(11).fill({ name: "Test", type: AccountType.WALLET })
    };
    const invalidResult2 = prismaToOpenAPI.validateCreateAccountsInput(invalidInput2);
    expect(invalidResult2.success).toBe(false);

    // Invalid input - invalid account data
    const invalidInput3 = {
      accounts: [{ name: "", type: AccountType.WALLET }]
    };
    const invalidResult3 = prismaToOpenAPI.validateCreateAccountsInput(invalidInput3);
    expect(invalidResult3.success).toBe(false);
  });

  test("should return all account types with descriptions", () => {
    const accountTypes = prismaToOpenAPI.getAccountTypes();
    
    expect(accountTypes).toHaveLength(4);
    expect(accountTypes.map(t => t.value)).toEqual([
      AccountType.WALLET,
      AccountType.BANK_ACCOUNT,
      AccountType.EXCHANGE_ACCOUNT,
      AccountType.CREDIT_CARD
    ]);
    
    // Check that each type has a description
    accountTypes.forEach(type => {
      expect(type.description).toBeDefined();
      expect(type.description.length).toBeGreaterThan(0);
    });
  });

  test("should generate JSON schema without $schema property", () => {
    const functionDef = prismaToOpenAPI.getCreateAccountFunction();

    // Cast to any to access properties
    const params = functionDef.parameters as any;

    // Should not contain $schema property
    expect(params.$schema).toBeUndefined();

    // Should be valid OpenAPI 3.0 format
    expect(params.type).toBe("object");
    expect(params.properties).toBeDefined();
    expect(params.required).toBeDefined();
  });
});
