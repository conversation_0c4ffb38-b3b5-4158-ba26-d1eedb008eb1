import { test } from "vitest";
import { motlLlm } from "./motlLlm";
import z from "zod";
import { err, ok } from "neverthrow";

test("generateObjectWithValidation", async () => {
  await motlLlm
    .generateObjectWithValidation({
      aiModel: "gpt-4.1-mini",
      schema: z.object({
        name: z.string(),
      }),
      messages: [
        {
          role: "user",
          content: "What is the capital of France?",
        },
      ],
      validateAndTransform: (data) => {
        return err({
          type: "FirstError",
          message: "thats not the capital of France",
        });
      },
      maxRetries: 3,
    })
    .then((res) => {
      if (res.isOk()) {
        console.log(res.value);
      } else {
        console.log(res.error);
      }
    });
});
