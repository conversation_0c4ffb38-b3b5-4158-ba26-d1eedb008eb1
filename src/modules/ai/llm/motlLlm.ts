import { z, ZodTypeAny } from "zod";
import { ChatCompletionMessageParam } from "openai/resources/chat/completions.mjs";
import { braintrust } from "../../tracing/braintrust";
import { llm, AIModelId } from "./llm";
import { nvUtils } from "../../core/utils/nvUtils";
import { ok, err, Result, ResultAsync } from "neverthrow";
import { Span } from "braintrust";

export class LLMValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "LLMValidationError";
  }
}

export namespace motlLlm {
  /**
   * Generic function to generate structured output with validation and retry logic.
   * This function handles the common pattern of:
   * 1. Generating structured output from LLM
   * 2. Validating and transforming the result
   * 3. Retrying with error feedback if validation fails
   * 4. Proper error handling with neverthrow
   */
  export async function generateObjectWithValidation<TSchema extends ZodTypeAny, TTransformedResult>(args: {
    /** The AI model to use for generation */
    aiModel: AIModelId;
    /** Zod schema for validation */
    schema: TSchema;
    /** Initial messages for the LLM conversation */
    messages: ChatCompletionMessageParam[];

    /** Maximum number of retry attempts (default: 3) */
    maxRetries?: number;
    /** Optional braintrust span for tracing */
    span?: Span;
    /** Optional seed for deterministic generation */
    seed?: number;
    /** Trace name for braintrust (default: "generateObjectWithValidation") */
    traceName?: string;
    validateAndTransform: (
      data: z.infer<TSchema>,
      retry: number
    ) => Result<
      TTransformedResult,
      {
        message: string;
      }
    >;
  }): Promise<Result<TTransformedResult, unknown>> {
    const { aiModel, schema, messages: initialMessages, maxRetries = 3, span, seed = 1, traceName = "generateObjectWithValidation" } = args;

    const res = await braintrust.trace(traceName, async (traceSpan) => {
      const activeSpan = span || traceSpan;
      let messages = [...initialMessages];

      const res = await nvUtils.retry({ maxRetries }, async (retry) => {
        const llmRes = await llm.generateStructuredOutput({
          seed,
          span: activeSpan,
          model: aiModel,
          schema,
          messages,
        });

        if (llmRes.isErr()) {
          return err(llmRes.error);
        }

        // Validate and transform the data
        const validationResult = args.validateAndTransform(llmRes.value.object, retry);

        if (validationResult.isOk()) {
          return ok(validationResult.value);
        }

        // Add the LLM response and error feedback to messages for retry
        messages.push({
          role: "assistant",
          content: llmRes.value.choices[0].message.content!,
        });
        messages.push({
          role: "user",
          content: `The previous response had an error:\n\n"${validationResult.error.message}". \n\n Please try again.`,
        });

        return validationResult;
      });

      if (res.isErr()) {
        traceSpan.log({
          error: res.error,
        });
      } else {
        traceSpan.log({
          output: res.value,
        });
      }

      return res;
    });

    return res;
  }

  /**
   * Helper function to create a validation function that throws ValidationError
   * for common validation patterns.
   */
  export function createSmartPrompt<T, TTransformed>(args: {
    validation: (data: T) => TTransformed;
  }): (data: T) => { transformed: TTransformed; success: true } | { success: false; error: string } {
    return (data: T) => {
      try {
        const transformed = args.validation(data);
        return {
          success: true,
          transformed,
        };
      } catch (error) {
        if (error instanceof LLMValidationError) {
          return {
            success: false,
            error: error.message,
          };
        }
        throw error;
      }
    };
  }
}
