import "dotenv/config";
import { generateObject, generateText } from "ai";
import { openai } from "@ai-sdk/openai";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { z } from "zod";
import { expect, test } from "vitest";
import { envVars } from "@/envVars";
import { braintrust } from "@/modules/tracing/braintrust";
import { initLogger, wrapOpenAI } from "braintrust";
import OpenAI from "openai";
import { llm } from "./llm";
import { MotlFile } from "@/modules/core/motlFile";
import { pdfPlumberApi } from "@/modules/pdfParser/pdfPlumberApi";
import { zodUtils } from "@/modules/core/utils/zodUtils";
import { json } from "@/modules/core/utils/jsonUtils";

test("generate object", async () => {
  const { object } = await generateObject({
    model: openai("gpt-4.1-nano"),
    schema: z.object({
      recipe: z.object({
        name: z.string(),
        ingredients: z.array(z.object({ name: z.string(), amount: z.string() })),
        steps: z.array(z.string()),
      }),
    }),
    prompt: "Generate a lasagna recipe.",
  });
  console.log("receipe", object);
});
test("structured reasoning", async () => {
  await braintrust.trace("structured reasoning test", async (span) => {
    const fileUrl = "https://pub-da9a66ddb6b5467c965f355f1bbe927d.r2.dev/774406c5df53de48bc0ecdad7b812418c739f072c89502b24cf8744d83696f64";

    const invoiceBaseDetailsSchema = z.object({
      invoiceDate: z.string().describe("YYYY-MM-DD"),

      issuer: z.string(),
    });
    const file = await MotlFile.parse(fileUrl);
    const res = await llm.generateStructuredOutput({
      model: "o4-mini",
      schema: invoiceBaseDetailsSchema,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Can you extract the details like in the given json schema from the invoice?`,
            },
            {
              type: "file",
              file: {
                file_data: await file.getBase64FileUrl(),
                filename: "part-1.pdf",
              },
            },
          ],
        },
      ],
    });
    res
      .map((res) => {
        console.log("success", res.object);
      })
      .mapErr((err) => {
        console.log("error", err);
      });
  });
});

test("structured reasoning complex output", async () => {
  await braintrust.trace("structured reasoning complex output", async (span) => {
    const fileUrl = "https://pub-da9a66ddb6b5467c965f355f1bbe927d.r2.dev/774406c5df53de48bc0ecdad7b812418c739f072c89502b24cf8744d83696f64";

    const addressSchema = z.object({
      country: zodUtils.optional(z.string()),
      street: z.string(),
      houseNumber: zodUtils.optional(z.string()),
      city: z.string(),
      postalCode: z.string(),
    });

    const invoicePartySchema = z.object({
      companyName: z.string(),
      uidNumber: zodUtils.optional(z.string()),
      email: zodUtils.optional(z.string()),
      phone: zodUtils.optional(z.string()),
      address: addressSchema,
      contactPerson: zodUtils.optional(z.string()),
    });

    const invoiceBaseDetailsSchema = z.object({
      invoiceDate: z.string().describe("YYYY-MM-DD"),
      invoiceReference: z.string(),
      issuer: invoicePartySchema,
      billedTo: invoicePartySchema,
    });
    const file = await MotlFile.parse(fileUrl);
    const res = await llm.generateStructuredOutput({
      model: "o4-mini",
      schema: invoiceBaseDetailsSchema,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Can you extract the details like in the given json schema from the invoice?`,
            },
            {
              type: "file",
              file: {
                file_data: await file.getBase64FileUrl(),
                filename: "part-1.pdf",
              },
            },
          ],
        },
      ],
    });
    res
      .map((res) => {
        console.log("success", res.object);
      })
      .mapErr((err) => {
        console.log("error", err);
      });
  });
  const logger = await braintrust.getLogger();
  await logger.flush();
});

test("generate object gemini", async () => {
  const logger = await initLogger({
    projectName: "albert",
    apiKey: envVars.BRAINTRUST_API_KEY,
  });

  const traceRes = await logger.traced(async (span) => {
    const openrouter = createOpenRouter({
      apiKey: envVars.OPENROUTER_API_KEY,
    });
    const zodSchema = z.object({
      recipe: z.object({
        name: z.string(),
        ingredients: z.array(z.object({ name: z.string(), amount: z.string() })),
        steps: z.array(z.string()),
      }),
    });
    const llmRes = await generateText({
      // model: openrouter("google/gemini-2.5-flash-preview-05-20"),
      model: openrouter("gpt-4.1-mini"),
      // schema: zodSchema,
      prompt: "Generate a lasagna recipe.",
      headers: {
        "x-bt-parent": await span.export(),
      },
      experimental_telemetry: {
        isEnabled: true,
      },
    }).catch((error) => {
      console.error("Error generating object:", error);
      throw error;
    });
    console.log("llmRes", llmRes);
  });
});

test("llm.generateStructuredOutput image", async () => {
  const fileUrl = "https://pub-da9a66ddb6b5467c965f355f1bbe927d.r2.dev/774406c5df53de48bc0ecdad7b812418c739f072c89502b24cf8744d83696f64";

  const file = await MotlFile.parse(fileUrl);

  const pdfPlumberRes = await pdfPlumberApi.parse(file);

  if (!pdfPlumberRes) {
    console.error("Error parsing pdf:", pdfPlumberRes);
    return;
  }

  const res = await llm.generateStructuredOutput({
    model: "gpt-4.1-mini",
    schema: z.object({
      issuer: z.object({
        name: z.string(),
        address: z.string(),
      }),
    }),
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: `Can you extract the issuer name and address from the following invoice? Invoice: ${pdfPlumberRes}`,
          },
          {
            type: "file",
            file: {
              file_data: await file.getBase64FileUrl(),
              filename: "part-1.pdf",
            },
          },
        ],
      },
    ],
  });

  if (res.isErr()) {
    console.error("Error generating object:", res.error);
    return;
  }

  console.log("res", res.value.object);

  console.log("usage", res.value.usage);
});

test("braintrust image playground", async () => {
  await braintrust.trace("braintrust image playground", async (span) => {
    const llm = wrapOpenAI(
      new OpenAI({
        baseURL: envVars.MOTL_CACHER_URL,
        defaultHeaders: {
          "x-api-key": envVars.MOTL_CACHER_API_KEY,
          "x-upstream-url": "https://api.openai.com/v1",
          "x-bt-parent": await span.export(),
        },
      })
    );

    const file = await MotlFile.parse("https://pub-da9a66ddb6b5467c965f355f1bbe927d.r2.dev/774406c5df53de48bc0ecdad7b812418c739f072c89502b24cf8744d83696f64");


    const res = await llm.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: [
        {
          role: "user",
          content: "What is the capital of France?",
        },
        {
          role: "user",
          content: [
            {
              type: "file",
              file: {
                filename: "part-1.pdf",
                file_data: await file.getBase64FileUrl(),
              },
            },
          ],
        },
      ],
    });

    console.log("res", json.prettyPrint(res));
  });

  await braintrust.getLogger().then((logger) => logger.flush());
});
