import "dotenv/config"; // Load environment variables from .env file
import { envVars } from "@/envVars";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import { initLogger } from "braintrust";
import z from "zod";
import { createOpenAI, openai } from "@ai-sdk/openai";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { context, trace } from "@opentelemetry/api";

export namespace aiSdk {
  export const openAi = createOpenAI({
    apiKey: envVars.BRAINTRUST_API_KEY,
    baseURL: "https://api.braintrust.dev/v1/proxy",
  });
}

async function main() {
  // const logger = await initLogger({
  //   projectName: "albert",
  //   apiKey: envVars.BRAINTRUST_API_KEY,
  // });

  // const traceRes = await logger.traced(async (span) => {
  const openrouter = createOpenRouter({
    baseURL: "https://api.braintrust.dev/v1/proxy",
    apiKey: envVars.OPENROUTER_API_KEY,
  });

  const googlAi = createGoogleGenerativeAI({
    apiKey: envVars.BRAINTRUST_API_KEY,
    baseURL: "https://api.braintrust.dev/v1/proxy",
  });

  const zodSchema = z.object({
    recipe: z.object({
      name: z.string(),
      ingredients: z.array(z.object({ name: z.string(), amount: z.string() })),
      steps: z.array(z.string()),
    }),
  });
  const llmRes = await generateText({
    // model: openrouter("google/gemini-2.5-flash-preview-05-20"),
    // model: openAi.languageModel("gpt-4.1-mini"),
    model: googlAi.languageModel("gemini-2.5-flash-preview-04-17"),
    // schema: zodSchema,
    prompt: "Generate a lasagna recipe.",
    headers: {
      "x-bt-parent": "project_name:albert",
    },
    seed: 1,
  }).catch((error) => {
    console.error("Error generating object:", error);
    throw error;
  });
  console.log("llmRes", llmRes);
  // });
}
