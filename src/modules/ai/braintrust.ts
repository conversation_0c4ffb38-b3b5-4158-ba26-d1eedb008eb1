import "dotenv/config";
import { envVars } from "@/envVars";
import { Attachment, initLogger, traced, wrapAISDKModel, wrapOpenAI } from "braintrust";
import { OpenAI } from "openai";
import { MotlFile } from "../core/motlFile";
import path from "path";
import { openai } from "@ai-sdk/openai";
import { generateText } from "ai";

// const openai = wrapOpenAI(new OpenAI({ apiKey: env.OPENAI_API_KEY }));
// const openai = new OpenAI({ apiKey: env.OPENAI_API_KEY });
const openAiModel = wrapAISDKModel(openai("gpt-4o"));

async function main() {
  const file = await MotlFile.parse(path.join(__dirname, "data", "FI20971070544.pdf"));

  const logger = await initLogger({
    projectName: "Invoice Parsing",
    apiKey: envVars.BRAINTRUST_API_KEY,
  });

  const completionRes = await traced(
    async (span) => {
      span.log({
        input: {
          context: new Attachment({
            filename: "invoice.pdf",
            data: (() => {
              const arrayBuffer = new ArrayBuffer(file.buffer.length);
              const uint8Array = new Uint8Array(arrayBuffer);
              uint8Array.set(file.buffer);
              return arrayBuffer;
            })(),
            contentType: file.mimeType,
          }),
        },
      });
      return generateText({
        model: openAiModel,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `Extract the invoice data from the given invoice 3`,
              },
              {
                type: "file",
                mimeType: file.mimeType,
                data: file.buffer,
              },
            ],
          },
        ],
      });
    },
    {
      name: "extract invoices",
    }
  );

  console.log("Completion response:", completionRes);
}

async function proxy() {
  const client = new OpenAI({
    baseURL: "https://api.braintrust.dev/v1/proxy",
    apiKey: envVars.BRAINTRUST_API_KEY, // Can use Braintrust, Anthropic, etc. API keys here
  });

  async function main() {
    const start = performance.now();
    const response = await client.chat.completions.create({
      // model: "gpt-4o-mini", // Can use claude-3-5-sonnet-latest, gemini-2.0-flash, etc. here
      model: "gemini-2.5-flash-preview-04-17",
      messages: [{ role: "user", content: "what llm are you?" }],
      response_format: {
        type: "json_schema",
        json_schema: {
          name: "answer",
          schema: {
            type: "object",
            title: "Answer",
            description: "A simple answer to the question",
            properties: {
              answer: {
                type: "string",
                description: "The answer to the question",
              },
            },
            required: ["answer"],
          },
        },
      },

      seed: 2, // A seed activates the proxy's cache
    });
    console.log(response.choices[0].message.content);
    const createdAt = new Date(response.created * 1000);
    console.log(`Response created at: ${createdAt.toISOString()}`);
    console.log(`Took ${(performance.now() - start) / 1000}s`);
  }
  main();
}

proxy();
