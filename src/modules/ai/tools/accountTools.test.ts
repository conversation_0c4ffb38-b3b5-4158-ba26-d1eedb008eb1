import { describe, test, expect, beforeEach, vi } from "vitest";
import { AccountType } from "@/prisma/generated";

// Mock the prisma import
vi.mock("@/prisma/prisma", () => ({
  prisma: {
    account: {
      create: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
      groupBy: vi.fn(),
    },
  },
}));

// Import after mocking
const { accountTools } = await import("./accountTools");
const { prisma } = await import("@/prisma/prisma");

describe("accountTools", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("createAccounts", () => {
    test("should create multiple accounts successfully", async () => {
      const mockAccounts = [
        {
          id: "test-id-1",
          name: "Test Wallet",
          type: AccountType.WALLET,
          createdAt: new Date("2024-01-01"),
          _count: { transactions: 0 },
        },
        {
          id: "test-id-2",
          name: "Test Bank Account",
          type: AccountType.BANK_ACCOUNT,
          createdAt: new Date("2024-01-01"),
          _count: { transactions: 0 },
        }
      ];

      vi.mocked(prisma.account.create)
        .mockResolvedValueOnce(mockAccounts[0])
        .mockResolvedValueOnce(mockAccounts[1]);

      const result = await accountTools.createAccounts.execute({
        accounts: [
          { name: "Test Wallet", type: AccountType.WALLET },
          { name: "Test Bank Account", type: AccountType.BANK_ACCOUNT }
        ]
      });

      expect(result.success).toBe(true);
      expect(result.message).toContain("Successfully created all 2 accounts");
      expect(result.summary.totalRequested).toBe(2);
      expect(result.summary.successCount).toBe(2);
      expect(result.summary.failureCount).toBe(0);
      expect(result.results).toHaveLength(2);
      expect(result.results[0].success).toBe(true);
      expect(result.results[1].success).toBe(true);
    });

    test("should handle partial success when some accounts fail", async () => {
      const mockAccount = {
        id: "test-id-1",
        name: "Test Wallet",
        type: AccountType.WALLET,
        createdAt: new Date("2024-01-01"),
        _count: { transactions: 0 },
      };

      vi.mocked(prisma.account.create)
        .mockResolvedValueOnce(mockAccount)
        .mockRejectedValueOnce(new Error("Unique constraint violation"));

      const result = await accountTools.createAccounts.execute({
        accounts: [
          { name: "Test Wallet", type: AccountType.WALLET },
          { name: "Duplicate Name", type: AccountType.BANK_ACCOUNT }
        ]
      });

      expect(result.success).toBe(true); // Partial success
      expect(result.message).toContain("Created 1 of 2 accounts (1 failed)");
      expect(result.summary.totalRequested).toBe(2);
      expect(result.summary.successCount).toBe(1);
      expect(result.summary.failureCount).toBe(1);
      expect(result.summary.hasPartialSuccess).toBe(true);
      expect(result.results).toHaveLength(2);
      expect(result.results[0].success).toBe(true);
      expect(result.results[1].success).toBe(false);
      expect(result.errors).toBeDefined();
    });

    test("should return error for invalid input", async () => {
      const result = await accountTools.createAccounts.execute({
        accounts: [] // Invalid empty array
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid input parameters");
      expect(result.details).toContain("At least one account is required");
    });
  });

  describe("createAccount", () => {
    test("should create account successfully with valid input", async () => {
      const mockAccount = {
        id: "test-id",
        name: "Test Account",
        type: AccountType.WALLET,
        createdAt: new Date("2024-01-01"),
        _count: { transactions: 0 },
      };

      vi.mocked(prisma.account.create).mockResolvedValue(mockAccount);

      const result = await accountTools.createAccount.execute({
        name: "Test Account",
        type: AccountType.WALLET,
      });

      expect(result.success).toBe(true);
      expect(result.message).toContain("Successfully created wallet account");
      expect(result.account).toEqual({
        id: "test-id",
        name: "Test Account",
        type: AccountType.WALLET,
        createdAt: "2024-01-01T00:00:00.000Z",
        transactionCount: 0,
      });

      expect(prisma.account.create).toHaveBeenCalledWith({
        data: {
          name: "Test Account",
          type: AccountType.WALLET,
        },
        include: {
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });
    });

    test("should return error for invalid input", async () => {
      const result = await accountTools.createAccount.execute({
        name: "", // Invalid empty name
        type: AccountType.WALLET,
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid input parameters");
      expect(result.details).toContain("name");
    });

    test("should handle database errors", async () => {
      vi.mocked(prisma.account.create).mockRejectedValue(new Error("Database error"));

      const result = await accountTools.createAccount.execute({
        name: "Test Account",
        type: AccountType.WALLET,
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe("Failed to create account");
      expect(result.details).toContain("Database error");
    });
  });

  describe("getAccounts", () => {
    test("should return accounts successfully", async () => {
      const mockAccounts = [
        {
          id: "account-1",
          name: "Account 1",
          type: AccountType.WALLET,
          createdAt: new Date("2024-01-01"),
          _count: { transactions: 5 },
        },
        {
          id: "account-2",
          name: "Account 2",
          type: AccountType.BANK_ACCOUNT,
          createdAt: new Date("2024-01-02"),
          _count: { transactions: 10 },
        },
      ];

      vi.mocked(prisma.account.findMany).mockResolvedValue(mockAccounts);

      const result = await accountTools.getAccounts.execute({ limit: 20 });

      expect(result.success).toBe(true);
      expect(result.count).toBe(2);
      expect(result.accounts).toHaveLength(2);
      expect(result.accounts[0]).toEqual({
        id: "account-1",
        name: "Account 1",
        type: AccountType.WALLET,
        createdAt: "2024-01-01",
        transactionCount: 5,
      });
    });

    test("should filter by account type", async () => {
      vi.mocked(prisma.account.findMany).mockResolvedValue([]);

      await accountTools.getAccounts.execute({
        limit: 20,
        type: "WALLET"
      });

      expect(prisma.account.findMany).toHaveBeenCalledWith({
        where: { type: "WALLET" },
        include: {
          _count: {
            select: {
              transactions: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 20,
      });
    });
  });

  describe("getAccountStats", () => {
    test("should return account statistics", async () => {
      vi.mocked(prisma.account.count).mockResolvedValue(10);
      vi.mocked(prisma.account.groupBy).mockResolvedValue([
        { type: AccountType.WALLET, _count: { id: 5 } },
        { type: AccountType.BANK_ACCOUNT, _count: { id: 3 } },
        { type: AccountType.EXCHANGE_ACCOUNT, _count: { id: 2 } },
      ]);

      const result = await accountTools.getAccountStats.execute();

      expect(result.success).toBe(true);
      expect(result.totalAccounts).toBe(10);
      expect(result.accountsByType).toEqual({
        [AccountType.WALLET]: 5,
        [AccountType.BANK_ACCOUNT]: 3,
        [AccountType.EXCHANGE_ACCOUNT]: 2,
      });
      expect(result.availableTypes).toBeDefined();
      expect(result.availableTypes).toHaveLength(4);
    });
  });
});
