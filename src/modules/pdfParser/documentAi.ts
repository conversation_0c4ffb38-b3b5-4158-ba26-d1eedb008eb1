import { err, ok, ResultAsync } from "neverthrow";
import { mistral } from "../ai/mistral";
import { pdfPlumberApi } from "./pdfPlumber<PERSON>pi";
import { MotlFile } from "../core/motlFile";
import path from "path";

export namespace documentAI {
  export function parseDocument(args: { file: MotlFile }) {
    return ResultAsync.fromPromise(
      (async () => {
        let parsedTextRes = await pdfPlumberApi.parse(args.file);

        let parsedText = "";

        if (parsedTextRes.isOk() && parsedTextRes.value.content) {
          parsedText = parsedTextRes.value.content;
        } else {
          const ocrRes = await mistral.ocrDocument({ file: args.file.buffer });

          if (ocrRes.isErr()) {
            return err(ocrRes.error);
          }

          parsedText = ocrRes.value.text;
        }

        return ok({
          parsedText,
        });
      })(),
      (err) => {
        return err;
      }
    ).andThen((res) => res);
  }
}

async function main() {
  const file = await MotlFile.parse(path.join(__dirname, "data", "slack_invoice_SBIE-8692888.pdf-attachment.pdf"));

  const wrappedRes = await documentAI.parseDocument({ file });

  console.log(wrappedRes);
}
if (require.main === module) {
  main();
}
