import { GenericFile, MotlFile } from "../core/motlFile";
import path from "path";
import ky from "ky";
import { Result, ok, err, ResultAsync, Ok } from "neverthrow";
import { measureTime } from "@/utils";
import { envVars } from "@/envVars";
import { createMotlCacher } from "../core/motlCacher";

const motlCacher = createMotlCacher();

export namespace pdfPlumberApi {
  export async function parse(genericFile: GenericFile) {
    try {
      const file = await MotlFile.parse(genericFile);
      const fileSizeMb = file.getMbSize();

      // console.log(`Attempting to parse PDF file: ${file.fileName} (${fileSizeMb.toFixed(2)} MB)`);

      const base64 = await file.getBase64();

      const res = await motlCacher
        .post("https://pdfplumber-serverless.vercel.app/api/pdf-plumber", {
          headers: {
            "Content-Type": "application/json",
          },
          json: {
            base64_pdf: base64,
          },
          timeout: 60000, // 60 seconds timeout
        })
        .catch((error) => {
          console.log(`Error during PDF parsing request`, {
            error,
            fileName: file.fileName,
            fileSizeMb: fileSizeMb.toFixed(2),
          });
          throw error;
        });

      const resBody = await res.json<{ content: string }>();
      return ok({ content: resBody.content, res });
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));

      // Try to get file info for logging even if parsing failed
      try {
        const file = await MotlFile.parse(genericFile);
        const fileSizeMb = file.getMbSize();
        console.error(`Failed to parse PDF file: ${file.fileName} (${fileSizeMb.toFixed(2)} MB)`, error);
      } catch {
        console.error(`Failed to parse PDF file (unable to determine size)`, error);
      }

      return err(error);
    }
  }
}

async function test() {
  try {
    const dataPath = path.join(__dirname, "data", "slack_invoice_SBIE-8692888.pdf-attachment.pdf");

    await measureTime("parse", async () => {
      const result = await pdfPlumberApi.parse(await MotlFile.parse(dataPath));

      if (result.isOk()) {
        console.log("Success:", result.value.content);
        console.log("cache hit:", result.value.res.headers.get("x-edge-cache"));
      } else {
        console.error("Error:", result.error.message);
      }
    });
  } catch (e) {
    console.error("Error:", e);
  }

  console.log("done");
}

if (require.main === module && module.filename.endsWith(".ts")) {
  test();
}
