import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { AccountType } from "@/prisma/generated";

// Validation for Solana public key format
const solanaPublicKeyRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;

// Input validation schemas
const createAccountSchema = z.object({
  name: z.string().min(1, "Account name is required").max(255, "Account name too long"),
  type: z.nativeEnum(AccountType),
  publicKey: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      return solanaPublicKeyRegex.test(val);
    }, "Invalid public key format")
    .transform((val) => val || undefined), // Convert empty string to undefined
});

const updateAccountSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Account name is required").max(255, "Account name too long").optional(),
  type: z.nativeEnum(AccountType).optional(),
  publicKey: z
    .string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      return solanaPublicKeyRegex.test(val);
    }, "Invalid public key format")
    .transform((val) => val || undefined), // Convert empty string to undefined
});

export const accountRouter = createTRPCRouter({
  getAll: publicProcedure
    .input(
      z
        .object({
          limit: z.number().min(1).max(500).default(500),
          cursor: z.string().optional(),
          type: z.nativeEnum(AccountType).optional(),
        })
        .optional()
        .default({})
    )
    .query(async ({ input, ctx }) => {
      try {
        const limit = input?.limit ?? 500;
        const cursor = input?.cursor;
        const type = input?.type;

        const accounts = await ctx.prisma.account.findMany({
          take: limit + 1, // Take one extra to check if there's a next page
          cursor: cursor ? { id: cursor } : undefined,
          where: type ? { type } : undefined,
          orderBy: { name: "asc" },
          include: {
            transactions: {
              select: {
                id: true,
                executedAt: true,
              },
              orderBy: { executedAt: "desc" },
              take: 5, // Only get the latest 5 transactions for preview
            },
            _count: {
              select: {
                transactions: true,
              },
            },
          },
        });

        let nextCursor: string | undefined = undefined;
        if (accounts.length > limit) {
          const nextItem = accounts.pop(); // Remove the extra item
          nextCursor = nextItem!.id;
        }

        return {
          items: accounts,
          nextCursor,
        };
      } catch (error) {
        console.error("Failed to fetch accounts:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch accounts",
          cause: error,
        });
      }
    }),

  getById: publicProcedure.input(z.object({ id: z.string() })).query(async ({ input, ctx }) => {
    try {
      const account = await ctx.prisma.account.findUnique({
        where: { id: input.id },
        include: {
          transactions: {
            include: {
              transfer: true,
              trade: true,
            },
            orderBy: { executedAt: "desc" },
          },
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      if (!account) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Account not found",
        });
      }

      return account;
    } catch (error) {
      console.error("Failed to fetch account:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch account",
        cause: error,
      });
    }
  }),

  create: publicProcedure.input(createAccountSchema).mutation(async ({ input, ctx }) => {
    try {
      // Validate that publicKey is provided for WALLET type
      if (input.type === "WALLET" && !input.publicKey) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Public key is required for wallet accounts",
        });
      }

      // Check for duplicate public key if provided
      if (input.publicKey) {
        const existingAccount = await ctx.prisma.account.findFirst({
          where: { publicKey: input.publicKey },
        });

        if (existingAccount) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "An account with this public key already exists",
          });
        }
      }

      const account = await ctx.prisma.account.create({
        data: {
          name: input.name,
          type: input.type,
          publicKey: input.publicKey,
        },
        include: {
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      return account;
    } catch (error) {
      console.error("Failed to create account:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to create account",
        cause: error,
      });
    }
  }),

  update: publicProcedure.input(updateAccountSchema).mutation(async ({ input, ctx }) => {
    try {
      const { id, ...updateData } = input;

      // Check if account exists
      const existingAccount = await ctx.prisma.account.findUnique({
        where: { id },
      });

      if (!existingAccount) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Account not found",
        });
      }

      // Validate that publicKey is provided for WALLET type
      const newType = updateData.type || existingAccount.type;
      if (newType === "WALLET" && !updateData.publicKey && !existingAccount.publicKey) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Public key is required for wallet accounts",
        });
      }

      // Check for duplicate public key if provided and different from current
      if (updateData.publicKey && updateData.publicKey !== existingAccount.publicKey) {
        const duplicateAccount = await ctx.prisma.account.findFirst({
          where: {
            publicKey: updateData.publicKey,
            id: { not: id }, // Exclude current account
          },
        });

        if (duplicateAccount) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "An account with this public key already exists",
          });
        }
      }

      const account = await ctx.prisma.account.update({
        where: { id },
        data: updateData,
        include: {
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      return account;
    } catch (error) {
      console.error("Failed to update account:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to update account",
        cause: error,
      });
    }
  }),

  delete: publicProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    try {
      // Check if account exists
      const existingAccount = await ctx.prisma.account.findUnique({
        where: { id: input.id },
        include: {
          _count: {
            select: {
              transactions: true,
            },
          },
        },
      });

      if (!existingAccount) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Account not found",
        });
      }

      // // Check if account has transactions
      // if (existingAccount._count.transactions > 0) {
      //   throw new TRPCError({
      //     code: "BAD_REQUEST",
      //     message: "Cannot delete account with existing transactions",
      //   });
      // }

      await ctx.prisma.account.delete({
        where: { id: input.id },
      });

      return { success: true };
    } catch (error) {
      console.error("Failed to delete account:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete account",
        cause: error,
      });
    }
  }),

  getStats: publicProcedure.query(async ({ ctx }) => {
    try {
      const [total, byType, withTransactions] = await Promise.all([
        ctx.prisma.account.count(),
        ctx.prisma.account.groupBy({
          by: ["type"],
          _count: {
            id: true,
          },
        }),
        ctx.prisma.account.count({
          where: {
            transactions: {
              some: {},
            },
          },
        }),
      ]);

      // Transform byType to a more usable format
      const typeStats = byType.reduce((acc, item) => {
        acc[item.type] = item._count.id;
        return acc;
      }, {} as Record<AccountType, number>);

      return {
        total,
        byType: typeStats,
        withTransactions,
        withoutTransactions: total - withTransactions,
      };
    } catch (error) {
      console.error("Failed to fetch account stats:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch account stats",
        cause: error,
      });
    }
  }),

  bulkDelete: publicProcedure
    .input(
      z.object({
        type: z.nativeEnum(AccountType).optional(),
        excludeWallets: z.boolean().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Build the where condition based on filters
        let whereCondition: any = {};

        if (input.type) {
          whereCondition.type = input.type;
        }

        if (input.excludeWallets) {
          whereCondition.type = {
            not: "WALLET",
          };
        }

        // Delete accounts matching the filters
        const deleteResult = await ctx.prisma.account.deleteMany({
          where: whereCondition,
        });

        return {
          deletedCount: deleteResult.count,
        };
      } catch (error) {
        console.error("Failed to bulk delete accounts:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to bulk delete accounts",
          cause: error,
        });
      }
    }),
});
