#!/usr/bin/env tsx
import "dotenv/config";

/**
 * Flipside CLI for testing and running queries
 *
 * Usage:
 * pnpm tsx src/modules/flipside/cli.ts test
 * pnpm tsx src/modules/flipside/cli.ts demo
 * pnpm tsx src/modules/flipside/cli.ts query "SELECT 1 as test"
 */

import { FlipsideTest } from "./flipsideTest";
import { flipsideClient, FlipsideUtils } from "./flipsideClient";
import { FlipsideQueries } from "./flipsideQueries";

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command) {
    console.log(`
🚀 Flipside CLI

Available commands:
  test     - Run all integration tests
  demo     - Run demo queries
  health   - Check Flipside connection
  query    - Execute custom SQL query (with neverthrow error handling)
  simple   - Execute custom SQL query (direct result, throws on error)
  eth      - Get Ethereum metrics
  gas      - Get gas price trends
  tokens   - Get top tokens by volume

Examples:
  pnpm tsx src/modules/flipside/cli.ts test
  pnpm tsx src/modules/flipside/cli.ts demo
  pnpm tsx src/modules/flipside/cli.ts query "SELECT 1 as test"
  pnpm tsx src/modules/flipside/cli.ts simple "SELECT 1 as test"
  pnpm tsx src/modules/flipside/cli.ts eth 7
    `);
    return;
  }

  try {
    switch (command) {
      case "test":
        await FlipsideTest.runAllTests();
        break;

      case "demo":
        await FlipsideTest.runDemo();
        break;

      case "health":
        await testHealth();
        break;

      case "query":
        const sql = args[1];
        if (!sql) {
          console.error("❌ Please provide a SQL query");
          return;
        }
        await executeCustomQuery(sql);
        break;

      case "eth":
        const days = parseInt(args[1]) || 7;
        await getEthereumMetrics(days);
        break;

      case "gas":
        const gasDays = parseInt(args[1]) || 1;
        await getGasPrices(gasDays);
        break;

      case "tokens":
        const limit = parseInt(args[1]) || 10;
        const tokenDays = parseInt(args[2]) || 7;
        await getTopTokens(limit, tokenDays);
        break;

      case "simple":
        const simpleSql = args[1];
        if (!simpleSql) {
          console.error("❌ Please provide a SQL query");
          return;
        }
        await executeSimpleQuery(simpleSql);
        break;

      default:
        console.error(`❌ Unknown command: ${command}`);
        break;
    }
  } catch (error) {
    console.error("❌ CLI error:", error);
    process.exit(1);
  }
}

async function testHealth() {
  console.log("🔍 Testing Flipside connection...");

  const result = await flipsideClient.query("SELECT 1 as health_check", {
    maxAgeMinutes: 0,
    timeoutMinutes: 1,
  });

  if (result.isErr()) {
    console.error("❌ Health check failed:", result.error.message);
    return;
  }

  const stats = FlipsideUtils.getQueryStats(result.value);
  console.log("✅ Connection healthy!");
  console.log("📊 Query executed in:", stats.executionSeconds, "seconds");
}

async function executeCustomQuery(sql: string) {
  console.log("🔍 Executing custom query...");
  console.log("📝 SQL:", sql);

  try {
    // Using the new queryDirect method for simpler error handling
    const result = await flipsideClient.queryDirect(sql, {
      maxAgeMinutes: 10,
      timeoutMinutes: 5,
    });

    const records = FlipsideUtils.extractRecords(result);
    const stats = FlipsideUtils.getQueryStats(result);

    console.log("✅ Query completed!");
    console.log("📊 Stats:", stats);
    console.log("📋 Records:", records.length > 0 ? records.slice(0, 5) : "No records");

    if (records.length > 5) {
      console.log(`... and ${records.length - 5} more records`);
    }
  } catch (error) {
    console.error("❌ Query failed:", error instanceof Error ? error.message : String(error));
  }
}

async function executeSimpleQuery(sql: string) {
  console.log("🔍 Executing simple query (direct result)...");
  console.log("📝 SQL:", sql);

  try {
    // Using the new queryRecords method for the simplest possible usage
    const records = await flipsideClient.queryRecords(sql, {
      maxAgeMinutes: 10,
      timeoutMinutes: 5,
    });

    console.log("✅ Query completed!");
    console.log("📋 Records:", records.length > 0 ? records.slice(0, 5) : "No records");

    if (records.length > 5) {
      console.log(`... and ${records.length - 5} more records`);
    }

    // Also demonstrate queryDirect for full result set info
    console.log("\n🔍 Getting full result info...");
    const fullResult = await flipsideClient.queryDirect(sql, {
      maxAgeMinutes: 10,
      timeoutMinutes: 5,
    });
    const stats = FlipsideUtils.getQueryStats(fullResult);
    console.log("📊 Query stats:", stats);
  } catch (error) {
    console.error("❌ Query failed:", error.message);
  }
}

async function getEthereumMetrics(days: number) {
  console.log(`🔍 Getting Ethereum metrics for last ${days} days...`);

  try {
    const metrics = await FlipsideQueries.getEthereumMetrics(days);

    console.log("✅ Ethereum metrics retrieved!");
    console.log("📊 Data:");

    metrics.slice(0, 5).forEach((day) => {
      console.log(`   ${day.date}: ${Number(day.transaction_count).toLocaleString()} txs, ${Number(day.avg_gas_price_gwei).toFixed(2)} gwei avg`);
    });

    if (metrics.length > 5) {
      console.log(`   ... and ${metrics.length - 5} more days`);
    }
  } catch (error) {
    console.error("❌ Failed to get Ethereum metrics:", error);
  }
}

async function getGasPrices(days: number) {
  console.log(`⛽ Getting gas price trends for last ${days} days...`);

  try {
    const trends = await FlipsideQueries.getGasPriceTrends(days);

    console.log("✅ Gas price trends retrieved!");
    console.log("📊 Recent hours:");

    trends.slice(0, 10).forEach((hour) => {
      console.log(`   ${hour.hour}: ${Number(hour.median_gas_price_gwei).toFixed(2)} gwei (median), ${Number(hour.max_gas_price_gwei).toFixed(2)} gwei (max)`);
    });

    if (trends.length > 10) {
      console.log(`   ... and ${trends.length - 10} more hours`);
    }
  } catch (error) {
    console.error("❌ Failed to get gas price trends:", error);
  }
}

async function getTopTokens(limit: number, days: number) {
  console.log(`🏆 Getting top ${limit} tokens by volume (last ${days} days)...`);

  try {
    const tokens = await FlipsideQueries.getTopTokensByVolume(limit, days);

    console.log("✅ Top tokens retrieved!");
    console.log("📊 Rankings:");

    tokens.forEach((token, index) => {
      const volume = Number(token.total_volume_usd);
      const trades = Number(token.transaction_count);
      console.log(`   ${index + 1}. ${token.symbol}: $${volume.toLocaleString()} (${trades.toLocaleString()} trades)`);
    });
  } catch (error) {
    console.error("❌ Failed to get top tokens:", error);
  }
}

// Run the CLI
if (require.main === module) {
  main().catch(console.error);
}
