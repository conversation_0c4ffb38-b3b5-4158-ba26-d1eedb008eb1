/**
 * Flipside Crypto SDK Integration Module
 * 
 * This module provides a comprehensive integration with Flipside Crypto's API
 * for querying blockchain data across multiple chains.
 * 
 * Features:
 * - Type-safe client with neverthrow error handling
 * - tRPC router for API endpoints
 * - Pre-built query templates for common use cases
 * - Pagination and caching support
 * - Comprehensive test suite
 * 
 * Usage:
 * ```typescript
 * import { flipsideClient, FlipsideQueries } from '@/modules/flipside';
 * 
 * // Basic query
 * const result = await flipsideClient.query("SELECT 1 as test");
 * 
 * // Pre-built queries
 * const ethMetrics = await FlipsideQueries.getEthereumMetrics(7);
 * ```
 */

// Core client and utilities
export { 
  FlipsideClient, 
  flipsideClient, 
  FlipsideUtils,
  type FlipsideError,
  type FlipsideQueryOptions 
} from './flipsideClient';

// tRPC router for API endpoints
export { flipsideRouter } from './flipsideRouter';

// Pre-built query templates
export { FlipsideQueries } from './flipsideQueries';

// Test utilities
export { FlipsideTest } from './flipsideTest';

/**
 * Quick start examples and common patterns
 */
export namespace FlipsideExamples {
  
  /**
   * Example: Basic query with error handling
   */
  export const basicQuery = `
    import { flipsideClient } from '@/modules/flipside';
    
    const result = await flipsideClient.query(
      "SELECT count(*) as tx_count FROM ethereum.core.fact_transactions WHERE block_timestamp >= GETDATE() - interval'1 day'",
      { maxAgeMinutes: 30 }
    );
    
    if (result.isErr()) {
      console.error('Query failed:', result.error.message);
      return;
    }
    
    const records = result.value.records;
    console.log('Transaction count:', records[0].tx_count);
  `;

  /**
   * Example: Using pre-built queries
   */
  export const preBuiltQuery = `
    import { FlipsideQueries } from '@/modules/flipside';
    
    // Get Ethereum metrics for the last 7 days
    const metrics = await FlipsideQueries.getEthereumMetrics(7);
    
    // Get gas price trends
    const gasTrends = await FlipsideQueries.getGasPriceTrends(1);
    
    // Get top tokens by volume
    const topTokens = await FlipsideQueries.getTopTokensByVolume(10, 7);
  `;

  /**
   * Example: Using tRPC endpoints
   */
  export const trpcUsage = `
    import { trpc } from '@/utils/trpc';
    
    // Health check
    const health = await trpc.flipside.healthCheck.query();
    
    // Custom query
    const result = await trpc.flipside.query.mutate({
      sql: "SELECT * FROM ethereum.core.fact_transactions LIMIT 10",
      maxAgeMinutes: 60
    });
    
    // Get Ethereum transactions
    const ethTxs = await trpc.flipside.getEthereumTransactions.query({
      days: 7
    });
  `;

  /**
   * Example: Pagination
   */
  export const paginationExample = `
    import { flipsideClient } from '@/modules/flipside';
    
    // Query all pages automatically
    const allPages = await flipsideClient.queryAllPages(
      "SELECT * FROM ethereum.core.fact_transactions WHERE block_timestamp >= GETDATE() - interval'1 hour'"
    );
    
    if (allPages.isOk()) {
      const combinedRecords = FlipsideUtils.combinePageRecords(allPages.value);
      console.log('Total records:', combinedRecords.length);
    }
  `;

  /**
   * Example: Custom SQL with parameters
   */
  export const parameterizedQuery = `
    import { flipsideClient, FlipsideUtils } from '@/modules/flipside';
    
    const walletAddress = '0x...';
    const days = 30;
    
    const sql = FlipsideUtils.formatSql(
      \`SELECT * FROM ethereum.core.fact_transactions 
       WHERE from_address = {address} 
       AND block_timestamp >= GETDATE() - interval'{days} days'\`,
      { address: walletAddress, days }
    );
    
    const result = await flipsideClient.query(sql);
  `;
}

/**
 * Configuration and setup information
 */
export namespace FlipsideConfig {
  export const API_URL = "https://api-v2.flipsidecrypto.xyz";
  export const DEFAULT_TIMEOUT_MINUTES = 15;
  export const DEFAULT_MAX_AGE_MINUTES = 30;
  export const DEFAULT_PAGE_SIZE = 100000;
  
  export const SUPPORTED_CHAINS = [
    'ethereum',
    'polygon',
    'arbitrum',
    'optimism',
    'avalanche',
    'bsc',
    'solana',
    'near',
    'flow',
    'algorand'
  ] as const;
  
  export const COMMON_SCHEMAS = {
    ethereum: ['core', 'defi', 'nft', 'gov'],
    polygon: ['core', 'defi', 'nft'],
    arbitrum: ['core', 'defi'],
    optimism: ['core', 'defi'],
    solana: ['core', 'defi', 'nft'],
  } as const;
}
