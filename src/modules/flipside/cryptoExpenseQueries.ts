import { flipsideClient } from './flipsideClient'
import { Result, ok, err } from 'neverthrow'
import { FlipsideError } from './flipsideClient'
import { getCompanyWallets, getBuybackTokenMints, validateDateRange, IMPORT_CONFIG } from '@/modules/crypto-expenses/config'

/**
 * Crypto expense related queries for Flipside API
 */
export namespace CryptoExpenseQueries {
  /**
   * Raw buyback data structure from Flipside
   */
  export interface FlipsideBuybackRecord {
    block_timestamp: string
    tx_id: string
    swapper: string
    swap_from_symbol: string
    swap_to_symbol: string
    swap_from_amount: number
    swap_to_amount: number
    swap_from_amount_usd: number
    swap_to_amount_usd: number
    swap_from_mint?: string
    swap_to_mint?: string
    amount_usd: number // This is the main amount field from the query
    [key: string]: any // For additional fields
  }

  /**
   * Raw floor sweep data structure from Flipside
   */
  export interface FlipsideFloorSweepRecord {
    block_timestamp: string
    tx_id: string
    buyer_address: string
    collection_id: string
    collection_name?: string
    token_id?: string
    mint_address?: string
    price_usd: number
    amount_usd: number // This is the main amount field from the query (same as price_usd)
    [key: string]: any // For additional fields
  }

  /**
   * Raw liquidity pool action data structure from Flipside
   */
  export interface FlipsideLiquidityPoolRecord {
    block_timestamp: string
    tx_id: string
    signer: string
    action: string
    pool_address: string
    token_a_mint: string
    token_b_mint: string
    token_a_symbol?: string
    token_b_symbol?: string
    token_a_amount: number
    token_b_amount: number
    token_a_amount_usd: number
    token_b_amount_usd: number // This is the main expense amount
    amount_usd: number // This maps to token_b_amount_usd
    [key: string]: any // For additional fields
  }

  export interface IFlipsideTransferRecord {
    tx_from: string
    tx_to: string
    token: string
    amount: number
    price_usd: number
    usd_value: number
    block_timestamp: string
    tx_id: string
    [key: string]: any // For additional fields
  }

  /**
   * Get buyback transactions from Solana DEX swaps
   */
  export async function getBuybacks(
    companyWallets: string[] = getCompanyWallets(),
    targetMints: string[] = getBuybackTokenMints(),
    fromDate?: Date,
    toDate?: Date,
  ): Promise<Result<FlipsideBuybackRecord[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (companyWallets.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    const mintList = targetMints.map((mint) => `'${mint}'`).join(', ')
    const walletList = companyWallets.map((wallet) => `'${wallet}'`).join(', ')

    let dateFilter = ''
    if (fromDate) {
      dateFilter += ` AND block_timestamp >= '${fromDate.toISOString()}'`
    }
    if (toDate) {
      dateFilter += ` AND block_timestamp <= '${toDate.toISOString()}'`
    }

    const sql = `
      SELECT
        swap_from_amount_usd as amount_usd,
        block_timestamp,
        tx_id,
        swapper,
        swap_from_symbol,
        swap_to_symbol,
        swap_from_amount,
        swap_to_amount,
        swap_from_amount_usd,
        swap_to_amount_usd,
        swap_from_mint,
        swap_to_mint
      FROM
        solana.defi.ez_dex_swaps
      WHERE
        swap_to_mint in (${mintList})
        and swapper in (${walletList})
        ${dateFilter}
      ORDER BY
        block_timestamp DESC;
    `

    const result = await flipsideClient.query(sql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as FlipsideBuybackRecord[]
    return ok(records)
  }

  /**
   * Get buybacks for a specific date range with accounting period grouping
   */
  export async function getBuybacksWithAccountingPeriod(
    companyWallets: string[] = getCompanyWallets(),
    targetMints: string[] = getBuybackTokenMints(),
    fromDate?: Date,
    toDate?: Date,
  ): Promise<Result<(FlipsideBuybackRecord & { accounting_period: string })[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (companyWallets.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    const mintList = targetMints.map((mint) => `'${mint}'`).join(', ')
    const walletList = companyWallets.map((wallet) => `'${wallet}'`).join(', ')

    let dateFilter = ''
    if (fromDate) {
      dateFilter += ` AND block_timestamp >= '${fromDate.toISOString()}'`
    }
    if (toDate) {
      dateFilter += ` AND block_timestamp <= '${toDate.toISOString()}'`
    }

    const sql = `
      SELECT
        swap_from_amount_usd as amount_usd,
        block_timestamp,
        tx_id,
        swapper,
        swap_from_symbol,
        swap_to_symbol,
        swap_from_amount,
        swap_to_amount,
        swap_from_amount_usd,
        swap_to_amount_usd,
        swap_from_mint,
        swap_to_mint,
        DATE_TRUNC('month', block_timestamp) as accounting_period
      FROM
        solana.defi.ez_dex_swaps
      WHERE
        swap_to_mint in (${mintList})
        and swapper in (${walletList})
        ${dateFilter}
      ORDER BY
        block_timestamp DESC;
    `

    const result = await flipsideClient.query(sql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as (FlipsideBuybackRecord & { accounting_period: string })[]
    return ok(records)
  }

  /**
   * Get summary statistics for buybacks
   */
  export async function getBuybackSummary(
    companyWallets: string[] = getCompanyWallets(),
    targetMints: string[] = getBuybackTokenMints(),
    fromDate?: Date,
    toDate?: Date,
  ) {
    const result = await getBuybacks(companyWallets, targetMints, fromDate, toDate)

    if (result.isErr()) {
      return result
    }

    const records = result.value
    const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0)
    const totalTransactions = records.length
    const uniqueWallets = new Set(records.map((r) => r.swapper)).size

    return ok({
      totalUsd,
      totalTransactions,
      uniqueWallets,
      dateRange: {
        from: records.length > 0 ? new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
        to: records.length > 0 ? new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
      },
    })
  }

  /**
   * Get floor sweep transactions from Solana NFT sales
   */
  export async function getFloorSweeps(
    companyWallets: string[] = getCompanyWallets(),
    collectionIds: string[] = [],
    fromDate?: Date,
    toDate?: Date,
  ): Promise<Result<FlipsideFloorSweepRecord[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (companyWallets.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    if (collectionIds.length === 0) {
      throw new Error('No collection IDs provided. Please specify NFT collection IDs to track.')
    }

    const collectionList = collectionIds.map((id) => `'${id}'`).join(', ')
    const walletList = companyWallets.map((wallet) => `'${wallet}'`).join(', ')

    let dateFilter = ''
    if (fromDate) {
      dateFilter += ` AND block_timestamp >= '${fromDate.toISOString()}'`
    }
    if (toDate) {
      dateFilter += ` AND block_timestamp <= '${toDate.toISOString()}'`
    }

    const sql = `
      SELECT
        price_usd as amount_usd,
        block_timestamp,
        tx_id,
        buyer_address,
        collection_id,
        collection_name,
        token_id,
        mint_address,
        price_usd
      FROM
        solana.nft.ez_nft_sales
      WHERE
        collection_id in (${collectionList})
        and buyer_address in (${walletList})
        ${dateFilter}
      ORDER BY
        block_timestamp DESC;
    `

    const result = await flipsideClient.query(sql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as FlipsideFloorSweepRecord[]
    return ok(records)
  }

  /**
   * Get floor sweeps with accounting period grouping
   */
  export async function getFloorSweepsWithAccountingPeriod(
    companyWallets: string[] = getCompanyWallets(),
    collectionIds: string[] = [],
    fromDate?: Date,
    toDate?: Date,
  ): Promise<Result<(FlipsideFloorSweepRecord & { accounting_period: string })[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (companyWallets.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    if (collectionIds.length === 0) {
      throw new Error('No collection IDs provided. Please specify NFT collection IDs to track.')
    }

    const collectionList = collectionIds.map((id) => `'${id}'`).join(', ')
    const walletList = companyWallets.map((wallet) => `'${wallet}'`).join(', ')

    let dateFilter = ''
    if (fromDate) {
      dateFilter += ` AND block_timestamp >= '${fromDate.toISOString()}'`
    }
    if (toDate) {
      dateFilter += ` AND block_timestamp <= '${toDate.toISOString()}'`
    }

    const sql = `
      SELECT
        price_usd as amount_usd,
        block_timestamp,
        tx_id,
        buyer_address,
        collection_id,
        collection_name,
        token_id,
        mint_address,
        price_usd,
        DATE_TRUNC('month', block_timestamp) as accounting_period
      FROM
        solana.nft.ez_nft_sales
      WHERE
        collection_id in (${collectionList})
        and buyer_address in (${walletList})
        ${dateFilter}
      ORDER BY
        block_timestamp DESC;
    `

    const result = await flipsideClient.query(sql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as (FlipsideFloorSweepRecord & { accounting_period: string })[]
    return ok(records)
  }

  /**
   * Get floor sweep summary statistics
   */
  export async function getFloorSweepSummary(
    companyWallets: string[] = getCompanyWallets(),
    collectionIds: string[] = [],
    fromDate?: Date,
    toDate?: Date,
  ) {
    const result = await getFloorSweeps(companyWallets, collectionIds, fromDate, toDate)

    if (result.isErr()) {
      return err(result.error)
    }

    const records = result.value
    const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0)
    const totalTransactions = records.length
    const uniqueWallets = new Set(records.map((r) => r.buyer_address)).size
    const uniqueCollections = new Set(records.map((r) => r.collection_id)).size

    return ok({
      totalUsd,
      totalTransactions,
      uniqueWallets,
      uniqueCollections,
      dateRange: {
        from: records.length > 0 ? new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
        to: records.length > 0 ? new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
      },
    })
  }

  /**
   * Get liquidity pool actions from Solana DeFi data
   */
  export async function getLiquidityPoolActions(
    companyWallets: string[] = getCompanyWallets(),
    tokenMints: string[] = [],
    fromDate?: Date,
    toDate?: Date,
  ): Promise<Result<FlipsideLiquidityPoolRecord[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (companyWallets.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    if (tokenMints.length === 0) {
      throw new Error('No token mints provided. Please specify token mint addresses to track.')
    }

    const mintList = tokenMints.map((mint) => `'${mint}'`).join(', ')
    const walletList = companyWallets.map((wallet) => `'${wallet}'`).join(', ')

    let dateFilter = ''
    if (fromDate) {
      dateFilter += ` AND block_timestamp >= '${fromDate.toISOString()}'`
    }
    if (toDate) {
      dateFilter += ` AND block_timestamp <= '${toDate.toISOString()}'`
    }

    const sql = `
      SELECT
        token_b_amount_usd as amount_usd,
        block_timestamp,
        tx_id,
        signer,
        action,
        pool_address,
        token_a_mint,
        token_b_mint,
        token_a_symbol,
        token_b_symbol,
        token_a_amount,
        token_b_amount,
        token_a_amount_usd,
        token_b_amount_usd
      FROM
        solana.defi.ez_liquidity_pool_actions
      WHERE
        token_a_mint IN (${mintList})
        AND signer IN (${walletList})
        ${dateFilter}
      ORDER BY
        block_timestamp DESC;
    `

    const result = await flipsideClient.query(sql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as FlipsideLiquidityPoolRecord[]
    return ok(records)
  }

  /**
   * Get liquidity pool actions with accounting period grouping
   */
  export async function getLiquidityPoolActionsWithAccountingPeriod(
    companyWallets: string[] = getCompanyWallets(),
    tokenMints: string[] = [],
    fromDate?: Date,
    toDate?: Date,
  ): Promise<Result<(FlipsideLiquidityPoolRecord & { accounting_period: string })[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (companyWallets.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    if (tokenMints.length === 0) {
      throw new Error('No token mints provided. Please specify token mint addresses to track.')
    }

    const mintList = tokenMints.map((mint) => `'${mint}'`).join(', ')
    const walletList = companyWallets.map((wallet) => `'${wallet}'`).join(', ')

    let dateFilter = ''
    if (fromDate) {
      dateFilter += ` AND block_timestamp >= '${fromDate.toISOString()}'`
    }
    if (toDate) {
      dateFilter += ` AND block_timestamp <= '${toDate.toISOString()}'`
    }

    const sql = `
      SELECT
        token_b_amount_usd as amount_usd,
        block_timestamp,
        tx_id,
        signer,
        action,
        pool_address,
        token_a_mint,
        token_b_mint,
        token_a_symbol,
        token_b_symbol,
        token_a_amount,
        token_b_amount,
        token_a_amount_usd,
        token_b_amount_usd,
        DATE_TRUNC('month', block_timestamp) as accounting_period
      FROM
        solana.defi.ez_liquidity_pool_actions
      WHERE
        token_a_mint IN (${mintList})
        AND signer IN (${walletList})
        ${dateFilter}
      ORDER BY
        block_timestamp DESC;
    `

    const result = await flipsideClient.query(sql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as (FlipsideLiquidityPoolRecord & { accounting_period: string })[]
    return ok(records)
  }

  /**
   * Get liquidity pool action summary statistics
   */
  export async function getLiquidityPoolActionSummary(
    companyWallets: string[] = getCompanyWallets(),
    tokenMints: string[] = [],
    fromDate?: Date,
    toDate?: Date,
  ) {
    const result = await getLiquidityPoolActions(companyWallets, tokenMints, fromDate, toDate)

    if (result.isErr()) {
      return err(result.error)  
    }

    const records = result.value
    const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0)
    const totalTransactions = records.length
    const uniqueWallets = new Set(records.map((r) => r.signer)).size
    const uniqueActions = new Set(records.map((r) => r.action)).size
    const uniquePools = new Set(records.map((r) => r.pool_address)).size

    return ok({
      totalUsd,
      totalTransactions,
      uniqueWallets,
      uniqueActions,
      uniquePools,
      dateRange: {
        from: records.length > 0 ? new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
        to: records.length > 0 ? new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
      },
    })
  }

  /**
   * Utility function to merge company wallet information
   * This mimics the utils.merge_company_wallets function from the Python script
   */
  export function mergeCompanyWallets(
    records: FlipsideBuybackRecord[],
    companyWalletMapping: Record<string, string>,
  ): (FlipsideBuybackRecord & { by_sac?: string })[] {
    return records.map((record) => ({
      ...record,
      by_sac: companyWalletMapping[record.swapper] || record.swapper,
    }))
  }

  /**
   * Utility function to move specified columns to the front of the record
   * This mimics the utils.move_columns_to_front function from the Python script
   */
  export function moveColumnsToFront<T extends Record<string, any>>(records: T[], frontColumns: (keyof T)[]): T[] {
    return records.map((record) => {
      const reorderedRecord = {} as T

      // Add front columns first
      frontColumns.forEach((col) => {
        if (col in record) {
          reorderedRecord[col] = record[col]
        }
      })

      // Add remaining columns
      Object.keys(record).forEach((key) => {
        if (!frontColumns.includes(key as keyof T)) {
          reorderedRecord[key as keyof T] = record[key as keyof T]
        }
      })

      return reorderedRecord
    })
  }

  // flipside transfer info

  export async function getNativeTransfersPerWallet(wallet: string) {}

  /**
   * Helper function to generate X-month date ranges starting
   */
  function generateMonthRanges(startDate: Date, endDate: Date, months: number = 3): { from: Date; to: Date }[] {
    const ranges: { from: Date; to: Date }[] = []
    let currentStart = new Date(startDate)

    while (currentStart < endDate) {
      // Calculate end of 3-month period
      const currentEnd = new Date(currentStart)
      currentEnd.setMonth(currentEnd.getMonth() + months)
      currentEnd.setDate(0) // Last day of previous month
      currentEnd.setHours(23, 59, 59, 999) // End of day

      // Don't go beyond the specified end date
      const rangeEnd = currentEnd > endDate ? endDate : currentEnd

      ranges.push({
        from: new Date(currentStart),
        to: rangeEnd
      })

      // Move to next X-month period
      currentStart = new Date(currentEnd)
      currentStart.setDate(currentStart.getDate() + 1) // First day of next month
      currentStart.setHours(0, 0, 0, 0) // Start of day
    }

    return ranges
  }

  export async function sleep(timeMs: number) {
    return new Promise((resolve) => setTimeout(resolve, timeMs))
  }

  export async function getAllNativeTransfers(walletList: string[] = getCompanyWallets(), fromDate?: Date, toDate?: Date): Promise<Result<IFlipsideTransferRecord[], FlipsideError>> {
    // Validate inputs
    validateDateRange(fromDate, toDate)

    if (walletList.length === 0) {
      throw new Error('No company wallets provided. Please configure company wallets in config.ts')
    }

    // Set default date range starting from October 1st, 2021
    const defaultStartDate = new Date('2021-10-01T00:00:00.000Z')
    const defaultEndDate = new Date() // Current date

    const effectiveFromDate = fromDate || defaultStartDate
    const effectiveToDate = toDate || defaultEndDate

    // Generate 12-month ranges
    const dateRanges = generateMonthRanges(effectiveFromDate, effectiveToDate, 12)

    console.log(`Fetching transfers in ${dateRanges.length} 12-month blocks from ${effectiveFromDate.toISOString()} to ${effectiveToDate.toISOString()}`)

    dateRanges.forEach(dR => console.log(dR.from.toString() + ' to ' + dR.to.toString()))

    await sleep(5_000)

    const allRecords: IFlipsideTransferRecord[] = []

    // Process each 12-month block
    for (const range of dateRanges) {
      console.log(`Fetching transfers for period: ${range.from.toISOString()} to ${range.to.toISOString()}`)

      const result = await getAllNativeTransfersForPeriod(walletList, range.from, range.to)

      if (result.isErr()) {
        return result
      }

      allRecords.push(...result.value)
    }

    console.log(`Total transfers fetched: ${allRecords.length}`)
    return ok(allRecords)
  }

  /**
   * Internal function to fetch native transfers for a specific period
   */
  async function getAllNativeTransfersForPeriod(walletList: string[], fromDate: Date, toDate: Date): Promise<Result<IFlipsideTransferRecord[], FlipsideError>> {
    const walletListStr = walletList.map((wallet) => `'${wallet}'`).join(', ')

    const dateFilter = ` AND tf.block_timestamp >= '${fromDate.toISOString()}' AND tf.block_timestamp <= '${toDate.toISOString()}'`

    const fetchTransfersFlipSideSql = `
WITH transfers AS (
            SELECT
                tf.tx_from,
                tf.tx_to,
                CASE
                    WHEN tf.mint IN (
                        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                        'A9mUU4qviSctJVPJdBJWkb28deg915LYJKrzQ19ji3FM'
                    ) THEN 'USDC'
                    WHEN tf.mint IN (
                        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
                        '8qJSyQprMC57TWKaYEmetUR3UUiTP2M3hXdcvFhkZdmv'
                    ) THEN 'USDT'
                    WHEN tf.mint IN (
                        'So11111111111111111111111111111111111111111',
                        'So11111111111111111111111111111111111111112'
                    ) THEN 'SOL'
                END                     AS token,
                tf.amount               AS amount,          -- 6-decimals
                tf.block_timestamp,
                tf.tx_id,
                tf.mint as token_mint
            FROM   solana.core.fact_transfers   tf
            WHERE
              tf.tx_from IN (${walletListStr})
              AND tf.amount  > 0.001                      -- outbound only
              AND tf.mint IN (                        -- the four mints above
                    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                    'A9mUU4qviSctJVPJdBJWkb28deg915LYJKrzQ19ji3FM',
                    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
                    '8qJSyQprMC57TWKaYEmetUR3UUiTP2M3hXdcvFhkZdmv',
                    'So11111111111111111111111111111111111111111',
                    'So11111111111111111111111111111111111111112'
              )
              ${dateFilter}
              AND  NOT EXISTS (SELECT 1
                  FROM solana.defi.fact_swaps s
                  WHERE s.tx_id = tf.tx_id)
        ),

        priced AS (
            SELECT
                t.*,
                p.price                       AS price_usd,
                t.amount * p.price            AS usd_value
            FROM   transfers t
            LEFT   JOIN solana.price.ez_prices_hourly p
                  ON p.hour          = DATE_TRUNC('hour', t.block_timestamp)
                  AND (
                         p.token_address = t.token_mint          -- USDC/USDT or wSOL route
                      OR (t.token = 'SOL'                        -- ← native SOL fallback
                          AND p.token_address IS NULL
                          AND p.symbol = 'SOL')
                     )
        )

        SELECT *
        FROM   priced p
        where  p.usd_value > 0.1
        ORDER  BY tx_from, token, amount DESC;
      `

    console.log(`Executing SQL for period ${fromDate.toISOString()} to ${toDate.toISOString()}`)

    const result = await flipsideClient.query(fetchTransfersFlipSideSql, {
      maxAgeMinutes: IMPORT_CONFIG.maxAgeMinutes,
      timeoutMinutes: IMPORT_CONFIG.timeoutMinutes,
    })

    if (result.isErr()) {
      return err(result.error)
    }

    const records = (result.value.records || []) as IFlipsideTransferRecord[]
    console.log(`Fetched ${records.length} records for period ${fromDate.toISOString()} to ${toDate.toISOString()}`)
    return ok(records)
  }
}
