# Flipside Crypto Integration Module

A comprehensive TypeScript integration with Flipside Crypto's API for querying blockchain data across multiple chains.

## Features

- 🔒 **Type-safe client** with neverthrow error handling
- 🌐 **tRPC router** for API endpoints
- 📊 **Pre-built query templates** for common use cases
- 📄 **Pagination support** with automatic page combining
- ⚡ **Caching** with configurable TTL
- 🧪 **Comprehensive test suite**
- 🛠️ **CLI tools** for testing and development

## Quick Start

### 1. Environment Setup

Make sure your `.env` file contains:
```bash
FLIPSIDE_API_KEY=your_api_key_here
```

### 2. Basic Usage

```typescript
import { flipsideClient, FlipsideQueries } from '@/modules/flipside';

// Method 1: Using neverthrow Result wrapper (recommended for error handling)
const result = await flipsideClient.query("SELECT 1 as test");

if (result.isOk()) {
  console.log('Records:', result.value.records);
} else {
  console.error('Error:', result.error.message);
}

// Method 2: Direct result (throws on error, simpler for basic usage)
try {
  const records = await flipsideClient.queryRecords("SELECT 1 as test");
  console.log('Records:', records);
} catch (error) {
  console.error('Error:', error.message);
}

// Method 3: Direct full result (throws on error, includes metadata)
try {
  const resultSet = await flipsideClient.queryDirect("SELECT 1 as test");
  console.log('Records:', resultSet.records);
  console.log('Stats:', resultSet.runStats);
} catch (error) {
  console.error('Error:', error.message);
}

// Pre-built queries
const ethMetrics = await FlipsideQueries.getEthereumMetrics(7);
const gasTrends = await FlipsideQueries.getGasPriceTrends(1);
```

### 3. tRPC Integration

```typescript
import { trpc } from '@/utils/trpc';

// Health check
const health = await trpc.flipside.healthCheck.query();

// Custom query
const result = await trpc.flipside.query.mutate({
  sql: "SELECT * FROM ethereum.core.fact_transactions LIMIT 10",
  maxAgeMinutes: 60
});

// Ethereum transactions
const ethTxs = await trpc.flipside.getEthereumTransactions.query({
  days: 7
});
```

## Query Methods

The FlipsideClient provides multiple ways to execute queries, each suited for different use cases:

### 1. `query()` - Neverthrow Result Wrapper
**Best for**: Production code where you want explicit error handling
```typescript
const result = await flipsideClient.query("SELECT 1 as test");
if (result.isOk()) {
  // Handle success
  console.log(result.value.records);
} else {
  // Handle error
  console.error(result.error.message);
}
```

### 2. `queryDirect()` - Direct Result with Full Metadata
**Best for**: When you need query metadata (stats, pagination info) but want simpler error handling
```typescript
try {
  const resultSet = await flipsideClient.queryDirect("SELECT 1 as test");
  console.log('Records:', resultSet.records);
  console.log('Execution time:', resultSet.runStats?.queryExecSeconds);
} catch (error) {
  console.error('Query failed:', error.message);
}
```

### 3. `queryRecords()` - Records Only
**Best for**: Simple data retrieval where you only need the records
```typescript
try {
  const records = await flipsideClient.queryRecords<MyType>("SELECT * FROM my_table");
  // records is directly an array of your data
  records.forEach(record => console.log(record));
} catch (error) {
  console.error('Query failed:', error.message);
}
```

### 4. `queryAllRecords()` - All Pages Combined
**Best for**: Large datasets where you want all results automatically paginated
```typescript
try {
  const allRecords = await flipsideClient.queryAllRecords("SELECT * FROM large_table");
  console.log(`Got ${allRecords.length} total records across all pages`);
} catch (error) {
  console.error('Query failed:', error.message);
}
```

## API Reference

### FlipsideClient

The main client for executing queries:

```typescript
class FlipsideClient {
  // Execute a single query (with neverthrow Result wrapper)
  async query(sql: string, options?: FlipsideQueryOptions): Promise<Result<QueryResultSet, FlipsideError>>

  // Execute a single query (direct result, throws on error)
  async queryDirect(sql: string, options?: FlipsideQueryOptions): Promise<QueryResultSet>

  // Execute a query and return only records (direct result, throws on error)
  async queryRecords<T>(sql: string, options?: FlipsideQueryOptions): Promise<T[]>

  // Get paginated results
  async getQueryResults(queryRunId: string, pageNumber: number, pageSize: number): Promise<Result<QueryResultSet, FlipsideError>>

  // Execute query and fetch all pages (with neverthrow Result wrapper)
  async queryAllPages(sql: string, options?: FlipsideQueryOptions): Promise<Result<QueryResultSet[], FlipsideError>>

  // Execute query and fetch all records from all pages (direct result, throws on error)
  async queryAllRecords<T>(sql: string, options?: FlipsideQueryOptions): Promise<T[]>
}
```

### FlipsideQueries

Pre-built query templates:

```typescript
namespace FlipsideQueries {
  // Ethereum metrics
  getEthereumMetrics(days: number): Promise<any[]>
  
  // DeFi protocol activity
  getDeFiActivity(protocols: string[], days: number): Promise<any[]>
  
  // NFT marketplace activity
  getNFTActivity(marketplaces: string[], days: number): Promise<any[]>
  
  // Token metrics
  getTokenMetrics(tokenAddresses: string[], days: number): Promise<any[]>
  
  // Bridge activity
  getBridgeActivity(days: number): Promise<any[]>
  
  // Wallet analysis
  getWalletAnalysis(walletAddress: string, days: number): Promise<any[]>
  
  // Gas price trends
  getGasPriceTrends(days: number): Promise<any[]>
  
  // Top tokens by volume
  getTopTokensByVolume(limit: number, days: number): Promise<any[]>
}
```

### FlipsideUtils

Utility functions:

```typescript
namespace FlipsideUtils {
  // Format SQL with parameters
  formatSql(template: string, params: Record<string, string | number>): string
  
  // Extract records from result set
  extractRecords<T>(resultSet: QueryResultSet): T[]
  
  // Combine records from multiple pages
  combinePageRecords<T>(pages: QueryResultSet[]): T[]
  
  // Get query execution stats
  getQueryStats(resultSet: QueryResultSet): QueryStats
}
```

## CLI Tools

Test and interact with the Flipside API using the CLI:

```bash
# Run all tests
pnpm tsx src/modules/flipside/cli.ts test

# Run demo queries
pnpm tsx src/modules/flipside/cli.ts demo

# Health check
pnpm tsx src/modules/flipside/cli.ts health

# Execute custom query (with neverthrow error handling)
pnpm tsx src/modules/flipside/cli.ts query "SELECT 1 as test"

# Execute custom query (direct result, throws on error)
pnpm tsx src/modules/flipside/cli.ts simple "SELECT 1 as test"

# Get Ethereum metrics (last 7 days)
pnpm tsx src/modules/flipside/cli.ts eth 7

# Get gas price trends (last 1 day)
pnpm tsx src/modules/flipside/cli.ts gas 1

# Get top 10 tokens by volume (last 7 days)
pnpm tsx src/modules/flipside/cli.ts tokens 10 7
```

## Configuration Options

### Query Options

```typescript
interface FlipsideQueryOptions {
  maxAgeMinutes?: number;     // Cache TTL (default: 30)
  cached?: boolean;           // Use cache (default: true)
  timeoutMinutes?: number;    // Query timeout (default: 15)
  pageSize?: number;          // Records per page (default: 100000)
  pageNumber?: number;        // Page number (default: 1)
  dataProvider?: string;      // Data provider (default: 'flipside')
  dataSource?: string;        // Data source (default: 'snowflake-default')
}
```

### Error Types

```typescript
type FlipsideError = 
  | { type: "FLIPSIDE_INIT_ERROR"; message: string; rawError: unknown }
  | { type: "FLIPSIDE_QUERY_ERROR"; message: string; rawError: unknown }
  | { type: "FLIPSIDE_TIMEOUT_ERROR"; message: string; queryId?: string }
  | { type: "FLIPSIDE_RATE_LIMIT_ERROR"; message: string }
  | { type: "FLIPSIDE_VALIDATION_ERROR"; message: string; details: string };
```

## Supported Chains

- Ethereum
- Polygon
- Arbitrum
- Optimism
- Avalanche
- BSC
- Solana
- Near
- Flow
- Algorand

## Common Schemas

- **Core**: Basic blockchain data (transactions, blocks, etc.)
- **DeFi**: Decentralized finance protocols
- **NFT**: Non-fungible token data
- **Gov**: Governance data

## Examples

### Custom Query with Parameters

```typescript
import { flipsideClient, FlipsideUtils } from '@/modules/flipside';

const walletAddress = '0x...';
const days = 30;

const sql = FlipsideUtils.formatSql(
  `SELECT * FROM ethereum.core.fact_transactions 
   WHERE from_address = {address} 
   AND block_timestamp >= GETDATE() - interval'{days} days'`,
  { address: walletAddress, days }
);

const result = await flipsideClient.query(sql);
```

### Pagination Example

```typescript
// Query all pages automatically
const allPages = await flipsideClient.queryAllPages(
  "SELECT * FROM ethereum.core.fact_transactions WHERE block_timestamp >= GETDATE() - interval'1 hour'"
);

if (allPages.isOk()) {
  const combinedRecords = FlipsideUtils.combinePageRecords(allPages.value);
  console.log('Total records:', combinedRecords.length);
}
```

### Error Handling

```typescript
const result = await flipsideClient.query("SELECT 1 as test");

if (result.isErr()) {
  switch (result.error.type) {
    case "FLIPSIDE_TIMEOUT_ERROR":
      console.log('Query timed out');
      break;
    case "FLIPSIDE_RATE_LIMIT_ERROR":
      console.log('Rate limit exceeded');
      break;
    default:
      console.log('Query failed:', result.error.message);
  }
  return;
}

// Success case
const records = result.value.records;
```

## Testing

Run the test suite to verify everything is working:

```bash
pnpm tsx src/modules/flipside/cli.ts test
```

This will run:
- Health check test
- Ethereum metrics test
- Gas price trends test
- Pagination test
- Error handling test

## Rate Limits

Flipside has rate limits based on your plan:
- **Free**: 500 query seconds per month
- **Builder**: More query seconds and features
- **Pro**: Higher limits and direct Snowflake access

The module automatically handles rate limit errors and provides clear error messages.

## Support

For issues with the Flipside API itself, visit [Flipside Documentation](https://docs.flipsidecrypto.xyz/).

For issues with this integration module, check the error logs and ensure your API key is correctly configured.
