import { flipsideClient, FlipsideUtils } from "./flipsideClient";
import { FlipsideQueries } from "./flipsideQueries";

/**
 * Test functions for Flipside integration
 */
export namespace FlipsideTest {
  /**
   * Basic health check test
   */
  export async function testHealthCheck() {
    console.log("🔍 Testing Flipside health check...");

    try {
      const result = await flipsideClient.query("SELECT 1 as test", {
        maxAgeMinutes: 0,
        timeoutMinutes: 1,
      });

      if (result.isErr()) {
        console.error("❌ Health check failed:", result.error);
        return false;
      }

      const records = FlipsideUtils.extractRecords(result.value);
      const stats = FlipsideUtils.getQueryStats(result.value);

      console.log("✅ Health check passed!");
      console.log("📊 Query stats:", stats);
      console.log("📋 Records:", records);

      return true;
    } catch (error) {
      console.error("❌ Health check error:", error);
      return false;
    }
  }

  /**
   * Test Ethereum metrics query
   */
  export async function testEthereumMetrics() {
    console.log("🔍 Testing Ethereum metrics query...");

    try {
      const metrics = await FlipsideQueries.getEthereumMetrics(1); // Last 1 day

      console.log("✅ Ethereum metrics retrieved!");
      console.log("📊 Sample data:", metrics.slice(0, 2));
      console.log("📈 Total records:", metrics.length);

      return metrics;
    } catch (error) {
      console.error("❌ Ethereum metrics test failed:", error);
      return null;
    }
  }

  /**
   * Test gas price trends
   */
  export async function testGasPriceTrends() {
    console.log("🔍 Testing gas price trends...");

    try {
      const trends = await FlipsideQueries.getGasPriceTrends(1); // Last 1 day

      console.log("✅ Gas price trends retrieved!");
      console.log("⛽ Sample data:", trends.slice(0, 3));
      console.log("📈 Total records:", trends.length);

      return trends;
    } catch (error) {
      console.error("❌ Gas price trends test failed:", error);
      return null;
    }
  }

  /**
   * Test pagination functionality
   */
  export async function testPagination() {
    console.log("🔍 Testing pagination...");

    try {
      const sql = `
        SELECT 
          tx_hash,
          block_timestamp,
          from_address,
          to_address
        FROM ethereum.core.fact_transactions 
        WHERE block_timestamp >= GETDATE() - interval'1 hour'
        LIMIT 50
      `;

      const result = await flipsideClient.query(sql, {
        pageSize: 10,
        pageNumber: 1,
        maxAgeMinutes: 30,
      });

      if (result.isErr()) {
        console.error("❌ Pagination test failed:", result.error);
        return false;
      }

      const stats = FlipsideUtils.getQueryStats(result.value);
      console.log("✅ Pagination test passed!");
      console.log("📊 Query stats:", stats);
      console.log("📄 Page info:", result.value.page);

      return true;
    } catch (error) {
      console.error("❌ Pagination test error:", error);
      return false;
    }
  }

  /**
   * Test error handling
   */
  export async function testErrorHandling() {
    console.log("🔍 Testing error handling...");

    try {
      // Intentionally invalid SQL
      const result = await flipsideClient.query("SELECT * FROM invalid_table_name", {
        maxAgeMinutes: 0,
        timeoutMinutes: 1,
      });

      if (result.isErr()) {
        console.log("✅ Error handling works correctly!");
        console.log("🚫 Expected error:", result.error.type, "-", result.error.message);
        return true;
      } else {
        console.log("❌ Error handling failed - query should have failed");
        return false;
      }
    } catch (error) {
      console.error("❌ Error handling test failed:", error);
      return false;
    }
  }

  /**
   * Run all tests
   */
  export async function runAllTests() {
    console.log("🚀 Starting Flipside integration tests...\n");

    const results = {
      healthCheck: await testHealthCheck(),
      ethereumMetrics: await testEthereumMetrics(),
      gasPriceTrends: await testGasPriceTrends(),
      pagination: await testPagination(),
      errorHandling: await testErrorHandling(),
    };

    console.log("\n📋 Test Results Summary:");
    console.log("========================");

    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${test}`);
    });

    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;

    console.log(`\n🎯 Overall: ${passedCount}/${totalCount} tests passed`);

    return results;
  }

  /**
   * Quick demo of common queries
   */
  export async function runDemo() {
    console.log("🎬 Running Flipside demo...\n");

    try {
      // Demo 1: Basic Ethereum stats
      console.log("📊 Demo 1: Ethereum Transaction Stats (Last 24h)");
      const ethStats = await FlipsideQueries.getEthereumMetrics(1);
      if (ethStats.length > 0) {
        const latest = ethStats[0];
        console.log(`   Transactions: ${latest.transaction_count}`);
        console.log(`   Unique senders: ${latest.unique_senders}`);
        console.log(`   Avg gas price: ${latest.avg_gas_price_gwei} gwei`);
      }

      // Demo 2: Gas price trends
      console.log("\n⛽ Demo 2: Gas Price Trends (Last 6 hours)");
      const gasData = await FlipsideQueries.getGasPriceTrends(0.25); // 6 hours
      if (gasData.length > 0) {
        const recent = gasData.slice(0, 3);
        recent.forEach((hour) => {
          console.log(`   ${hour.hour}: ${hour.median_gas_price_gwei} gwei (median)`);
        });
      }

      // Demo 3: Top tokens by volume
      console.log("\n🏆 Demo 3: Top 5 Tokens by Volume (Last 24h)");
      const topTokens = await FlipsideQueries.getTopTokensByVolume(5, 1);
      topTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. ${token.symbol}: $${Number(token.total_volume_usd).toLocaleString()}`);
      });

      console.log("\n✨ Demo completed successfully!");
    } catch (error) {
      console.error("❌ Demo failed:", error);
    }
  }
}

// Export for CLI usage
export default FlipsideTest;
