import React, { useState } from 'react';
import { trpc } from '@/utils/trpc';

/**
 * Example React component showing how to use Flipside tRPC endpoints
 */
export function FlipsideExample() {
  const [customSql, setCustomSql] = useState("SELECT 1 as test");
  const [queryResult, setQueryResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Health check query
  const healthQuery = trpc.flipside.healthCheck.useQuery();

  // Ethereum transactions query
  const ethTxQuery = trpc.flipside.getEthereumTransactions.useQuery({
    days: 7,
    maxAgeMinutes: 60
  });

  // Custom query mutation
  const customQueryMutation = trpc.flipside.query.useMutation({
    onSuccess: (data) => {
      setQueryResult(data);
      setLoading(false);
    },
    onError: (error) => {
      console.error('Query failed:', error);
      setLoading(false);
    }
  });

  const executeCustomQuery = async () => {
    setLoading(true);
    setQueryResult(null);
    
    try {
      await customQueryMutation.mutateAsync({
        sql: customSql,
        maxAgeMinutes: 30,
        timeoutMinutes: 5
      });
    } catch (error) {
      console.error('Failed to execute query:', error);
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Flipside Crypto Integration</h1>
      
      {/* Health Check Section */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Health Check</h2>
        {healthQuery.isLoading && <p>Checking connection...</p>}
        {healthQuery.error && (
          <p className="text-red-600">Error: {healthQuery.error.message}</p>
        )}
        {healthQuery.data && (
          <div className="text-green-600">
            <p>✅ Status: {healthQuery.data.status}</p>
            <p>🕒 Timestamp: {healthQuery.data.timestamp}</p>
            <p>⚡ Execution time: {healthQuery.data.stats.executionSeconds}s</p>
          </div>
        )}
      </div>

      {/* Ethereum Transactions Section */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Ethereum Transactions (Last 7 Days)</h2>
        {ethTxQuery.isLoading && <p>Loading Ethereum data...</p>}
        {ethTxQuery.error && (
          <p className="text-red-600">Error: {ethTxQuery.error.message}</p>
        )}
        {ethTxQuery.data && (
          <div>
            <p className="mb-2">📊 Found {ethTxQuery.data.records.length} hourly records</p>
            <p className="mb-2">⚡ Query executed in {ethTxQuery.data.stats.executionSeconds}s</p>
            
            {ethTxQuery.data.records.length > 0 && (
              <div className="mt-4">
                <h3 className="font-medium mb-2">Recent Hours:</h3>
                <div className="space-y-1 text-sm">
                  {ethTxQuery.data.records.slice(0, 5).map((record: any, index: number) => (
                    <div key={index} className="flex justify-between">
                      <span>{new Date(record.hour).toLocaleString()}</span>
                      <span>{Number(record.tx_count).toLocaleString()} txs</span>
                      <span>{Number(record.avg_gas_price_gwei).toFixed(2)} gwei</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Custom Query Section */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Custom Query</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">SQL Query:</label>
          <textarea
            value={customSql}
            onChange={(e) => setCustomSql(e.target.value)}
            className="w-full p-2 border rounded-md font-mono text-sm"
            rows={4}
            placeholder="Enter your SQL query here..."
          />
        </div>
        
        <button
          onClick={executeCustomQuery}
          disabled={loading || !customSql.trim()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Executing...' : 'Execute Query'}
        </button>

        {customQueryMutation.error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">
              Error: {customQueryMutation.error.message}
            </p>
          </div>
        )}

        {queryResult && (
          <div className="mt-4">
            <h3 className="font-medium mb-2">Query Results:</h3>
            <div className="mb-2 text-sm text-gray-600">
              📊 {queryResult.records?.length || 0} records returned in {queryResult.stats.executionSeconds}s
            </div>
            
            {queryResult.records && queryResult.records.length > 0 && (
              <div className="bg-gray-50 p-3 rounded-md overflow-auto">
                <pre className="text-xs">
                  {JSON.stringify(queryResult.records.slice(0, 3), null, 2)}
                </pre>
                {queryResult.records.length > 3 && (
                  <p className="text-xs text-gray-500 mt-2">
                    ... and {queryResult.records.length - 3} more records
                  </p>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Example Queries */}
      <div className="p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-3">Example Queries</h2>
        <div className="space-y-2 text-sm">
          <button
            onClick={() => setCustomSql("SELECT COUNT(*) as total_transactions FROM ethereum.core.fact_transactions WHERE block_timestamp >= GETDATE() - interval'1 day'")}
            className="block w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded"
          >
            📊 Total Ethereum transactions (last 24h)
          </button>
          
          <button
            onClick={() => setCustomSql("SELECT block_number, block_timestamp, tx_hash FROM ethereum.core.fact_transactions ORDER BY block_timestamp DESC LIMIT 10")}
            className="block w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded"
          >
            🔍 Latest 10 transactions
          </button>
          
          <button
            onClick={() => setCustomSql("SELECT date_trunc('hour', block_timestamp) as hour, AVG(gas_price / 1e9) as avg_gas_gwei FROM ethereum.core.fact_transactions WHERE block_timestamp >= GETDATE() - interval'6 hours' GROUP BY 1 ORDER BY 1 DESC")}
            className="block w-full text-left p-2 bg-gray-50 hover:bg-gray-100 rounded"
          >
            ⛽ Gas price trends (last 6 hours)
          </button>
        </div>
      </div>
    </div>
  );
}

export default FlipsideExample;
