import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/trpc";
import { flipsideClient, FlipsideUtils } from "./flipsideClient";

/**
 * Input validation schemas
 */
const QueryInputSchema = z.object({
  sql: z.string().min(1, "SQL query is required"),
  maxAgeMinutes: z.number().min(0).max(1440).optional(), // Max 24 hours
  cached: z.boolean().optional(),
  timeoutMinutes: z.number().min(1).max(60).optional(), // Max 1 hour
  pageSize: z.number().min(1).max(100000).optional(),
  pageNumber: z.number().min(1).optional(),
  dataProvider: z.string().optional(),
  dataSource: z.string().optional(),
});

const PaginatedQueryInputSchema = z.object({
  queryRunId: z.string().min(1, "Query run ID is required"),
  pageNumber: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100000).default(1000),
});

const QueryAllPagesInputSchema = QueryInputSchema.omit({ pageNumber: true, pageSize: true });

/**
 * Output schemas for type safety
 */
const QueryStatsSchema = z.object({
  queryId: z.string().nullable(),
  status: z.string().nullable(),
  recordCount: z.number(),
  executionSeconds: z.number(),
  elapsedSeconds: z.number(),
  bytes: z.number(),
});

/**
 * Flipside tRPC router
 */
export const flipsideRouter = createTRPCRouter({
  /**
   * Execute a SQL query against Flipside's data warehouse
   */
  query: publicProcedure
    .input(QueryInputSchema)
    .mutation(async ({ input }) => {
      const result = await flipsideClient.query(input.sql, {
        maxAgeMinutes: input.maxAgeMinutes,
        cached: input.cached,
        timeoutMinutes: input.timeoutMinutes,
        pageSize: input.pageSize,
        pageNumber: input.pageNumber,
        dataProvider: input.dataProvider,
        dataSource: input.dataSource,
      });

      if (result.isErr()) {
        throw new Error(`Flipside query failed: ${result.error.message}`);
      }

      const queryResult = result.value;
      
      return {
        queryId: queryResult.queryId,
        status: queryResult.status,
        columns: queryResult.columns,
        columnTypes: queryResult.columnTypes,
        records: queryResult.records,
        stats: FlipsideUtils.getQueryStats(queryResult),
        page: queryResult.page,
      };
    }),

  /**
   * Get paginated results for a specific query
   */
  getQueryResults: publicProcedure
    .input(PaginatedQueryInputSchema)
    .query(async ({ input }) => {
      const result = await flipsideClient.getQueryResults(
        input.queryRunId,
        input.pageNumber,
        input.pageSize
      );

      if (result.isErr()) {
        throw new Error(`Failed to get query results: ${result.error.message}`);
      }

      const queryResult = result.value;
      
      return {
        queryId: queryResult.queryId,
        status: queryResult.status,
        columns: queryResult.columns,
        columnTypes: queryResult.columnTypes,
        records: queryResult.records,
        stats: FlipsideUtils.getQueryStats(queryResult),
        page: queryResult.page,
      };
    }),

  /**
   * Execute a query and fetch all pages automatically
   */
  queryAllPages: publicProcedure
    .input(QueryAllPagesInputSchema)
    .mutation(async ({ input }) => {
      const result = await flipsideClient.queryAllPages(input.sql, {
        maxAgeMinutes: input.maxAgeMinutes,
        cached: input.cached,
        timeoutMinutes: input.timeoutMinutes,
        dataProvider: input.dataProvider,
        dataSource: input.dataSource,
      });

      if (result.isErr()) {
        throw new Error(`Flipside query failed: ${result.error.message}`);
      }

      const pages = result.value;
      const combinedRecords = FlipsideUtils.combinePageRecords(pages);
      const firstPage = pages[0];
      
      return {
        queryId: firstPage?.queryId,
        status: firstPage?.status,
        columns: firstPage?.columns,
        columnTypes: firstPage?.columnTypes,
        records: combinedRecords,
        stats: firstPage ? FlipsideUtils.getQueryStats(firstPage) : null,
        totalPages: pages.length,
        totalRecords: combinedRecords.length,
      };
    }),

  /**
   * Get common blockchain data - Ethereum transactions for the last 7 days
   */
  getEthereumTransactions: publicProcedure
    .input(z.object({
      days: z.number().min(1).max(30).default(7),
      maxAgeMinutes: z.number().min(0).max(1440).optional().default(60),
    }))
    .query(async ({ input }) => {
      const sql = `
        SELECT 
          date_trunc('hour', block_timestamp) as hour,
          count(distinct tx_hash) as tx_count,
          avg(gas_price / 1e9) as avg_gas_price_gwei,
          sum(gas_used) as total_gas_used
        FROM ethereum.core.fact_transactions 
        WHERE block_timestamp >= GETDATE() - interval'${input.days} days'
        GROUP BY 1
        ORDER BY 1 DESC
      `;

      const result = await flipsideClient.query(sql, {
        maxAgeMinutes: input.maxAgeMinutes,
      });

      if (result.isErr()) {
        throw new Error(`Failed to get Ethereum transactions: ${result.error.message}`);
      }

      return {
        records: FlipsideUtils.extractRecords(result.value),
        stats: FlipsideUtils.getQueryStats(result.value),
      };
    }),

  /**
   * Get blockchain statistics for multiple chains
   */
  getBlockchainStats: publicProcedure
    .input(z.object({
      chains: z.array(z.enum(['ethereum', 'polygon', 'arbitrum', 'optimism'])).default(['ethereum']),
      days: z.number().min(1).max(30).default(7),
      maxAgeMinutes: z.number().min(0).max(1440).optional().default(120),
    }))
    .query(async ({ input }) => {
      const chainQueries = input.chains.map(chain => `
        SELECT 
          '${chain}' as blockchain,
          count(distinct tx_hash) as total_transactions,
          count(distinct from_address) as unique_users,
          avg(gas_price / 1e9) as avg_gas_price_gwei
        FROM ${chain}.core.fact_transactions 
        WHERE block_timestamp >= GETDATE() - interval'${input.days} days'
      `);

      const sql = chainQueries.join(' UNION ALL ');

      const result = await flipsideClient.query(sql, {
        maxAgeMinutes: input.maxAgeMinutes,
      });

      if (result.isErr()) {
        throw new Error(`Failed to get blockchain stats: ${result.error.message}`);
      }

      return {
        records: FlipsideUtils.extractRecords(result.value),
        stats: FlipsideUtils.getQueryStats(result.value),
      };
    }),

  /**
   * Health check endpoint to verify Flipside connection
   */
  healthCheck: publicProcedure
    .query(async () => {
      const sql = "SELECT 1 as health_check";
      
      const result = await flipsideClient.query(sql, {
        maxAgeMinutes: 0, // Always fresh for health checks
        timeoutMinutes: 1, // Quick timeout
      });

      if (result.isErr()) {
        throw new Error(`Flipside health check failed: ${result.error.message}`);
      }

      return {
        status: "healthy",
        timestamp: new Date().toISOString(),
        stats: FlipsideUtils.getQueryStats(result.value),
      };
    }),
});
