import { flipsideClient, FlipsideUtils } from "./flipsideClient";

/**
 * Pre-built query templates for common blockchain data analysis
 */
export namespace FlipsideQueries {
  export async function getEthereumMetrics(days: number = 7) {
    const sql = `
      SELECT 
        date_trunc('day', block_timestamp) as date,
        count(distinct tx_hash) as transaction_count,
        count(distinct from_address) as unique_senders,
        count(distinct to_address) as unique_receivers,
        avg(gas_price / 1e9) as avg_gas_price_gwei,
        percentile_cont(0.5) within group (order by gas_price / 1e9) as median_gas_price_gwei,
        sum(gas_used) as total_gas_used,
        sum(tx_fee / 1e18) as total_fees_eth
      FROM ethereum.core.fact_transactions
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND tx_succeeded = true
      GROUP BY 1
      ORDER BY 1 DESC
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 30 });

    if (result.isErr()) {
      throw new Error(`Failed to get Ethereum metrics: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get DeFi protocol activity across multiple chains
   */
  export async function getDeFiActivity(protocols: string[] = ["uniswap", "aave", "compound"], days: number = 7) {
    const sql = `
      SELECT 
        date_trunc('day', block_timestamp) as date,
        'ethereum' as blockchain,
        project_name,
        count(distinct tx_hash) as transaction_count,
        count(distinct origin_from_address) as unique_users,
        sum(amount_usd) as total_volume_usd
      FROM ethereum.defi.ez_dex_swaps
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND project_name IN (${protocols.map((p) => `'${p}'`).join(", ")})
      GROUP BY 1, 2, 3
      ORDER BY 1 DESC, 6 DESC
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 60 });

    if (result.isErr()) {
      throw new Error(`Failed to get DeFi activity: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get NFT marketplace activity
   */
  export async function getNFTActivity(marketplaces: string[] = ["opensea", "blur", "looksrare"], days: number = 7) {
    const sql = `
      SELECT 
        date_trunc('day', block_timestamp) as date,
        platform_name,
        count(distinct tx_hash) as sales_count,
        count(distinct buyer_address) as unique_buyers,
        count(distinct seller_address) as unique_sellers,
        count(distinct nft_address) as unique_collections,
        sum(price_usd) as total_volume_usd,
        avg(price_usd) as avg_sale_price_usd,
        percentile_cont(0.5) within group (order by price_usd) as median_sale_price_usd
      FROM ethereum.nft.ez_nft_sales
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND platform_name IN (${marketplaces.map((m) => `'${m}'`).join(", ")})
        AND price_usd > 0
      GROUP BY 1, 2
      ORDER BY 1 DESC, 7 DESC
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 60 });

    if (result.isErr()) {
      throw new Error(`Failed to get NFT activity: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get token price and volume data
   */
  export async function getTokenMetrics(tokenAddresses: string[], days: number = 7) {
    const addressList = tokenAddresses.map((addr) => `'${addr.toLowerCase()}'`).join(", ");

    const sql = `
      SELECT 
        date_trunc('hour', block_timestamp) as hour,
        token_address,
        symbol,
        avg(price_usd) as avg_price_usd,
        sum(amount_usd) as volume_usd,
        count(distinct tx_hash) as transaction_count
      FROM ethereum.defi.ez_dex_swaps
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND (token_in IN (${addressList}) OR token_out IN (${addressList}))
      GROUP BY 1, 2, 3
      ORDER BY 1 DESC, 5 DESC
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 15 });

    if (result.isErr()) {
      throw new Error(`Failed to get token metrics: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get cross-chain bridge activity
   */
  export async function getBridgeActivity(days: number = 7) {
    const sql = `
      SELECT 
        date_trunc('day', block_timestamp) as date,
        platform as bridge_name,
        source_chain,
        destination_chain,
        count(distinct tx_hash) as bridge_count,
        count(distinct origin_from_address) as unique_users,
        sum(amount_usd) as total_volume_usd,
        avg(amount_usd) as avg_bridge_amount_usd
      FROM crosschain.core.ez_bridge_activity
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND amount_usd > 0
      GROUP BY 1, 2, 3, 4
      ORDER BY 1 DESC, 7 DESC
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 120 });

    if (result.isErr()) {
      throw new Error(`Failed to get bridge activity: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get wallet analysis for a specific address
   */
  export async function getWalletAnalysis(walletAddress: string, days: number = 30) {
    const address = walletAddress.toLowerCase();

    const sql = `
      WITH wallet_txs AS (
        SELECT 
          tx_hash,
          block_timestamp,
          from_address,
          to_address,
          tx_fee / 1e18 as fee_eth,
          gas_used,
          tx_succeeded
        FROM ethereum.core.fact_transactions
        WHERE (from_address = '${address}' OR to_address = '${address}')
          AND block_timestamp >= GETDATE() - interval'${days} days'
      ),
      wallet_stats AS (
        SELECT 
          count(*) as total_transactions,
          count(case when from_address = '${address}' then 1 end) as outgoing_txs,
          count(case when to_address = '${address}' then 1 end) as incoming_txs,
          sum(fee_eth) as total_fees_paid,
          avg(gas_used) as avg_gas_used,
          count(distinct date_trunc('day', block_timestamp)) as active_days
        FROM wallet_txs
        WHERE from_address = '${address}'
      )
      SELECT * FROM wallet_stats
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 10 });

    if (result.isErr()) {
      throw new Error(`Failed to get wallet analysis: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get gas price trends
   */
  export async function getGasPriceTrends(days: number = 7) {
    const sql = `
      SELECT 
        date_trunc('hour', block_timestamp) as hour,
        avg(gas_price / 1e9) as avg_gas_price_gwei,
        percentile_cont(0.1) within group (order by gas_price / 1e9) as p10_gas_price_gwei,
        percentile_cont(0.5) within group (order by gas_price / 1e9) as median_gas_price_gwei,
        percentile_cont(0.9) within group (order by gas_price / 1e9) as p90_gas_price_gwei,
        max(gas_price / 1e9) as max_gas_price_gwei,
        count(*) as transaction_count
      FROM ethereum.core.fact_transactions
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND tx_succeeded = true
      GROUP BY 1
      ORDER BY 1 DESC
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 30 });

    if (result.isErr()) {
      throw new Error(`Failed to get gas price trends: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }

  /**
   * Get top tokens by volume
   */
  export async function getTopTokensByVolume(limit: number = 20, days: number = 7) {
    const sql = `
      SELECT 
        token_address,
        symbol,
        sum(amount_usd) as total_volume_usd,
        count(distinct tx_hash) as transaction_count,
        count(distinct origin_from_address) as unique_traders,
        avg(amount_usd) as avg_trade_size_usd
      FROM ethereum.defi.ez_dex_swaps
      WHERE block_timestamp >= GETDATE() - interval'${days} days'
        AND amount_usd > 0
      GROUP BY 1, 2
      ORDER BY 3 DESC
      LIMIT ${limit}
    `;

    const result = await flipsideClient.query(sql, { maxAgeMinutes: 60 });

    if (result.isErr()) {
      throw new Error(`Failed to get top tokens: ${result.error.message}`);
    }

    return FlipsideUtils.extractRecords(result.value);
  }
}