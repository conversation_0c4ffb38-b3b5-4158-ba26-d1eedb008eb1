import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";

// Input validation schemas
const getTransactionsSchema = z
  .object({
    limit: z.number().min(1).max(100).default(10),
    cursor: z.string().optional(),
    accountId: z.string().optional(), // Keep for backward compatibility
    accountIds: z.array(z.string()).optional(), // New field for multiple accounts
    search: z.string().optional(),
    type: z.enum(["transfer", "trade", "crypto_expense"]).optional(),
    dateFrom: z.string().optional(), // ISO date string
    dateTo: z.string().optional(), // ISO date string
    amountMin: z.number().optional(),
    amountMax: z.number().optional(),
    sortField: z.enum(["executedAt", "account", "type", "amount"]).optional(),
    sortDirection: z.enum(["asc", "desc"]).optional(),
  })
  .optional()
  .default({});

const getTransactionByIdSchema = z.object({
  id: z.string(),
});

const getTransactionsByGroupIdSchema = z.object({
  transactionGroupId: z.string(),
});

export const transactionRouter = createTRPCRouter({
  getAll: publicProcedure.input(getTransactionsSchema).query(async ({ input, ctx }) => {
    try {
      const limit = input?.limit ?? 10;
      const cursor = input?.cursor;
      const accountId = input?.accountId;
      const accountIds = input?.accountIds;
      const search = input?.search;
      const type = input?.type;
      const dateFrom = input?.dateFrom;
      const dateTo = input?.dateTo;
      const amountMin = input?.amountMin;
      const amountMax = input?.amountMax;
      const sortField = input?.sortField;
      const sortDirection = input?.sortDirection;

      // Build where clause
      let whereClause: any = {};

      // Account filter - support both single and multiple accounts
      if (accountIds && accountIds.length > 0) {
        whereClause.accountId = { in: accountIds };
      } else if (accountId) {
        whereClause.accountId = accountId;
      }

      // Type filter
      if (type) {
        switch (type) {
          case "transfer":
            whereClause.transfer = { isNot: null };
            break;
          case "trade":
            whereClause.trade = { isNot: null };
            break;
          case "crypto_expense":
            whereClause.cryptoExpense = { isNot: null };
            break;
        }
      }

      // Date range filter
      if (dateFrom || dateTo) {
        whereClause.executedAt = {};
        if (dateFrom) {
          whereClause.executedAt.gte = new Date(dateFrom);
        }
        if (dateTo) {
          // Add 23:59:59 to include the entire day
          const endDate = new Date(dateTo);
          endDate.setHours(23, 59, 59, 999);
          whereClause.executedAt.lte = endDate;
        }
      }

      // Amount range filter (USD values)
      if (amountMin !== undefined || amountMax !== undefined) {
        const amountConditions = [];

        if (amountMin !== undefined || amountMax !== undefined) {
          const amountFilter: any = {};
          if (amountMin !== undefined) amountFilter.gte = amountMin;
          if (amountMax !== undefined) amountFilter.lte = amountMax;

          // Filter transfers by USD value
          amountConditions.push({
            transfer: {
              usdValue: amountFilter,
            },
          });

          // Filter crypto expenses by USD amount
          amountConditions.push({
            cryptoExpense: {
              amountUsd: amountFilter,
            },
          });

          // Note: Trades don't have a direct USD value field in the schema
          // For now, we'll exclude trades from amount filtering
          // In the future, we could calculate USD values or add a usdValue field to trades
        }

        if (amountConditions.length > 0) {
          whereClause.OR = whereClause.OR ? [...whereClause.OR, ...amountConditions] : amountConditions;
        }
      }

      // Search filter
      if (search && search.trim()) {
        const searchTerm = search.trim();
        whereClause.OR = [
          // Search in transfer counterparty
          {
            transfer: {
              counterparty: {
                search: searchTerm,
              },
            },
          },
          // Search in transfer description
          {
            transfer: {
              description: {
                search: searchTerm,
              },
            },
          },
          // Search in trade description
          {
            trade: {
              description: {
                search: searchTerm,
              },
            },
          },
          // Search in trade token symbols
          {
            trade: {
              tokenFrom: {
                search: searchTerm,
              },
            },
          },
          {
            trade: {
              tokenTo: {
                search: searchTerm,
              },
            },
          },
          // Search in account name
          {
            account: {
              name: {
                search: searchTerm,
              },
            },
          },
        ];
      }

      // Build orderBy clause
      let orderBy: any = { executedAt: "desc" }; // Default sort

      if (sortField && sortDirection) {
        switch (sortField) {
          case "executedAt":
            orderBy = { executedAt: sortDirection };
            break;
          case "account":
            orderBy = { account: { name: sortDirection } };
            break;
          case "type":
            // Sort by type is complex since we have different transaction types
            // For now, we'll sort by executedAt as fallback
            orderBy = { executedAt: sortDirection };
            break;
          case "amount":
            // Amount sorting is complex due to different transaction types
            // For now, we'll sort by executedAt as fallback
            orderBy = { executedAt: sortDirection };
            break;
          default:
            orderBy = { executedAt: "desc" };
        }
      }

      const transactions = await ctx.prisma.transaction.findMany({
        take: limit + 1, // Take one extra to check if there's a next page
        cursor: cursor ? { id: cursor } : undefined,
        where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
        orderBy,
        include: {
          transfer: {
            include: {
              reconciliations: {
                where: {
                  status: {
                    in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
                  },
                },
                select: {
                  id: true,
                  status: true,
                  confidenceScore: true,
                  invoice: {
                    select: {
                      id: true,
                      invoiceReference: true,
                      amountGross: true,
                      currencyCode: true,
                      vendor: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          trade: true,
          cryptoExpense: {
            select: {
              id: true,
              type: true,
              txId: true,
              amountUsd: true,
              blockTimestamp: true,
              buyback: {
                select: {
                  id: true,
                  swapFromSymbol: true,
                  swapToSymbol: true,
                  swapFromAmount: true,
                  swapToAmount: true,
                  swapFromAmountUsd: true,
                  swapToAmountUsd: true,
                  swapFromMint: true,
                  swapToMint: true,
                },
              },
              floorSweep: {
                select: {
                  id: true,
                  collectionId: true,
                  collectionName: true,
                  tokenId: true,
                  mintAddress: true,
                  priceUsd: true,
                },
              },
              liquidityPool: {
                select: {
                  id: true,
                  action: true,
                  poolAddress: true,
                  tokenAMint: true,
                  tokenBMint: true,
                  tokenASymbol: true,
                  tokenBSymbol: true,
                  tokenAAmount: true,
                  tokenBAmount: true,
                  tokenAAmountUsd: true,
                  tokenBAmountUsd: true,
                },
              },
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              type: true,
              publicKey: true,
            },
          },
        },
      });

      let nextCursor: string | undefined = undefined;
      if (transactions.length > limit) {
        const nextItem = transactions.pop(); // Remove the extra item
        nextCursor = nextItem!.id;
      }

      return {
        transactions,
        nextCursor,
      };
    } catch (error) {
      console.error("Failed to fetch transactions:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch transactions",
        cause: error,
      });
    }
  }),

  getById: publicProcedure.input(getTransactionByIdSchema).query(async ({ input, ctx }) => {
    try {
      const transaction = await ctx.prisma.transaction.findUnique({
        where: { id: input.id },
        include: {
          transfer: {
            include: {
              reconciliations: {
                where: {
                  status: {
                    in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
                  },
                },
                select: {
                  id: true,
                  status: true,
                  confidenceScore: true,
                  invoice: {
                    select: {
                      id: true,
                      invoiceReference: true,
                      amountGross: true,
                      currencyCode: true,
                      vendor: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          trade: true,
          cryptoExpense: {
            select: {
              id: true,
              type: true,
              txId: true,
              amountUsd: true,
              blockTimestamp: true,
              buyback: {
                select: {
                  id: true,
                  swapFromSymbol: true,
                  swapToSymbol: true,
                  swapFromAmount: true,
                  swapToAmount: true,
                  swapFromAmountUsd: true,
                  swapToAmountUsd: true,
                  swapFromMint: true,
                  swapToMint: true,
                },
              },
              floorSweep: {
                select: {
                  id: true,
                  collectionId: true,
                  collectionName: true,
                  tokenId: true,
                  mintAddress: true,
                  priceUsd: true,
                },
              },
              liquidityPool: {
                select: {
                  id: true,
                  action: true,
                  poolAddress: true,
                  tokenAMint: true,
                  tokenBMint: true,
                  tokenASymbol: true,
                  tokenBSymbol: true,
                  tokenAAmount: true,
                  tokenBAmount: true,
                  tokenAAmountUsd: true,
                  tokenBAmountUsd: true,
                },
              },
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      if (!transaction) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transaction not found",
        });
      }

      return transaction;
    } catch (error) {
      console.error("Failed to fetch transaction:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch transaction",
        cause: error,
      });
    }
  }),

  getByGroupId: publicProcedure.input(getTransactionsByGroupIdSchema).query(async ({ input, ctx }) => {
    try {
      const transactions = await ctx.prisma.transaction.findMany({
        where: { transactionGroupId: input.transactionGroupId },
        orderBy: { dbCreatedAt: "asc" },
        include: {
          transfer: {
            include: {
              reconciliations: {
                where: {
                  status: {
                    in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
                  },
                },
                select: {
                  id: true,
                  status: true,
                  confidenceScore: true,
                  invoice: {
                    select: {
                      id: true,
                      invoiceReference: true,
                      amountGross: true,
                      currencyCode: true,
                      vendor: {
                        select: {
                          name: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          trade: true,
          cryptoExpense: {
            select: {
              id: true,
              type: true,
              txId: true,
              amountUsd: true,
              blockTimestamp: true,
              buyback: {
                select: {
                  id: true,
                  swapFromSymbol: true,
                  swapToSymbol: true,
                  swapFromAmount: true,
                  swapToAmount: true,
                  swapFromAmountUsd: true,
                  swapToAmountUsd: true,
                  swapFromMint: true,
                  swapToMint: true,
                },
              },
              floorSweep: {
                select: {
                  id: true,
                  collectionId: true,
                  collectionName: true,
                  tokenId: true,
                  mintAddress: true,
                  priceUsd: true,
                },
              },
              liquidityPool: {
                select: {
                  id: true,
                  action: true,
                  poolAddress: true,
                  tokenAMint: true,
                  tokenBMint: true,
                  tokenASymbol: true,
                  tokenBSymbol: true,
                  tokenAAmount: true,
                  tokenBAmount: true,
                  tokenAAmountUsd: true,
                  tokenBAmountUsd: true,
                },
              },
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      return transactions;
    } catch (error) {
      console.error("Failed to fetch transactions by group ID:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch transactions by group ID",
        cause: error,
      });
    }
  }),

  getStats: publicProcedure.query(async ({ ctx }) => {
    try {
      const [total, byAccount, withTransfers, withTrades, withCryptoExpenses] = await Promise.all([
        ctx.prisma.transaction.count(),
        ctx.prisma.transaction.groupBy({
          by: ["accountId"],
          _count: {
            id: true,
          },
        }),
        ctx.prisma.transaction.count({
          where: {
            transfer: {
              isNot: null,
            },
          },
        }),
        ctx.prisma.transaction.count({
          where: {
            trade: {
              isNot: null,
            },
          },
        }),
        ctx.prisma.transaction.count({
          where: {
            cryptoExpense: {
              isNot: null,
            },
          },
        }),
      ]);

      return {
        total,
        byAccount,
        withTransfers,
        withTrades,
        withCryptoExpenses,
        withLiquidityActions: total - withTransfers - withTrades - withCryptoExpenses,
      };
    } catch (error) {
      console.error("Failed to fetch transaction stats:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch transaction stats",
        cause: error,
      });
    }
  }),

  getFilteredStats: publicProcedure
    .input(
      z
        .object({
          accountId: z.string().optional(), // Keep for backward compatibility
          accountIds: z.array(z.string()).optional(), // New field for multiple accounts
          search: z.string().optional(),
          type: z.enum(["transfer", "trade", "crypto_expense"]).optional(),
          dateFrom: z.string().optional(),
          dateTo: z.string().optional(),
          amountMin: z.number().optional(),
          amountMax: z.number().optional(),
        })
        .optional()
        .default({})
    )
    .query(async ({ input, ctx }) => {
      try {
        // Build where clause - reuse the same logic as getAll
        let whereClause: any = {};

        // Account filter - support both single and multiple accounts
        if (input?.accountIds && input.accountIds.length > 0) {
          whereClause.accountId = { in: input.accountIds };
        } else if (input?.accountId) {
          whereClause.accountId = input.accountId;
        }

        // Type filter
        if (input?.type) {
          switch (input.type) {
            case "transfer":
              whereClause.transfer = { isNot: null };
              break;
            case "trade":
              whereClause.trade = { isNot: null };
              break;
            case "crypto_expense":
              whereClause.cryptoExpense = { isNot: null };
              break;
          }
        }

        // Date range filter
        if (input?.dateFrom || input?.dateTo) {
          whereClause.executedAt = {};
          if (input.dateFrom) {
            whereClause.executedAt.gte = new Date(input.dateFrom);
          }
          if (input.dateTo) {
            const endDate = new Date(input.dateTo);
            endDate.setHours(23, 59, 59, 999);
            whereClause.executedAt.lte = endDate;
          }
        }

        // Amount range filter (USD values)
        if (input?.amountMin !== undefined || input?.amountMax !== undefined) {
          const amountConditions = [];

          if (input.amountMin !== undefined || input.amountMax !== undefined) {
            const amountFilter: any = {};
            if (input.amountMin !== undefined) amountFilter.gte = input.amountMin;
            if (input.amountMax !== undefined) amountFilter.lte = input.amountMax;

            // Filter transfers by USD value
            amountConditions.push({
              transfer: { usdValue: amountFilter },
            });

            // Filter crypto expenses by USD amount
            amountConditions.push({
              cryptoExpense: { amountUsd: amountFilter },
            });

            // Note: Trades don't have a direct USD value field in the schema
            // For now, we'll exclude trades from amount filtering
          }

          if (amountConditions.length > 0) {
            whereClause.OR = whereClause.OR ? [...whereClause.OR, ...amountConditions] : amountConditions;
          }
        }

        // Search filter
        if (input?.search && input.search.trim()) {
          const searchTerm = input.search.trim();
          whereClause.OR = [
            // Search in transfer counterparty
            {
              transfer: {
                counterparty: {
                  search: searchTerm,
                },
              },
            },
            // Search in transfer description
            {
              transfer: {
                description: {
                  search: searchTerm,
                },
              },
            },
            // Search in trade description
            {
              trade: {
                description: {
                  search: searchTerm,
                },
              },
            },
            // Search in trade token symbols
            {
              trade: {
                tokenFrom: {
                  search: searchTerm,
                },
              },
            },
            {
              trade: {
                tokenTo: {
                  search: searchTerm,
                },
              },
            },
            // Search in account name
            {
              account: {
                name: {
                  search: searchTerm,
                },
              },
            },
          ];
        }

        const baseWhere = Object.keys(whereClause).length > 0 ? whereClause : undefined;

        const [total, withTransfers, withTrades, withCryptoExpenses] = await Promise.all([
          ctx.prisma.transaction.count({
            where: baseWhere,
          }),
          ctx.prisma.transaction.count({
            where: {
              ...baseWhere,
              transfer: {
                isNot: null,
              },
            },
          }),
          ctx.prisma.transaction.count({
            where: {
              ...baseWhere,
              trade: {
                isNot: null,
              },
            },
          }),
          ctx.prisma.transaction.count({
            where: {
              ...baseWhere,
              cryptoExpense: {
                isNot: null,
              },
            },
          }),
        ]);

        return {
          total,
          withTransfers,
          withTrades,
          withCryptoExpenses,
          withLiquidityActions: total - withTransfers - withTrades - withCryptoExpenses,
        };
      } catch (error) {
        console.error("Failed to fetch filtered transaction stats:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch filtered transaction stats",
          cause: error,
        });
      }
    }),
});
