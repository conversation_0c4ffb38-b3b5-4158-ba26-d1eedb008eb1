import { Lamina<PERSON>, observe } from "@lmnr-ai/lmnr";
Laminar.initialize({
  projectApiKey: "plcj7PCjAUMdHj3zXYPMWWqZ7vrtznAeS8H6E2hnHGR1yOVHyaGy8BB2kNbZmi5P",
  instrumentModules: {
    openAI: OpenAI,
  },
});

import { AIModelId, llm } from "@/modules/ai/llm/llm";
import { zodUtils } from "@/modules/core/utils/zodUtils";
import { InvoiceImportItem } from "@/prisma/generated";
import { ExternalAttachment } from "braintrust";
import z from "zod";
import { prisma } from "../../../prisma/prisma";
import * as R from "remeda";
import * as lfi from "lfi";
import { braintrust } from "@/modules/tracing/braintrust";
import { MotlFile } from "@/modules/core/motlFile";
import { ok, ResultAsync } from "neverthrow";
import { generateObject } from "ai";
import { aiSdk } from "@/modules/ai/llm/aiSdk";
import yargs from "yargs";
import { OpenAI } from "openai";

const useTextInput = true;

const invoiceBaseDetails = prisma.invoiceBaseDetails
  .findMany({
    where: {
      invoiceImportItemId: {
        not: null,
      },
    },
    include: {
      invoiceImportItem: true,
    },
    take: 5,
  })
  .then(async (invoiceBaseDetails) => {
    console.log("run at", new Date().toISOString());
    console.log(invoiceBaseDetails);

    console.log(R.groupBy(invoiceBaseDetails, (r) => (r.vendor as any).companyName));

    const fomoVendorWrong = invoiceBaseDetails.filter((r) => (r.vendor as any).companyName === "Fomo GmbH");

    console.log(fomoVendorWrong);

    const newParsed = await parseInvoices({
      invoiceImportItems: invoiceBaseDetails.map((r) => r.invoiceImportItem!).filter((i) => i !== null),
      // .filter((i) =>
      //   i.fileUrl.includes("https://pub-da9a66ddb6b5467c965f355f1bbe927d.r2.dev/07a8b06064b1355432a41b26b1bac845059bfd5f7f463a81fc8bbec71681b3c3")
      // )
      aiModel: "gpt-4.1-mini",
      // aiModel: "gpt-4.1",
      // aiModel: "o4-mini",
    }).then(async (results) => {
      const sucess = results.map((r) => (r.wrappedRes.isOk() ? { ...r.wrappedRes.value, item: r.item } : null)).filter((r) => r !== null);

      const groupedByCompanyName = R.groupBy(sucess, (r) => r.object.issuer.companyName);
      const wrongVendor = groupedByCompanyName["Fomo GmbH"];

      console.log(sucess);

      console.log(
        "newParsed wrongVendor",
        wrongVendor?.map((r) => ({ fileUrl: r.item.fileUrl, issuer: r.object.issuer.companyName }))
      );

      const failed = results.map((r) => (r.wrappedRes.isErr() ? r : null)).filter((r) => r !== null);
      console.log("failed", failed);

      console.log(`${sucess.length} / ${results.length} successful results`);

      console.log("result", {
        total: results.length,
        cached: sucess.filter((r) => r.isCached).length,
        success: sucess.length,
        wrongVendor: wrongVendor?.length ?? 0,
      });

      console.log(llm.getUsageCost());

      await Laminar.flush();
      const debug = 1;
    });
  });

async function parseInvoices(args: { invoiceImportItems: InvoiceImportItem[]; aiModel: AIModelId }) {
  const { invoiceImportItems } = args;
  return await observe({ name: "parseInvoices" }, async () => {
    // rootSpan.log({
    //   input: {
    //     invoiceImportItems: invoiceImportItems.length,
    //   },
    //   metadata: {
    //     aiModel: args.aiModel,
    //   },
    // });
    const isInvoicesRes = await lfi.pipe(
      lfi.asConcur(invoiceImportItems),
      lfi.mapConcur(async (item) => {
        return await observe({ name: "parseInvoice" }, async (span) => {
          const file = await MotlFile.parse(item.fileUrl);
          // span.log({
          //   input: {
          //     item,
          //     // document: new ExternalAttachment({
          //     //   contentType: file.mimeType,
          //     //   url: item.fileUrl,
          //     //   filename: file.fileName,
          //     // }),
          //   },
          //   metadata: {
          //     aiModel: args.aiModel,
          //   },
          // });

          const addressSchema = z.object({
            country: zodUtils.optional(z.string()),
            street: z.string(),
            houseNumber: zodUtils.optional(z.string()),
            city: z.string(),
            postalCode: z.string(),
          });

          const invoicePartySchema = z.object({
            companyName: z.string(),
            uidNumber: zodUtils.optional(z.string()),
            email: zodUtils.optional(z.string()),
            phone: zodUtils.optional(z.string()),
            address: addressSchema,
            contactPerson: zodUtils.optional(z.string()),
          });

          const invoiceBaseDetailsSchema = z.object({
            invoiceDate: z.string().describe("YYYY-MM-DD"),
            invoiceReference: z.string(),
            issuer: invoicePartySchema,
            recipient: invoicePartySchema,
          });

          const wrappedRes = await llm.generateStructuredOutput({
            // seed: 1,
            // temperature: 0,
            // span: span,
            model: args.aiModel,
            schema: invoiceBaseDetailsSchema,
            messages: [
              {
                role: "system",
                content: `You are the accountant of Fomo GmbH (company from Austria).
              You are given an accounts payable invoice and need to extract the base details like in the given json schema.
              Only extract the fields which are stated on the invoice. Don't infer any fields.`,
              },

              {
                role: "user",
                content: [
                  useTextInput
                    ? {
                        type: "text",
                        text: `Invoice: \n """${item.text}"""`,
                      }
                    : {
                        type: "file",
                        file: {
                          file_data: await file.getBase64FileUrl(),
                          filename: "invoice.pdf",
                        },
                        // file_id: "new",
                      },
                ],
              },
            ],
          });

          // const wrappedRes = await ResultAsync.fromPromise(
          //   generateObject({
          //     model: aiSdk.openAi.languageModel("gpt-4o-mini"),
          //     messages: [
          //       {
          //         role: "user",
          //         content: [
          //           {
          //             type: "text",
          //             text: `Can you extract the base details like in the given json schema from the following invoice. Only extract the fields which are stated on the invoice. Don't infer any fields.`,
          //           },
          //           {
          //             type: "file",
          //             mimeType: file.mimeType,
          //             data: file.buffer,
          //           },
          //         ],
          //       },
          //     ],
          //     schema: invoiceBaseDetailsSchema,
          //     headers: {
          //       "x-bt-parent": await span.export(),
          //     },
          //   }),
          //   (e) => ({
          //     type: "LLMError",
          //     message: e instanceof Error ? e.message : String(e),
          //     rawError: e,
          //   })
          // );

          const llmRes = wrappedRes;

          // span.log({
          //   output: {
          //     llmRes,
          //   },
          // });

          return { item, wrappedRes };
        });
      }),

      lfi.reduceConcur(lfi.toArray())
    );

    return isInvoicesRes;
  });
}
