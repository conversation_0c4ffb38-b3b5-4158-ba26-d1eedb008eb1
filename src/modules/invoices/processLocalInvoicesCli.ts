import { AIModelId, llm } from "../ai/llm/llm";
import { braintrust } from "../tracing/braintrust";
import * as lfi from "lfi";
import { GenericFile, MotlFile } from "../core/motlFile";
import { detectInvoice } from "./detectInvoices";
import { prisma } from "@/prisma/prisma";
import { err, okAsync, ResultAsync, Result } from "neverthrow";
import { sleep } from "openai/core.mjs";
import yargs from "yargs";
import { cryptoCompareApi } from "../core/cryptoCompareApi";
import { invoiceAi } from "./invoiceAi";
import { json } from "../core/utils/jsonUtils";
import { cli } from "../core/utils/cliUtils";
import fs from "fs/promises";
import path from "path";
import { s3 } from "../core/s3";
import limitConcur from "limit-concur";
import { createInvoice, invoiceService } from "./invoiceService";
import { pdfPlumberApi } from "../pdfParser/pdfPlumberApi";
import { mistral } from "../ai/mistral";
import PQueue from "p-queue";
import { documentAI } from "../pdfParser/documentAi";

export async function parseInvoices(args: {
  invoiceImportItems: { file: string; metadata: { filePath: string } }[];
  baseDetailsAiModel: AIModelId;
  invoiceValuesAiModel: AIModelId;
}) {
  const { invoiceImportItems } = args;

  // rootSpan.log({
  //   input: {
  //     invoiceImportItems: invoiceImportItems.length,
  //   },
  //   metadata: {
  //     aiModel: args.baseDetailsAiModel,
  //   },
  // });

  const createInvoiceQueue = new PQueue({ concurrency: 1 });

  const isInvoicesRes = await lfi.pipe(
    lfi.asConcur(invoiceImportItems),
    lfi.mapConcur(
      limitConcur(50, async (item) => {
        return braintrust.trace("processInvoice", async (span) => {
          const createImportItemRes = await ResultAsync.fromPromise(
            (async () => {
              console.log("processing", item.file);
              const file = await MotlFile.parse(item.file);
              const fileHash = await file.getHash();

              const uploadRes = await s3.upload({ file, public: true });

              // let parsedTextRes = await pdfPlumberApi.parse(file);

              // if (parsedTextRes.isErr()) {
              //   console.error(`error parsing ${file.fileName}, `, parsedTextRes.error);
              //   return err({ type: "error", error: parsedTextRes.error } as const);
              // }

              // let parsedText = parsedTextRes.value.content;

              // if (!parsedTextRes.value.content) {
              //   const ocrRes = await mistral.ocrDocument({ file: file.buffer });

              //   parsedText = ocrRes.text;
              // }

              const parsedText = await documentAI.parseDocument({ file });
              if (parsedText.isErr()) {
                return err({
                  type: "parseDocumentError",
                  message: "error parsing document",
                  err: parsedText.error,
                  context: {
                    file,
                  },
                });
              }

              const importItem = await prisma.invoiceImportItem.upsert({
                where: {
                  fileHash,
                },
                update: {
                  fileHash,
                  fileUrl: uploadRes.url,
                  importItemType: "UPLOADED",
                  externalId: fileHash,
                  filename: file.fileName,
                  text: parsedText.value.parsedText,
                  itemDate: new Date(),
                },
                create: {
                  fileHash,
                  fileUrl: uploadRes.url,
                  importItemType: "UPLOADED",
                  externalId: fileHash,
                  filename: file.fileName,
                  text: parsedText.value.parsedText,
                  itemDate: new Date(),
                },
              });

              span.log({
                input: {
                  importItem,
                },
              });
              return {
                importItem,
                file,
                fileHash,
                uploadRes,
              };
            })(),
            (err) =>
              ({
                code: "createImportItemError",
                message: "error creating invoice import item",
                err,
              } as const)
          );

          if (createImportItemRes.isErr()) {
            span.log({
              error: createImportItemRes.error,
            });
            return createImportItemRes;
          }

          const { importItem, file, fileHash, uploadRes } = createImportItemRes.value;

          const isInvoiceRes = await detectInvoice({
            item: {
              ...item,
              fileUrl: item.file,
            },
            file,
            aiModel: args.baseDetailsAiModel,
          });

          if (isInvoiceRes.isErr()) {
            return err({
              type: "detectInvoiceError",
              message: (isInvoiceRes.error as any).message,
              err: isInvoiceRes.error,
            });
          }
          await prisma.invoiceImportItem.update({
            where: {
              id: importItem.id,
            },
            data: {
              // @ts-ignore
              isInvoice: isInvoiceRes.value.isInvoice,
            },
          });
          // @ts-ignore
          if (!isInvoiceRes.value.isInvoice) {
            span.log({
              output: {
                notAnInvoice: true,
              },
            });
            return okAsync({
              type: "notAnInvoice",
              importItem,
              file,
              fileHash,
              uploadRes,
            });
          }

          return invoiceAi
            .parseInvoice({
              file: item.file,
              baseDetailsAiModel: args.baseDetailsAiModel,
              invoiceValuesAiModel: args.invoiceValuesAiModel,
            })
            .map((res) => {
              return {
                ...res,
                importItem,
              };
            })
            .mapErr((err) => {
              span.log({
                error: {
                  err,
                  code: "extractInvoiceValuesError",
                },
                output: {
                  type: "extractInvoiceValuesError",
                },
              });
              return err;
            });
        });
      })
    ),
    lfi.mapConcur(async (item) => {
      return item.map((r) => {
        if (!("baseDetails" in r)) {
          return r;
        }

        return createInvoiceQueue
          .add(async () =>
            createInvoice({
              invoice: {
                date: new Date(r.baseDetails.object.invoiceDate),
                invoiceReference: r.baseDetails.object.invoiceReference,
                recipient: r.baseDetails.object.recipient as any,
                amountGross: r.invoiceValues.amountGross,
                amountNet: r.invoiceValues.amountNet,
                amountVat: r.invoiceValues.amountVat,

                hasVAT: r.invoiceValues.hasVAT,
                isReverseCharge: r.hasTaxRes.object.isReverseCharge,
                currencyCode: r.hasTaxRes.object.currencyCode,
                sourceAmountGross: "sourceAmountGross" in r.invoiceValues ? (r.invoiceValues.sourceAmountGross as number) : undefined,
                sourceAmountNet: "sourceAmountNet" in r.invoiceValues ? (r.invoiceValues.sourceAmountNet as number) : undefined,
                sourceAmountVat: "sourceAmountVat" in r.invoiceValues ? (r.invoiceValues.sourceAmountVat as number) : undefined,
                conversationRate: "conversationRate" in r.invoiceValues ? (r.invoiceValues.conversationRate as number) : undefined,
                lineItems: {
                  createMany: {
                    data: r.invoiceValues.items.map((item) => ({
                      name: item.name,
                      description: item.description,
                      priceGross: item.priceGross,
                      priceNet: item.priceNet,
                      vatRate: item.vatRate,
                      vatAmount: item.vatAmount,
                    })),
                  },
                },
              },
              vendor: {
                name: r.baseDetails.object.vendor.companyName,
                street: r.baseDetails.object.vendor.address.street,
                houseNumber: r.baseDetails.object.vendor.address.houseNumber,
                city: r.baseDetails.object.vendor.address.city,
                country: r.baseDetails.object.vendor.address.country ?? "",
                vatNumber: r.baseDetails.object.vendor.uidNumber,
                contactPerson: r.baseDetails.object.vendor.contactPerson,
                email: r.baseDetails.object.vendor.email,
                phone: r.baseDetails.object.vendor.phone,
              },
              importItem: r.importItem,
            })
          )
          .then((res) => res!);
      });
    }),

    lfi.reduceConcur(lfi.toArray())
  );

  return isInvoicesRes;
}

async function main() {
  const cliArgs = await yargs(process.argv.slice(2))
    .option("limit", { type: "number", default: 1 })
    .option("skip", { type: "number", default: 0 })
    .option("delete", { type: "boolean", default: false })
    .option("externalId", { type: "string", default: undefined })
    .parse();

  if (cliArgs.delete) {
    await prisma.invoiceImportItem.deleteMany({
      where: {
        importItemType: "UPLOADED",
      },
    });
    await prisma.invoice.deleteMany({});
    await prisma.vendor.deleteMany({});
  }

  // const logger = await braintrust.getLogger();

  const dataPath = path.join("data-local", "Belege_16.06.25");

  const files = await fs.readdir(dataPath);

  console.log("total files in folder", files.length);

  const invoiceImportItems = files
    .map((file) => ({
      file: path.join(dataPath, file),
      metadata: {
        filePath: file,
      },
    }))
    .slice(cliArgs.skip, cliArgs.skip + cliArgs.limit);

  console.log("invoiceImportItems", invoiceImportItems.length);

  const invoiceResults = await parseInvoices({
    baseDetailsAiModel: "gpt-4.1",
    invoiceValuesAiModel: "o4-mini",
    invoiceImportItems: invoiceImportItems,
  });

  const successRes = invoiceResults.map((r) => r.unwrapOr(null)).filter((r) => r != null);

  const invoices = successRes.filter((r) => "baseDetails" in r);

  const notAnInvoice = successRes.filter((r) => "type" in r && r.type === "notAnInvoice");
  console.log(`not an invoice`, {
    notAnInvoice: notAnInvoice.length,
  });

  const failedInvoiceParsing = invoiceResults.filter((res) => res.isErr());
  if (failedInvoiceParsing.length > 0) console.log("Failed results:", json.prettyPrint(failedInvoiceParsing.map((r) => r)));

  // const successFulDbWrites = awaitedRes.map((r) => r.unwrapOr(null)).filter((r) => r != null);

  // Result.combine(awaitedRes).mapErr((failedDbWrites) => {
  //   console.log("failedDbWrites", failedDbWrites);
  // });
  // console.log("successFulDbWrites", successFulDbWrites.length);

  const cachedResponses = invoices.filter((item) => item?.baseDetails);

  console.log(`total documents: ${invoiceResults.length}`);

  console.log(`cached responses: ${cachedResponses.length}/${invoices.length}`);
  console.error(`errors in invoice detection: ${failedInvoiceParsing.length}`);

  console.log(`success fully parsed invoices: ${successRes.length}/${invoiceResults.length}`);

  console.log("llm.totalUsage", llm.getUsageCost());

  // await prisma.invoiceImportItem.updateMany({
  //   where: {
  //     id: {
  //       in: results.map((r) => r.object.id),
  //     },
  //   },
  //   data: {
  //     isInvoice: true,
  //   },
  // });
}

cli.scafold({
  main,
});
