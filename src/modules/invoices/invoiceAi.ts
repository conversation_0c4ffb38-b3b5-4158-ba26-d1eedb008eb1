import { ExternalAttachment } from "braintrust";
import { z } from "zod";
import { AIModelId, llm } from "../ai/llm/llm";
import { braintrust } from "../tracing/braintrust";
import { GenericFile, MotlFile } from "../core/motlFile";
import { InvoiceImportItem } from "@/prisma/generated";
import { err, ok, okAsync, ResultAsync } from "neverthrow";
import { zodUtils } from "../core/utils/zodUtils";
import { pipe } from "remeda";
import * as R from "remeda";
import { ChatCompletionContentPart, ChatCompletionMessageParam } from "openai/resources/chat/completions.mjs";

import { motlLlm } from "../ai/llm/motlLlm";
import { currency } from "../core/currency";
import { monetary } from "../core/monetary";
import { cryptoCompareApi } from "../core/cryptoCompareApi";

export namespace invoiceAi {
  export const addressSchema = z.object({
    country: zodUtils.optional(z.string()),
    street: z.string(),
    houseNumber: z.string(),
    city: z.string(),
    postalCode: z.string(),
  });

  export const invoicePartySchema = z.object({
    companyName: z.string(),
    uidNumber: zodUtils.optional(z.string()),
    email: zodUtils.optional(z.string()),
    phone: zodUtils.optional(z.string()),
    address: addressSchema,
    contactPerson: zodUtils.optional(z.string()),
  });

  export const invoiceBaseDetailsSchema = z.object({
    invoiceDate: z.string().describe("YYYY-MM-DD"),
    invoiceReference: z.string(),
    contactPerson: zodUtils.optional(z.string()),
    vendor: invoicePartySchema,
    recipient: invoicePartySchema,

    // invoiceHasVatTAx: z.boolean().describe("Whether the invoice includes VAT"),
    // isReverseCharge: zodUtils.optional(z.boolean()).describe("Whether the VAT on the invoice is a reverse charge"),
  });

  export function parseInvoice(args: { file: GenericFile; baseDetailsAiModel?: AIModelId; invoiceValuesAiModel?: AIModelId }) {
    const baseDetailsAiModel = args.baseDetailsAiModel ?? "gpt-4.1";
    const invoiceValuesAiModel = args.invoiceValuesAiModel ?? "o4-mini";
    return braintrust.traceAsyncResult("parseInvoice", async (span) => {
      return invoiceAi
        .extractBaseDetails({
          file: args.file,
          aiModel: baseDetailsAiModel,
        })
        .andThen((baseDetailsRes) =>
          invoiceAi
            .extractInvoiceValues({
              aiModel: invoiceValuesAiModel,
              file: baseDetailsRes.file,
            })
            .map((invoiceValuesRes) => ({
              baseDetails: baseDetailsRes.llmRes,
              ...invoiceValuesRes,
            }))
            .andThen((res) => {
              span.log({
                output: {
                  hasTaxRes: res.hasTaxRes.object,
                  invoiceValues: res.invoiceValues,
                  baseDetails: res.baseDetails.object,
                },
              });
              if (res.hasTaxRes.object.currencyCode != "EUR") {
                return cryptoCompareApi
                  .getPriceForDay(res.hasTaxRes.object.currencyCode, "EUR", new Date(res.baseDetails.object.invoiceDate))
                  .map((conversionRate) => {
                    const invoiceValues = {
                      ...res.invoiceValues,
                      sourceAmountGross: res.invoiceValues.amountGross,
                      sourceAmountNet: res.invoiceValues.amountNet,
                      sourceAmountVat: res.invoiceValues.amountVat,
                      amountGross: res.invoiceValues.amountGross * conversionRate,
                      amountNet: res.invoiceValues.amountNet * conversionRate,
                      amountVat: res.invoiceValues.amountVat * conversionRate,
                      conversationRate: conversionRate,
                      items: res.invoiceValues.items.map((item) => ({
                        ...item,
                        sourcePriceGross: item.priceGross,
                        sourcePriceNet: item.priceNet,
                        sourceVatAmount: item.vatAmount,
                        priceGross: item.priceGross * conversionRate,
                        priceNet: item.priceNet * conversionRate,
                        vatAmount: item.vatAmount * conversionRate,
                        withSourceAmounts: true,
                      })),
                    } as const;

                    return {
                      ...res,
                      invoiceValues,
                    };
                  });
              }

              return okAsync({
                ...res,
              });
            })
            .mapErr((err) => {
              span.log({
                error: {
                  err,
                  code: "extractInvoiceValuesError",
                },
                output: {
                  type: "extractInvoiceValuesError",
                },
              });
              return err;
            })
        );
    });
  }

  export function extractBaseDetails(args: { file: GenericFile; aiModel: AIModelId }) {
    return braintrust.traceAsyncResult("extractBaseDetails", async (span) => {
      const file = await MotlFile.parse(args.file);

      const base64FileUrl = await file.getBase64FileUrl();
      const mimeType = await file.getFileType();

      const fileMessagePart: ChatCompletionContentPart =
        mimeType?.mime === "application/pdf"
          ? {
              type: "file",
              file: {
                filename: file.fileName,
                file_data: base64FileUrl,
              },
            }
          : {
              type: "image_url",
              image_url: {
                url: base64FileUrl,
              },
            };

      return llm
        .generateStructuredOutput({
          seed: 1,
          // temperature: 0,
          span: span,
          model: args.aiModel,
          schema: invoiceBaseDetailsSchema,
          messages: [
            {
              role: "system",
              content:
                "You get an invoice and you need to extract the base details like in the given json schema from the invoice. Only extract the fields which are stated on the invoice. Don't infer any fields.",
            },
            {
              role: "user",
              content: [
                fileMessagePart,
                // {
                //   type: "text",
                //   text: "Invoice\n\n" + args.item.text,
                // },
              ],
            },
            // {
            //   role: "user",
            //   content: `Can you extract the base details like in the given json schema from the following invoice. Only extract the fields which are stated on the invoice. Don't infer any fields.\n\nInvoice\n"""${args.item.text}"""`,
            // },
          ],
        })
        .then((llmRes) => {
          span.log({
            output: {
              llmRes,
            },
          });

          return ResultAsync.fromSafePromise(file.getHash()).andThen((fileHash) =>
            llmRes.map((res) => ({
              fileHash,
              llmRes: res,
              file,
            }))
          );
        });
    });
  }

  export function extractInvoiceValues(args: { aiModel: AIModelId; file: MotlFile }) {
    const doesInvoiceHaveVATSchema = z.object({
      invoiceHasVatTAx: z
        .boolean()
        .describe("Whether the invoice includes VAT. set to true only if the invoice **explicitly lists a VAT amount greater than 0**"),
      isReverseCharge: z.boolean().describe("Whether the VAT is a reverse charge"),
      currencyCode: z
        .string()
        .describe(
          `The currency code in ISO 4217 format. Additional allowed are these crypto currencies ${Object.keys(currency.additionalCurrencies).join(", ")}.`
        )
        .refine(
          (value) => currency.allCurrencies[value],
          (value) => ({
            message: `The currency code ${value} is not a valid ISO 4217 or crypto currency code.`,
          })
        ),
    });

    return braintrust
      .traceAsyncResult("doesInvoiceHaveVAT", async (span) => {
        const base64FileUrl = await args.file.getBase64FileUrl();
        const mimeType = await args.file.getFileType();

        const fileMessagePart: ChatCompletionContentPart =
          mimeType?.mime === "application/pdf"
            ? {
                type: "file",
                file: {
                  filename: args.file.fileName,
                  file_data: base64FileUrl,
                },
              }
            : {
                type: "image_url",
                image_url: {
                  url: base64FileUrl,
                },
              };
        return llm.generateStructuredOutput({
          span,
          // @ts-ignore
          model: "gpt-4.1-mini" ?? args.aiModel,
          schema: doesInvoiceHaveVATSchema,
          messages: [
            {
              role: "system",
              content: `You are given an invoice and you need to determine if the invoice includes VAT and if it has reverse charge.
also extract the currency which is used for the invoice values and return it in the currencyCode field.`,
            },
            {
              role: "user",
              content: [fileMessagePart],
            },
          ],
        });
      })
      .andThen((hasTaxRes) => {
        if (hasTaxRes.object.invoiceHasVatTAx) {
          return ResultAsync.fromSafePromise(
            extractInvoiceValuesWithVAT({
              aiModel: args.aiModel,
              file: args.file,
            })
          ).andThen((r) =>
            r.map((res) => ({
              hasTaxRes,
              invoiceValues: res,
            }))
          );
        } else {
          return ResultAsync.fromSafePromise(
            extractInvoiceValuesWithoutVAT({
              aiModel: args.aiModel,
              file: args.file,
            })
          ).andThen((r) =>
            r.map((res) => ({
              hasTaxRes,
              invoiceValues: res,
            }))
          );
        }
      });
  }

  export async function extractInvoiceValuesWithVAT(args: { aiModel: AIModelId; file: MotlFile }) {
    const invoiceVATValuesSchema = z.object({
      invoiceItems: z.array(
        z.object({
          name: z.string().describe("The name of the invoice item"),
          description: zodUtils.optional(z.string()).describe("The description of the invoice item"),
          amountGross: zodUtils.optional(z.number()).describe("The amount of the invoice item including VAT"),
          amountNet: zodUtils.optional(z.number()).describe("The amount of the invoice item excluding VAT"),
          vatRate: z.number().describe("The VAT rate of the invoice item"),
          // vatAmount: zodUtils.optional(z.number()).describe("The VAT amount of the invoice item"),
        })
      ),
      totalAmountGross: z.number().describe("The total amount of the invoice including VAT"),
      totalAmountNet: zodUtils.optional(z.number()).describe("The total amount of the invoice excluding VAT"),
      totalAmountVAT: zodUtils.optional(z.number()).describe("The total amount of the VAT"),
    });

    const base64FileUrl = await args.file.getBase64FileUrl();
    const mimeType = await args.file.getFileType();

    const fileMessagePart: ChatCompletionContentPart =
      mimeType?.mime === "application/pdf"
        ? {
            type: "file",
            file: {
              filename: args.file.fileName,
              file_data: base64FileUrl,
            },
          }
        : {
            type: "image_url",
            image_url: {
              url: base64FileUrl,
            },
          };

    const messages = [
      {
        role: "system",
        content: `You are an Austrian accountant. You are given an invoice and you need to extract the values from the invoice.
Important:
- Only extract values from the invoice which are stated on the invoice. Don't calculate any values.
- you need to either provide amountGross or amountNet at the item level.
- if there is a global vat rate, set it at all items
- if the invoice differentiates between different vat rates, set the vat rate at the item level and don't set the global vat rate
`,
      },
      {
        role: "user",
        content: [fileMessagePart],
      },
    ] as ChatCompletionMessageParam[];

    const res = await motlLlm.generateObjectWithValidation({
      aiModel: args.aiModel,
      schema: invoiceVATValuesSchema,
      messages,
      maxRetries: 3,
      traceName: "extractInvoiceValues",
      validateAndTransform: (data: z.infer<typeof invoiceVATValuesSchema>) => {
        const invalidItems = data.invoiceItems.some((item) => item.amountGross === null && item.amountNet === null);
        if (invalidItems) {
          return err({
            message: "you need to provide either amountGross or amountNet at the item level",
          });
        }

        // Validate total amounts if all items have the same type
        if (data.invoiceItems.every((item) => item.amountGross !== null) && data.totalAmountGross !== null) {
          const totalAmountGrossFromItems = R.sumBy(data.invoiceItems, (item) => item.amountGross!);
          if (!monetary.equals(totalAmountGrossFromItems, data.totalAmountGross)) {
            return err({
              message: `the total amount gross of the invoice items does not match the total amount gross of the invoice. ${totalAmountGrossFromItems.toFixed(
                2
              )} !== ${data.totalAmountGross.toFixed(2)}`,
            });
          }
        }

        if (data.invoiceItems.every((item) => item.amountNet !== null) && data.totalAmountNet !== null) {
          const totalAmountNetFromItems = R.sumBy(data.invoiceItems, (item) => item.amountNet!);
          if (!monetary.equals(totalAmountNetFromItems, data.totalAmountNet)) {
            return err({
              message: "the total amount net of the invoice items does not match the total amount net of the invoice",
            });
          }
        }

        // Transform and calculate missing values
        const items = pipe(
          data.invoiceItems,
          R.map((item) => {
            const vatFraction = item.vatRate! / 100;
            return {
              ...item,
              vatFraction,
            };
          }),
          R.map((item) => {
            if (item.amountNet == null) {
              return {
                ...item,
                priceNet: item.amountGross! / (1 + item.vatFraction!),
                priceGross: item.amountGross!,
              };
            } else if (item.amountGross == null) {
              return {
                ...item,
                priceGross: item.amountNet! * (1 + item.vatFraction!),
                priceNet: item.amountNet!,
              };
            } else {
              return {
                ...item,
                priceGross: item.amountGross!,
                priceNet: item.amountNet!,
              };
            }
          }),
          R.map((item) => {
            const vatAmount = item.priceGross! - item.priceNet!;
            return {
              ...item,
              vatAmount,
            };
          })
        );

        const totalAmountNet = data.totalAmountNet ?? R.sumBy(items, (item) => item.priceNet!);
        const totalAmountVAT = data.totalAmountVAT ?? R.sumBy(items, (item) => item.vatAmount!);

        return ok({
          items,
          amountGross: data.totalAmountGross,
          amountNet: totalAmountNet,
          amountVat: totalAmountVAT,
          raw: data,
          hasVAT: true,
        });
      },
    });

    return res;
  }

  export async function extractInvoiceValuesWithoutVAT(args: { aiModel: AIModelId; file: MotlFile }) {
    const invoiceVATValuesSchema = z.object({
      invoiceItems: z.array(
        z.object({
          name: z.string().describe("The name of the invoice item"),
          description: zodUtils.optional(z.string()).describe("The description of the invoice item"),
          amount: z.number().describe("The total amount of the invoice item"),
          // vatAmount: zodUtils.optional(z.number()).describe("The VAT amount of the invoice item"),
        })
      ),
      totalAmount: z.number().describe("The total amount of the invoice"),
    });

    const base64FileUrl = await args.file.getBase64FileUrl();
    const mimeType = await args.file.getFileType();

    const fileMessagePart: ChatCompletionContentPart =
      mimeType?.mime === "application/pdf"
        ? {
            type: "file",
            file: {
              filename: args.file.fileName,
              file_data: base64FileUrl,
            },
          }
        : {
            type: "image_url",
            image_url: {
              url: base64FileUrl,
            },
          };

    const messages = [
      {
        role: "system",
        content: `You are an Austrian accountant. You are given an invoice and you need to extract the values from the invoice.
Important:
- Only extract values from the invoice which are stated on the invoice. Don't calculate any values.
- you need to either provide amountGross or amountNet at the item level.
`,
      },
      {
        role: "user",
        content: [fileMessagePart],
      },
    ] as ChatCompletionMessageParam[];

    const res = await motlLlm.generateObjectWithValidation({
      aiModel: args.aiModel,
      schema: invoiceVATValuesSchema,
      messages,
      maxRetries: 3,
      traceName: "extractInvoiceValues",
      validateAndTransform: (data: z.infer<typeof invoiceVATValuesSchema>) => {
        // Transform and calculate missing values

        const totalAmountFromItems = R.sumBy(data.invoiceItems, (item) => item.amount);
        if (!monetary.equals(totalAmountFromItems, data.totalAmount)) {
          return err({
            message: "the total amount of the invoice items does not match the total amount of the invoice",
          });
        }

        const items = data.invoiceItems.map((item) => ({
          ...item,
          priceGross: item.amount,
          priceNet: item.amount,
          amountVat: 0,
          vatRate: 0,
          vatAmount: 0,
        }));

        return ok({
          items,
          amountGross: data.totalAmount,
          amountNet: data.totalAmount,
          amountVat: 0,
          raw: data,
          hasVAT: false,
        });
      },
    });

    return res;
  }
}

// CLI functions commented out to avoid build issues
// These should be moved to a separate CLI script file

// async function compare() {
//   // CLI comparison function - moved to separate script
// }

// async function deleteInvoiceBaseDetails() {
//   const res = await prisma.invoiceBaseDetails.deleteMany({});
//   console.log("deleted invoiceBaseDetails:", res);
// }
