import { Prisma } from "@/prisma/generated";
import { prisma } from "@/prisma/prisma";
import { ResultAsync } from "neverthrow";
import { cryptoCompareApi } from "../core/cryptoCompareApi";

export async function getOrCreateVendor(args: { vendor: Prisma.VendorCreateInput }) {
  const ors: Prisma.VendorWhereInput[] = [
    {
      name: args.vendor.name,
    },
  ];

  if (args.vendor.vatNumber && args.vendor.vatNumber.length > 6) {
    ors.push({
      vatNumber: args.vendor.vatNumber,
    });
  }

  const vendor = await prisma.vendor.findFirst({
    where: {
      OR: ors,
    },
  });

  if (vendor) {
    return vendor;
  }

  const createdVendor = await prisma.vendor.create({
    data: {
      ...args.vendor,
    },
  });

  return createdVendor;
}

export async function getOrCreateImportItem(args: { importItem: Prisma.InvoiceImportItemCreateInput }) {
  const importItem = await prisma.invoiceImportItem.findFirst({
    where: {
      OR: [
        {
          fileHash: args.importItem.fileHash,
        },
        {
          externalId: args.importItem.externalId,
        },
      ],
    },
  });

  if (importItem) return importItem;

  const createdImportItem = await prisma.invoiceImportItem.create({
    data: {
      ...args.importItem,
    },
  });

  return createdImportItem;
}

export function createInvoice(args: {
  invoice: Omit<Prisma.InvoiceCreateInput, "vendor" | "importItem" | "vendorId" | "importItemId" | "date"> & { date: Date };
  vendor: Prisma.VendorCreateInput;
  importItem: Prisma.InvoiceImportItemCreateInput;
}) {
  return ResultAsync.fromPromise(
    (async () => {
      const importItem = args.importItem
        ? await getOrCreateImportItem({
            importItem: args.importItem,
          })
        : undefined;

      const vendor = await getOrCreateVendor({ vendor: args.vendor });

      const ors: Prisma.InvoiceWhereInput[] = [
        {
          vendorId: vendor.id,
          invoiceReference: args.invoice.invoiceReference,
        },
        // {
        //   amountGross: args.invoice.amountGross,
        //   date: args.invoice.date,
        //   vendorId: vendor.id,
        // },
      ];

      if (importItem) {
        ors.push({
          importItemId: importItem.id,
        });
      }

      const existingInvoice = await prisma.invoice.findFirst({
        where: {
          OR: ors,
        },
      });

      if (existingInvoice) {
        if (true) {
          const deleteRes = await ResultAsync.fromPromise(
            prisma.invoice.delete({
              where: {
                id: existingInvoice.id,
              },
            }),
            (err) => ({
              type: "error",
              code: "createInvoice.deleteInvoice",
              message: "error deleting invoice",
              context: {
                existingInvoice,
                query: ors,
                newInvoice: args.invoice,
              },
            })
          );
          if (deleteRes.isErr()) {
            return deleteRes;
          }
        }
        // else {
        //   console.log("invoice already exists", existingInvoice.id);
        //   return existingInvoice;
        // }
      }

      let conversionRateToBaseCurrency: number | undefined;
      if (args.invoice.currencyCode !== "EUR") {
        const res = await cryptoCompareApi.getPriceForDay(args.invoice.currencyCode, "EUR", args.invoice.date);
        if (res.isErr()) {
          console.error("error getting conversion rate to base currency", res.error);
          return res;
        }
      }

      const invoice = await ResultAsync.fromPromise(
        prisma.invoice.create({
          data: {
            ...args.invoice,
            vendorId: vendor.id,
            importItemId: importItem?.id,
          } as any,
        }),
        (err) =>
          ({
            type: "error",
            code: "createInvoice.createInvoice",
            message: "error creating invoice",
            err,
            context: {
              invoice: args.invoice,
              vendor,
              importItem,
              conversionRateToBaseCurrency,
            },
          } as const)
      );

      if (invoice.isOk()) {
        // Trigger auto-reconciliation for the new invoice
        const { autoReconcileForInvoice } = await import("@/modules/reconciliation/reconciliationService");
        const reconcileResult = await autoReconcileForInvoice(invoice.value.id);
        if (reconcileResult.isErr()) {
          console.error("Auto-reconciliation failed for new invoice:", reconcileResult.error);
          // Don't fail the invoice creation if reconciliation fails
        } else if (reconcileResult.value > 0) {
          console.log(`Auto-matched ${reconcileResult.value} transfers for new invoice ${invoice.value.id}`);
        }
      }

      return invoice;
    })(),
    (err) => {
      return {
        type: "error",
        code: "createInvoice.createInvoice",
        message: "error creating invoice",
        err,
        context: {
          invoice: args.invoice,
          vendor: args.vendor,
          importItem: args.importItem,
        },
      };
    }
  );
}

export const invoiceService = {
  getOrCreateVendor,
  getOrCreateImportItem,
  createInvoice,
};
