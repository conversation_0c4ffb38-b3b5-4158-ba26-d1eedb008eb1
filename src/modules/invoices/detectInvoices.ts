import yargs from "yargs";
import { prisma } from "../../prisma/prisma";
import { initLogger, init, traced, Attachment } from "braintrust";
import OpenAI from "openai";
import { z } from "zod";
import { zodResponseFormat } from "openai/helpers/zod";
import * as lfi from "lfi";
import { AIModelId, llm } from "../ai/llm/llm";
import { context, trace } from "@opentelemetry/api";
import { braintrust } from "../tracing/braintrust";
import { MotlFile } from "../core/motlFile";
import { ok, Result } from "neverthrow";
import { cli } from "../core/utils/cliUtils";
import { InvoiceImportItem } from "../../prisma/generated";
import { ChatCompletionContentPart } from "openai/resources/index.mjs";

export type LLMModels = "gemini-2.5-flash-preview-05-20";

export function detectInvoice(args: { item: InvoiceImportItem; file: MotlFile; aiModel: AIModelId }) {
  const { item, file } = args;
  return braintrust.traceAsyncResult(
    "detectInvoice",
    // @ts-ignore
    async (span) => {
      const isInvoiceSchema = z.object({
        documentType: z.enum(["invoice", "mahnung", "receipt", "none"]),
      });

      const base64FileUrl = await file.getBase64FileUrl();

      const mimeType = await file.getFileType();

      const fileMessagePart: ChatCompletionContentPart =
        mimeType?.mime === "application/pdf"
          ? {
              type: "file",
              file: {
                filename: file.fileName,
                file_data: base64FileUrl,
              },
            }
          : {
              type: "image_url",
              image_url: {
                url: base64FileUrl,
              },
            };

      const unwrappeRes = await llm.generateStructuredOutput({
        span: span,
        model: args.aiModel,
        schema: isInvoiceSchema,
        messages: [
          {
            role: "system",
            content: `You get an document and you need to categorize it as an invoice, mahnung, receipt or none.`,
          },
          {
            role: "user",
            content: [
              // {
              //   type: "text",
              //   text: `Invoice\n"""${item.text}"""`,
              // },
              fileMessagePart,
            ],
          },
        ],
        seed: 1,
      });

      if (unwrappeRes.isErr()) {
        return unwrappeRes;
      }

      const res = unwrappeRes.value;

      const isCached = res.isCached;

      const isInvoice = res.object.documentType === "invoice" || res.object.documentType === "receipt";

      span.log({
        output: {
          isInvoice,
          documentType: res.object.documentType,
          isCached,
        },
      });

      return ok({ isInvoice, isCached, res, item });
    }
  );
}

async function detectInvoices(args: { invoiceImportItems: InvoiceImportItem[]; aiModel: AIModelId }) {
  const { invoiceImportItems } = args;
  return await braintrust.trace("detectInvoices", async (rootSpan) => {
    rootSpan.log({
      input: {
        invoiceImportItems: invoiceImportItems.length,
      },
      metadata: {
        aiModel: args.aiModel,
      },
    });
    const isInvoicesRes = await lfi.pipe(
      lfi.asConcur(invoiceImportItems),
      lfi.mapConcur(async (item) => {
        return await braintrust.trace("detectInvoice", async (span) => {
          const file = await MotlFile.parse(item.fileUrl);
          span.log({
            input: {
              item,
            },
            metadata: {
              aiModel: args.aiModel,
            },
          });

          const isInvoiceSchema = z.object({
            documentType: z.enum(["invoice", "mahnung", "none"]),
          });

          const unwrappeRes = await llm.generateObject({
            span: span,
            model: args.aiModel,
            schema: isInvoiceSchema,
            messages: [
              // {
              //   role: "system",
              //   content: "You are an Austrian accountant.",
              // },
              {
                role: "user",
                content: `Can you categorize the following document as an invoice, mahnung or none:\n\nInvoice\n"""${item.text}"""`,
              },
            ],
            seed: 1,
          });

          if (unwrappeRes.isErr()) {
            return unwrappeRes;
          }

          const res = unwrappeRes.value;

          const isCached = res.isCached;

          const isInvoice = res.object.documentType === "invoice";

          span.log({
            output: {
              isInvoice,
              documentType: res.object.documentType,
              isCached,
            },
          });

          return ok({ isInvoice, isCached, res, item });
        });
      }),

      lfi.reduceConcur(lfi.toArray())
    );

    console.log(`checked documents: ${isInvoicesRes.length}`);

    const successRes = isInvoicesRes
      .filter((res) => {
        return res.isOk();
      })
      .map((res) => res.value);

    const failedResults = isInvoicesRes.filter((res) => res.isErr());
    if (failedResults.length > 0) {
      console.error(`errors in invoice detection: ${failedResults.length}`);
    }

    const detectedInvoices = successRes.filter((item) => item.isInvoice);
    const cachedResponses = successRes.filter((item) => item.isCached);

    console.log(`cached responses: ${cachedResponses.length}/${successRes.length}`);

    console.log(`invoices detected: ${detectedInvoices.length}/${invoiceImportItems.length}`);

    return successRes;
  });
}

async function compare() {
  const cliArgs = await yargs(process.argv.slice(2)).option("limit", { type: "number", default: 5 }).parse();

  // const logger = await braintrust.getLogger();

  const invoiceImportItems = await prisma.invoiceImportItem.findMany({
    where: {
      text: {
        not: "",
      },
    },
    orderBy: {
      createdAt: "asc",
    },
    take: cliArgs.limit === -1 ? undefined : cliArgs.limit,
  });
  const resultGpt41 = await detectInvoices({
    aiModel: "gpt-4.1-mini",
    invoiceImportItems: invoiceImportItems,
  });

  const resultGemini = await detectInvoices({
    aiModel: "gemini-2.5-flash-preview-05-20",
    invoiceImportItems: invoiceImportItems,
  });

  // Compare isInvoice results between the two models
  console.log("\n=== AI Model Comparison Results ===");
  console.log(`GPT-4.1-mini results: ${resultGpt41.length} items`);
  console.log(`Gemini-2.5-flash results: ${resultGemini.length} items`);

  // Create maps for easier comparison
  let agreements = 0;
  let disagreements = 0;
  const differences: Array<{
    item: InvoiceImportItem;
    gptResult: boolean;
    geminiResult: boolean;
  }> = [];

  // Compare results for each item
  for (const gptResult of resultGpt41) {
    const geminiResult = resultGemini.find((r) => r.item.id === gptResult.item.id);

    if (geminiResult !== undefined) {
      if (gptResult.isInvoice === geminiResult.isInvoice) {
        agreements++;
      } else {
        disagreements++;
        differences.push({
          item: gptResult.item,
          gptResult: gptResult.isInvoice,
          geminiResult: geminiResult.isInvoice,
        });
      }
    }
  }

  console.log(`\nAgreements: ${agreements}`);
  console.log(`Disagreements: ${disagreements}`);
  console.log(`Agreement rate: ${((agreements / (agreements + disagreements)) * 100).toFixed(1)}%`);

  if (differences.length > 0) {
    console.log("\n=== Disagreements Details ===");
    differences.forEach((diff, index) => {
      console.log(`\n${index + 1}. Item ID: ${diff.item.id}`);
      console.log(`   GPT-4.1-mini: ${diff.gptResult ? "✅ Invoice" : "❌ Not Invoice"}`);
      console.log(`   Gemini-2.5: ${diff.geminiResult ? "✅ Invoice" : "❌ Not Invoice"}`);
      console.log(`   Link: ${diff.item.fileUrl}`);
      console.log(`   Text preview: ${diff.item.text.slice(0, 100)}...`);
    });
  }

  // Summary statistics
  const gptInvoiceCount = resultGpt41.filter((r) => r.isInvoice).length;
  const geminiInvoiceCount = resultGemini.filter((r) => r.isInvoice).length;

  const allMahnungen = resultGpt41.filter((r) => r.isInvoice && r.item.text.toLowerCase().includes("mahnung"));

  const mahnungGpt = resultGpt41.filter((r) => r.isInvoice && r.item.text.toLowerCase().includes("mahnung"));
  const mahnungGemini = resultGemini.filter((r) => r.isInvoice && r.item.text.toLowerCase().includes("mahnung"));

  console.log("\n=== Mahnung Detection ===");
  console.log(`GPT-4.1-mini detected ${mahnungGpt.length} mahnung(s)`);
  console.log(`Gemini-2.5-flash detected ${mahnungGemini.length} mahnung(s)`);

  console.log("\n=== Summary Statistics ===");
  console.log(`GPT-4.1-mini detected invoices: ${gptInvoiceCount}/${resultGpt41.length} (${((gptInvoiceCount / resultGpt41.length) * 100).toFixed(1)}%)`);
  console.log(
    `Gemini-2.5-flash detected invoices: ${geminiInvoiceCount}/${resultGemini.length} (${((geminiInvoiceCount / resultGemini.length) * 100).toFixed(1)}%)`
  );

  const mahhnungenO4Mini = await detectInvoices({
    aiModel: "gpt-4o-mini",
    invoiceImportItems: allMahnungen.map((r) => r.item),
  });

  console.log("\n=== Mahnung Detection with GPT-4o-mini ===");
  const mahhnungenO4MiniCount = mahhnungenO4Mini.filter((r) => r.isInvoice && r.item.text.toLowerCase().includes("mahnung"));
  console.log(`GPT-4o-mini classified ${mahhnungenO4MiniCount.length} mahnung(s) as invcoies`);
  console.log(
    `GPT-4o-mini classified mahungen as invoices`,
    mahhnungenO4MiniCount.map((i) => i.item.fileUrl)
  );

  for (const item of allMahnungen) {
    console.log(`Mahnung detected: ${item.item.fileUrl}`);

    const gpt41result = resultGpt41.find((r) => r.item.id === item.item.id);
    const geminiResult = resultGemini.find((r) => r.item.id === item.item.id);
    const gptO4MiniResult = mahhnungenO4Mini.find((r) => r.item.id === item.item.id);
    console.log(`\tGPT-4.1-mini: ${gpt41result?.isInvoice ? "✅ Invoice" : "❌ Not Invoice"}`);
    console.log(`\tGemini-2.5: ${geminiResult?.isInvoice ? "✅ Invoice" : "❌ Not Invoice"}`);
    console.log(`\tGPT-4o-mini: ${gptO4MiniResult?.isInvoice ? "✅ Invoice" : "❌ Not Invoice"}`);
    console.log(`\tText preview: ${item.item.text.slice(0, 100)}...`);
  }
}

async function main() {
  const cliArgs = await yargs(process.argv.slice(2)).option("limit", { type: "number", default: 1 }).parse();

  // const logger = await braintrust.getLogger();

  const invoiceImportItems = await prisma.invoiceImportItem.findMany({
    where: {
      text: {
        not: "",
      },
    },
    orderBy: {
      createdAt: "asc",
    },
    take: cliArgs.limit === -1 ? undefined : cliArgs.limit,
  });
  const results = await detectInvoices({
    aiModel: "gpt-4o-mini",
    invoiceImportItems: invoiceImportItems,
  });

  await prisma.invoiceImportItem.updateMany({
    where: {
      id: {
        in: results.map((r) => r.item.id),
      },
    },
    data: {
      isInvoice: true,
    },
  });
}

// cli.scafold({
//   main,
//   compare,
// });
