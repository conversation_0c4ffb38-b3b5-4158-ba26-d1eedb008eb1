import { termost } from "termost";
import * as R from "remeda";

const program = termost({
  name: "fetch-emails",
  description: "CLI for fetch emails",
  version: "0.0.1",
  onException(error) {
    console.error(`Error logic ${error.message}`);
  },
  onShutdown() {
    console.log("Clean-up logic");
  },
});

program
  .command({
    name: "email",
    description: "Agent",
  })
  .task({
    label: "Fetch emails",
    async handler() {

      
    },
  });
