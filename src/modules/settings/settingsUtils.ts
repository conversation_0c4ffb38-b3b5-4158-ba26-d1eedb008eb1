/**
 * Utility functions for working with accounting year settings
 */

export interface AccountingYearStart {
  day: number; // 1-31
  month: number; // 1-12
}

/**
 * Convert day and month integers to a Date object for the current year
 */
export function accountingYearStartToDate(day: number, month: number, year?: number): Date {
  const targetYear = year ?? new Date().getFullYear();
  return new Date(targetYear, month - 1, day); // month is 0-indexed in Date constructor
}

/**
 * Convert a Date object to day and month integers
 */
export function dateToAccountingYearStart(date: Date): AccountingYearStart {
  return {
    day: date.getDate(),
    month: date.getMonth() + 1, // getMonth() returns 0-11
  };
}

/**
 * Get the accounting year start date for a given year
 */
export function getAccountingYearStartForYear(
  settings: AccountingYearStart,
  year: number
): Date {
  return accountingYearStartToDate(settings.day, settings.month, year);
}

/**
 * Determine which accounting year a given date falls into
 */
export function getAccountingYearForDate(
  date: Date,
  settings: AccountingYearStart
): number {
  const year = date.getFullYear();
  const accountingYearStart = getAccountingYearStartForYear(settings, year);
  
  // If the date is before the accounting year start, it belongs to the previous accounting year
  if (date < accountingYearStart) {
    return year - 1;
  }
  
  return year;
}

/**
 * Get the start and end dates for a given accounting year
 */
export function getAccountingYearRange(
  accountingYear: number,
  settings: AccountingYearStart
): { start: Date; end: Date } {
  const start = getAccountingYearStartForYear(settings, accountingYear);
  const end = getAccountingYearStartForYear(settings, accountingYear + 1);
  
  // End date is one day before the next accounting year starts
  end.setDate(end.getDate() - 1);
  
  return { start, end };
}

/**
 * Format accounting year start as a readable string
 */
export function formatAccountingYearStart(settings: AccountingYearStart): string {
  const date = accountingYearStartToDate(settings.day, settings.month);
  return date.toLocaleDateString('en-US', { 
    month: 'long', 
    day: 'numeric' 
  });
}

/**
 * Common accounting year presets
 */
export const ACCOUNTING_YEAR_PRESETS = {
  CALENDAR_YEAR: { day: 1, month: 1 }, // January 1st
  FISCAL_YEAR_US: { day: 1, month: 10 }, // October 1st
  FISCAL_YEAR_UK: { day: 6, month: 4 }, // April 6th
  FISCAL_YEAR_AUSTRALIA: { day: 1, month: 7 }, // July 1st
  FISCAL_YEAR_CANADA: { day: 1, month: 4 }, // April 1st
} as const;
