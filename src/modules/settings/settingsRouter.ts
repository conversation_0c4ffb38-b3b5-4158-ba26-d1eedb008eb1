import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";

export const settingsRouter = createTRPCRouter({
  get: publicProcedure.query(async ({ ctx }) => {
    try {
      // Try to get the first (and should be only) settings record
      let settings = await ctx.prisma.settings.findFirst();

      // If no settings exist, create default settings
      if (!settings) {
        // Default accounting year starts on January 1st (day: 1, month: 1)
        settings = await ctx.prisma.settings.create({
          data: {
            accountingYearStartDay: 1,
            accountingYearStartMonth: 1,
          },
        });
      }

      return settings;
    } catch (error) {
      console.error("Failed to get settings:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get settings",
        cause: error,
      });
    }
  }),

  update: publicProcedure
    .input(
      z.object({
        accountingYearStartDay: z.number().min(1).max(31),
        accountingYearStartMonth: z.number().min(1).max(12),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Get the first settings record or create one if it doesn't exist
        let settings = await ctx.prisma.settings.findFirst();

        if (settings) {
          // Update existing settings
          settings = await ctx.prisma.settings.update({
            where: { id: settings.id },
            data: {
              accountingYearStartDay: input.accountingYearStartDay,
              accountingYearStartMonth: input.accountingYearStartMonth,
            },
          });
        } else {
          // Create new settings
          settings = await ctx.prisma.settings.create({
            data: {
              accountingYearStartDay: input.accountingYearStartDay,
              accountingYearStartMonth: input.accountingYearStartMonth,
            },
          });
        }

        return settings;
      } catch (error) {
        console.error("Failed to update settings:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update settings",
          cause: error,
        });
      }
    }),
});
