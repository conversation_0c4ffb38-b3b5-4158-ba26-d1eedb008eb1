import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { CryptoExpenseService } from "./cryptoExpenseService";
import { CryptoExpenseQueries } from "@/modules/flipside/cryptoExpenseQueries";

// Input validation schemas
const importBuybacksSchema = z.object({
  companyWallets: z.array(z.string()).optional(),
  targetMints: z.array(z.string()).optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  createTransactions: z.boolean().default(false),
  accountId: z.string().optional(),
});

const importFloorSweepsSchema = z.object({
  companyWallets: z.array(z.string()).optional(),
  collectionIds: z.array(z.string()).min(1, "At least one collection ID is required"),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  createTransactions: z.boolean().default(false),
  accountId: z.string().optional(),
});

const importLiquidityPoolActionsSchema = z.object({
  companyWallets: z.array(z.string()).optional(),
  liquidityPoolTokenMints: z.array(z.string()).min(1, "At least one token mint is required"),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  createTransactions: z.boolean().default(false),
  accountId: z.string().optional(),
});

const getCryptoExpensesSchema = z.object({
  type: z.enum(["BUYBACK", "FLOOR_SWEEP", "LIQUIDITY_POOL", "OTHER"]).optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0),
});

const getCryptoExpenseStatsSchema = z.object({
  type: z.enum(["BUYBACK", "FLOOR_SWEEP", "LIQUIDITY_POOL", "OTHER"]).optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
});

const deleteCryptoExpensesSchema = z.object({
  type: z.enum(["BUYBACK", "FLOOR_SWEEP", "LIQUIDITY_POOL", "OTHER"]).optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  txIds: z.array(z.string()).optional(),
});

const previewBuybacksSchema = z.object({
  companyWallets: z.array(z.string()).optional(),
  targetMints: z.array(z.string()).optional(),
  fromDate: z.date().optional(),
  toDate: z.date().optional(),
  limit: z.number().min(1).max(100).default(10),
});

export const cryptoExpenseRouter = createTRPCRouter({
  /**
   * Import buyback expenses from Flipside API
   */
  importBuybacks: publicProcedure
    .input(importBuybacksSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Validate that if createTransactions is true, accountId must be provided
        if (input.createTransactions && !input.accountId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "accountId is required when createTransactions is true",
          });
        }

        const service = new CryptoExpenseService(ctx.prisma);
        const result = await service.importBuybacks(input);

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error.message,
            cause: result.error.error,
          });
        }

        return result.value;
      } catch (error) {
        console.error("Failed to import buybacks:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to import buybacks",
          cause: error,
        });
      }
    }),

  /**
   * Preview buyback data from Flipside without importing
   */
  previewBuybacks: publicProcedure
    .input(previewBuybacksSchema)
    .query(async ({ input }) => {
      try {
        const result = await CryptoExpenseQueries.getBuybacks(
          input.companyWallets,
          input.targetMints,
          input.fromDate,
          input.toDate
        );

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: `Failed to fetch buyback preview: ${result.error.message}`,
            cause: result.error,
          });
        }

        const records = result.value.slice(0, input.limit);
        const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);

        return {
          records,
          summary: {
            totalRecords: result.value.length,
            previewRecords: records.length,
            totalUsd,
            dateRange: {
              from: records.length > 0 ? new Date(Math.min(...records.map(r => new Date(r.block_timestamp).getTime()))) : null,
              to: records.length > 0 ? new Date(Math.max(...records.map(r => new Date(r.block_timestamp).getTime()))) : null,
            },
          },
        };
      } catch (error) {
        console.error("Failed to preview buybacks:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to preview buybacks",
          cause: error,
        });
      }
    }),

  /**
   * Import floor sweep expenses from Flipside API
   */
  importFloorSweeps: publicProcedure
    .input(importFloorSweepsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Validate that if createTransactions is true, accountId must be provided
        if (input.createTransactions && !input.accountId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "accountId is required when createTransactions is true",
          });
        }

        const service = new CryptoExpenseService(ctx.prisma);
        const result = await service.importFloorSweeps(input);

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error.message,
            cause: result.error.error,
          });
        }

        return result.value;
      } catch (error) {
        console.error("Failed to import floor sweeps:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to import floor sweeps",
          cause: error,
        });
      }
    }),

  /**
   * Preview floor sweep data from Flipside without importing
   */
  previewFloorSweeps: publicProcedure
    .input(z.object({
      companyWallets: z.array(z.string()).optional(),
      collectionIds: z.array(z.string()).min(1, "At least one collection ID is required"),
      fromDate: z.date().optional(),
      toDate: z.date().optional(),
      limit: z.number().min(1).max(100).default(10),
    }))
    .query(async ({ input }) => {
      try {
        const result = await CryptoExpenseQueries.getFloorSweeps(
          input.companyWallets,
          input.collectionIds,
          input.fromDate,
          input.toDate
        );

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: `Failed to fetch floor sweep preview: ${result.error.message}`,
            cause: result.error,
          });
        }

        const records = result.value.slice(0, input.limit);
        const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);

        return {
          records,
          summary: {
            totalRecords: result.value.length,
            previewRecords: records.length,
            totalUsd,
            dateRange: {
              from: records.length > 0 ? new Date(Math.min(...records.map(r => new Date(r.block_timestamp).getTime()))) : null,
              to: records.length > 0 ? new Date(Math.max(...records.map(r => new Date(r.block_timestamp).getTime()))) : null,
            },
          },
        };
      } catch (error) {
        console.error("Failed to preview floor sweeps:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to preview floor sweeps",
          cause: error,
        });
      }
    }),

  /**
   * Import liquidity pool action expenses from Flipside API
   */
  importLiquidityPoolActions: publicProcedure
    .input(importLiquidityPoolActionsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Validate that if createTransactions is true, accountId must be provided
        if (input.createTransactions && !input.accountId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "accountId is required when createTransactions is true",
          });
        }

        const service = new CryptoExpenseService(ctx.prisma);
        const result = await service.importLiquidityPoolActions(input);

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error.message,
            cause: result.error.error,
          });
        }

        return result.value;
      } catch (error) {
        console.error("Failed to import liquidity pool actions:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to import liquidity pool actions",
          cause: error,
        });
      }
    }),

  /**
   * Preview liquidity pool action data from Flipside without importing
   */
  previewLiquidityPoolActions: publicProcedure
    .input(z.object({
      companyWallets: z.array(z.string()).optional(),
      liquidityPoolTokenMints: z.array(z.string()).min(1, "At least one token mint is required"),
      fromDate: z.date().optional(),
      toDate: z.date().optional(),
      limit: z.number().min(1).max(100).default(10),
    }))
    .query(async ({ input }) => {
      try {
        const result = await CryptoExpenseQueries.getLiquidityPoolActions(
          input.companyWallets,
          input.liquidityPoolTokenMints,
          input.fromDate,
          input.toDate
        );

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: `Failed to fetch liquidity pool action preview: ${result.error.message}`,
            cause: result.error,
          });
        }

        const records = result.value.slice(0, input.limit);
        const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);

        return {
          records,
          summary: {
            totalRecords: result.value.length,
            previewRecords: records.length,
            totalUsd,
            dateRange: {
              from: records.length > 0 ? new Date(Math.min(...records.map(r => new Date(r.block_timestamp).getTime()))) : null,
              to: records.length > 0 ? new Date(Math.max(...records.map(r => new Date(r.block_timestamp).getTime()))) : null,
            },
          },
        };
      } catch (error) {
        console.error("Failed to preview liquidity pool actions:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to preview liquidity pool actions",
          cause: error,
        });
      }
    }),

  /**
   * Get crypto expenses with filtering and pagination
   */
  getCryptoExpenses: publicProcedure
    .input(getCryptoExpensesSchema)
    .query(async ({ input, ctx }) => {
      try {
        const service = new CryptoExpenseService(ctx.prisma);
        return await service.getCryptoExpenses(input);
      } catch (error) {
        console.error("Failed to get crypto expenses:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get crypto expenses",
          cause: error,
        });
      }
    }),

  /**
   * Get crypto expense statistics
   */
  getCryptoExpenseStats: publicProcedure
    .input(getCryptoExpenseStatsSchema)
    .query(async ({ input, ctx }) => {
      try {
        const service = new CryptoExpenseService(ctx.prisma);
        return await service.getCryptoExpenseStats(input);
      } catch (error) {
        console.error("Failed to get crypto expense stats:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get crypto expense stats",
          cause: error,
        });
      }
    }),

  /**
   * Delete crypto expenses (for cleanup/re-import)
   */
  deleteCryptoExpenses: publicProcedure
    .input(deleteCryptoExpensesSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const service = new CryptoExpenseService(ctx.prisma);
        const deletedCount = await service.deleteCryptoExpenses(input);
        return { deletedCount };
      } catch (error) {
        console.error("Failed to delete crypto expenses:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete crypto expenses",
          cause: error,
        });
      }
    }),

  /**
   * Get buyback summary from Flipside (without importing)
   */
  getBuybackSummary: publicProcedure
    .input(z.object({
      companyWallets: z.array(z.string()).optional(),
      targetMints: z.array(z.string()).optional(),
      fromDate: z.date().optional(),
      toDate: z.date().optional(),
    }))
    .query(async ({ input }) => {
      try {
        const result = await CryptoExpenseQueries.getBuybackSummary(
          input.companyWallets,
          input.targetMints,
          input.fromDate,
          input.toDate
        );

        if (result.isErr()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: `Failed to get buyback summary: ${result.error.message}`,
            cause: result.error,
          });
        }

        return result.value;
      } catch (error) {
        console.error("Failed to get buyback summary:", error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get buyback summary",
          cause: error,
        });
      }
    }),
});
