/**
 * Configuration for crypto expenses
 * 
 * This file contains the configuration for company wallets and token mints
 * used in crypto expense tracking.
 */

/**
 * Company wallet addresses
 * Add your company's wallet addresses here
 */
export const COMPANY_WALLETS = [
  // Example: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj"
  // Add your actual company wallet addresses here
];

/**
 * Company wallet mapping for display names
 * Maps wallet addresses to human-readable names
 */
export const COMPANY_WALLET_MAPPING: Record<string, string> = {
  // Example: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj": "Main Treasury"
  // Add your wallet mappings here
};

/**
 * Token mint addresses for buybacks
 */
export const BUYBACK_TOKEN_MINTS = {
  ALL_MINT: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
  PUFF_MINT: "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB",
};

/**
 * NFT collection IDs for floor sweeps
 * Add your NFT collection IDs here
 */
export const FLOOR_SWEEP_COLLECTIONS = [
  // Example: "collection-id-1",
  // Example: "collection-id-2",
  // Add your actual NFT collection IDs here
];

/**
 * Token mint addresses for liquidity pool actions
 * Add your token mint addresses to track liquidity pool activities
 */
export const LIQUIDITY_POOL_TOKEN_MINTS = [
  // Example: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj", // ALL token
  // Example: "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB", // PUFF token
  // Add your actual token mint addresses here
];

/**
 * Default configuration for imports
 */
export const IMPORT_CONFIG = {
  maxAgeMinutes: 30,
  timeoutMinutes: 10,
  defaultPageSize: 100000,
  batchSize: 100, // Number of records to process in each batch
};

/**
 * Validation rules
 */
export const VALIDATION = {
  maxDateRange: 365, // Maximum days in a single query
  minWalletAddressLength: 32,
  maxWalletAddressLength: 44,
};

/**
 * Get configured company wallets
 * Throws an error if no wallets are configured
 */
export function getCompanyWallets(): string[] {
  if (COMPANY_WALLETS.length === 0) {
    throw new Error(
      "No company wallets configured. Please add wallet addresses to COMPANY_WALLETS in src/modules/crypto-expenses/config.ts"
    );
  }
  return COMPANY_WALLETS;
}

/**
 * Get configured buyback token mints
 */
export function getBuybackTokenMints(): string[] {
  return Object.values(BUYBACK_TOKEN_MINTS);
}

/**
 * Get configured floor sweep collections
 */
export function getFloorSweepCollections(): string[] {
  return FLOOR_SWEEP_COLLECTIONS;
}

/**
 * Get configured liquidity pool token mints
 */
export function getLiquidityPoolTokenMints(): string[] {
  return LIQUIDITY_POOL_TOKEN_MINTS;
}

/**
 * Get wallet display name
 */
export function getWalletDisplayName(address: string): string {
  return COMPANY_WALLET_MAPPING[address] || address;
}

/**
 * Validate date range
 */
export function validateDateRange(fromDate?: Date, toDate?: Date): void {
  if (fromDate && toDate) {
    const diffDays = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    if (diffDays > VALIDATION.maxDateRange) {
      throw new Error(`Date range too large. Maximum ${VALIDATION.maxDateRange} days allowed.`);
    }
    if (fromDate > toDate) {
      throw new Error("From date must be before to date.");
    }
  }
}

/**
 * Validate wallet address format
 */
export function validateWalletAddress(address: string): boolean {
  return (
    address.length >= VALIDATION.minWalletAddressLength &&
    address.length <= VALIDATION.maxWalletAddressLength &&
    /^[1-9A-HJ-NP-Za-km-z]+$/.test(address)
  );
}
