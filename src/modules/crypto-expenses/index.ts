/**
 * Crypto Expenses Module
 * 
 * This module provides functionality for importing and managing crypto expenses
 * from blockchain data via the Flipside API.
 * 
 * Features:
 * - Import buyback transactions from Solana DEX swaps
 * - Store crypto expense data with detailed transaction information
 * - Link crypto expenses to accounting transactions
 * - Query and filter crypto expenses
 * - Generate statistics and summaries
 * 
 * Usage:
 * ```typescript
 * import { CryptoExpenseService } from '@/modules/crypto-expenses';
 * 
 * const service = new CryptoExpenseService(prisma);
 * const result = await service.importBuybacks({
 *   companyWallets: ['wallet1', 'wallet2'],
 *   fromDate: new Date('2024-01-01'),
 *   createTransactions: true,
 *   accountId: 'account-id'
 * });
 * ```
 */

export { CryptoExpenseService } from './cryptoExpenseService';
export { cryptoExpenseRouter } from './cryptoExpenseRouter';
export type { 
  CryptoExpenseImportResult, 
  CryptoExpenseImportOptions 
} from './cryptoExpenseService';

/**
 * Configuration and setup information
 */
export namespace CryptoExpenseConfig {
  export const SUPPORTED_TYPES = ['BUYBACK', 'OTHER'] as const;
  
  export const DEFAULT_COMPANY_WALLETS = [
    // Add your default company wallet addresses here
  ];
  
  export const DEFAULT_BUYBACK_MINTS = {
    ALL_MINT: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
    PUFF_MINT: "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB",
  };
  
  export const IMPORT_DEFAULTS = {
    maxAgeMinutes: 30,
    timeoutMinutes: 10,
    createTransactions: false,
  };
}

/**
 * Quick start examples and common patterns
 */
export namespace CryptoExpenseExamples {
  
  /**
   * Example: Basic buyback import
   */
  export const basicImport = `
    import { CryptoExpenseService } from '@/modules/crypto-expenses';
    import { prisma } from '@/prisma/prisma';
    
    const service = new CryptoExpenseService(prisma);
    
    const result = await service.importBuybacks({
      companyWallets: ['your-wallet-address'],
      fromDate: new Date('2024-01-01'),
      toDate: new Date('2024-12-31'),
    });
    
    if (result.isErr()) {
      console.error('Import failed:', result.error.message);
      return;
    }
    
    console.log('Imported:', result.value.imported, 'records');
  `;

  /**
   * Example: Import with transaction creation
   */
  export const importWithTransactions = `
    import { CryptoExpenseService } from '@/modules/crypto-expenses';
    import { prisma } from '@/prisma/prisma';
    
    const service = new CryptoExpenseService(prisma);
    
    const result = await service.importBuybacks({
      companyWallets: ['your-wallet-address'],
      createTransactions: true,
      accountId: 'your-account-id', // Required when createTransactions is true
      fromDate: new Date('2024-01-01'),
    });
  `;

  /**
   * Example: Using tRPC endpoints
   */
  export const trpcUsage = `
    import { trpc } from '@/utils/trpc';
    
    // Preview buybacks before importing
    const preview = await trpc.cryptoExpenses.previewBuybacks.query({
      companyWallets: ['wallet1', 'wallet2'],
      fromDate: new Date('2024-01-01'),
      limit: 10
    });
    
    // Import buybacks
    const importResult = await trpc.cryptoExpenses.importBuybacks.mutate({
      companyWallets: ['wallet1', 'wallet2'],
      fromDate: new Date('2024-01-01'),
      createTransactions: true,
      accountId: 'account-id'
    });
    
    // Get crypto expenses
    const expenses = await trpc.cryptoExpenses.getCryptoExpenses.query({
      type: 'BUYBACK',
      limit: 50
    });
    
    // Get statistics
    const stats = await trpc.cryptoExpenses.getCryptoExpenseStats.query({
      type: 'BUYBACK',
      fromDate: new Date('2024-01-01')
    });
  `;

  /**
   * Example: Using the CLI script
   */
  export const cliUsage = `
    # Preview buybacks without importing
    pnpm tsx src/scripts/importCryptoExpenses.ts --preview --from-date 2024-01-01
    
    # Import buybacks for specific date range
    pnpm tsx src/scripts/importCryptoExpenses.ts --from-date 2024-01-01 --to-date 2024-12-31
    
    # Import with custom wallets and create transactions
    pnpm tsx src/scripts/importCryptoExpenses.ts \\
      --company-wallets "wallet1,wallet2,wallet3" \\
      --create-transactions \\
      --account-id "account-id"
    
    # Delete existing and re-import
    pnpm tsx src/scripts/importCryptoExpenses.ts --delete-existing --from-date 2024-01-01
  `;
}

/**
 * Common utilities and helpers
 */
export namespace CryptoExpenseUtils {
  
  /**
   * Format USD amount for display
   */
  export function formatUsd(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }
  
  /**
   * Format wallet address for display (truncated)
   */
  export function formatWalletAddress(address: string, length: number = 8): string {
    if (address.length <= length * 2) return address;
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  }
  
  /**
   * Calculate accounting period from date
   */
  export function getAccountingPeriod(date: Date): string {
    return date.toISOString().slice(0, 7); // YYYY-MM format
  }
  
  /**
   * Validate wallet address format (basic Solana address validation)
   */
  export function isValidSolanaAddress(address: string): boolean {
    return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
  }
}
