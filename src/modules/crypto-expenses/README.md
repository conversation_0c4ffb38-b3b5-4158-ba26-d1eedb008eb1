# Crypto Expenses Module

This module provides functionality for importing and managing crypto expenses from blockchain data via the Flipside API.

## Features

- 🔗 **Flipside Integration**: Fetch crypto expense data from Solana DEX swaps
- 💰 **Buyback Tracking**: Track token buyback transactions with detailed metadata
- 📊 **Database Storage**: Store crypto expenses with links to accounting transactions
- 🔍 **Query & Filter**: Advanced filtering and pagination for crypto expenses
- 📈 **Statistics**: Generate summaries and statistics for crypto expenses
- 🛠️ **CLI Tools**: Command-line script for importing data
- 🌐 **tRPC API**: Type-safe API endpoints for frontend integration

## Quick Start

### 1. Configuration

First, configure your company wallets in `src/modules/crypto-expenses/config.ts`:

```typescript
export const COMPANY_WALLETS = [
  "your-wallet-address-1",
  "your-wallet-address-2",
  // Add more wallet addresses
];

export const COMPANY_WALLET_MAPPING: Record<string, string> = {
  "your-wallet-address-1": "Main Treasury",
  "your-wallet-address-2": "Trading Wallet",
};
```

### 2. Environment Setup

Make sure your `.env` file contains the Flipside API key:

```bash
FLIPSIDE_API_KEY=your_flipside_api_key_here
```

### 3. Database Migration

The crypto expense tables are automatically created when you run:

```bash
pnpm prisma db push
```

## Usage

### CLI Script

The easiest way to import crypto expenses is using the CLI script:

```bash
# Preview buybacks without importing
pnpm tsx src/scripts/importCryptoExpenses.ts --preview --from-date 2024-01-01

# Import buybacks for specific date range
pnpm tsx src/scripts/importCryptoExpenses.ts --from-date 2024-01-01 --to-date 2024-12-31

# Import with custom wallets
pnpm tsx src/scripts/importCryptoExpenses.ts \
  --company-wallets "wallet1,wallet2,wallet3" \
  --from-date 2024-01-01

# Import and create transaction records
pnpm tsx src/scripts/importCryptoExpenses.ts \
  --create-transactions \
  --account-id "your-account-id" \
  --from-date 2024-01-01

# Delete existing and re-import
pnpm tsx src/scripts/importCryptoExpenses.ts --delete-existing --from-date 2024-01-01
```

### Programmatic Usage

```typescript
import { CryptoExpenseService } from '@/modules/crypto-expenses';
import { prisma } from '@/prisma/prisma';

const service = new CryptoExpenseService(prisma);

// Import buybacks
const result = await service.importBuybacks({
  companyWallets: ['your-wallet-address'],
  fromDate: new Date('2024-01-01'),
  toDate: new Date('2024-12-31'),
  createTransactions: true,
  accountId: 'your-account-id',
});

if (result.isErr()) {
  console.error('Import failed:', result.error.message);
} else {
  console.log('Imported:', result.value.imported, 'records');
}
```

### tRPC API

```typescript
import { trpc } from '@/utils/trpc';

// Preview buybacks
const preview = await trpc.cryptoExpenses.previewBuybacks.query({
  companyWallets: ['wallet1', 'wallet2'],
  fromDate: new Date('2024-01-01'),
  limit: 10
});

// Import buybacks
const importResult = await trpc.cryptoExpenses.importBuybacks.mutate({
  companyWallets: ['wallet1', 'wallet2'],
  fromDate: new Date('2024-01-01'),
  createTransactions: true,
  accountId: 'account-id'
});

// Get crypto expenses
const expenses = await trpc.cryptoExpenses.getCryptoExpenses.query({
  type: 'BUYBACK',
  limit: 50
});

// Get statistics
const stats = await trpc.cryptoExpenses.getCryptoExpenseStats.query({
  type: 'BUYBACK',
  fromDate: new Date('2024-01-01')
});
```

## Data Structure

### CryptoExpense Model

```typescript
{
  id: string;
  type: 'BUYBACK' | 'OTHER';
  txId: string; // Solana transaction ID
  blockTimestamp: Date;
  swapper: string; // Wallet address
  swapFromSymbol: string;
  swapToSymbol: string;
  swapFromAmount: Decimal;
  swapToAmount: Decimal;
  swapFromAmountUsd: Decimal;
  swapToAmountUsd: Decimal;
  swapFromMint?: string;
  swapToMint?: string;
  companyWalletName?: string;
  accountingPeriod?: string;
  rawFlipsideData?: Json;
  transactionId?: string; // Link to Transaction model
}
```

### Flipside Query

The module uses this Solana DEX swap query:

```sql
SELECT
  swap_from_amount_usd as amount_usd,
  block_timestamp,
  tx_id,
  swapper,
  swap_from_symbol,
  swap_to_symbol,
  swap_from_amount,
  swap_to_amount,
  swap_from_amount_usd,
  swap_to_amount_usd,
  swap_from_mint,
  swap_to_mint
FROM
  solana.defi.ez_dex_swaps
WHERE
  swap_to_mint in ('target_mint_1', 'target_mint_2')
  and swapper in ('company_wallet_1', 'company_wallet_2')
ORDER BY
  block_timestamp DESC;
```

## Configuration Options

### Company Wallets

Configure your company's wallet addresses in `config.ts`:

```typescript
export const COMPANY_WALLETS = [
  "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
  // Add your actual wallet addresses
];
```

### Target Token Mints

Configure the token mints you want to track for buybacks:

```typescript
export const BUYBACK_TOKEN_MINTS = {
  ALL_MINT: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
  PUFF_MINT: "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB",
};
```

### Import Settings

```typescript
export const IMPORT_CONFIG = {
  maxAgeMinutes: 30,     // Cache duration
  timeoutMinutes: 10,    // Query timeout
  defaultPageSize: 100000,
  batchSize: 100,        // Records per batch
};
```

## Error Handling

The module uses neverthrow for type-safe error handling:

```typescript
const result = await service.importBuybacks(options);

if (result.isErr()) {
  switch (result.error.type) {
    case "FLIPSIDE_ERROR":
      console.error("Flipside API error:", result.error.message);
      break;
    case "IMPORT_ERROR":
      console.error("Database import error:", result.error.message);
      break;
    default:
      console.error("Unknown error:", result.error.message);
  }
} else {
  console.log("Success:", result.value);
}
```

## Troubleshooting

### No Records Found

If the script returns 0 records:

1. **Check wallet addresses**: Ensure your company wallet addresses are correct
2. **Check date range**: The wallets might not have transactions in the specified date range
3. **Check token mints**: Ensure the target token mints are correct
4. **Check Flipside API key**: Ensure your API key is valid and has sufficient credits

### Import Errors

1. **Database connection**: Ensure your database is running and accessible
2. **Prisma schema**: Run `pnpm prisma db push` to ensure the schema is up to date
3. **Duplicate transactions**: The script automatically skips duplicate transactions based on `txId`

### Performance

For large date ranges:

1. **Use smaller date ranges**: Import data in monthly chunks
2. **Use pagination**: The Flipside client automatically handles pagination
3. **Monitor API limits**: Flipside has rate limits and credit usage

## Next Steps

1. **Configure your wallets**: Add your actual company wallet addresses to `config.ts`
2. **Test the integration**: Run the preview command with your wallets
3. **Set up regular imports**: Create a cron job or scheduled task to import data regularly
4. **Build UI components**: Create frontend components to view and manage crypto expenses
5. **Add more expense types**: Extend the module to support other types of crypto expenses
