import { Result, ok, err } from "neverthrow";
import { PrismaClient } from "@/prisma/generated";
import { CryptoExpenseQueries } from "@/modules/flipside/cryptoExpenseQueries";
import { Decimal } from "@/prisma/generated/runtime/library";

export interface CryptoExpenseImportResult {
  imported: number;
  skipped: number;
  errors: number;
  summary: {
    totalUsd: number;
    dateRange: {
      from: Date | null;
      to: Date | null;
    };
  };
}

export interface CryptoExpenseImportOptions {
  companyWallets?: string[];
  targetMints?: string[];
  collectionIds?: string[];
  liquidityPoolTokenMints?: string[];
  fromDate?: Date;
  toDate?: Date;
  createTransactions?: boolean;
  accountId?: string; // Required if createTransactions is true
}

/**
 * Service for importing and managing crypto expenses
 */
export class CryptoExpenseService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Import buyback expenses from Flipside API
   */
  async importBuybacks(options: CryptoExpenseImportOptions = {}): Promise<Result<CryptoExpenseImportResult, { type: string; message: string; error?: any }>> {
    try {
      // Fetch data from Flipside
      const flipsideResult = await CryptoExpenseQueries.getBuybacksWithAccountingPeriod(
        options.companyWallets,
        options.targetMints,
        options.fromDate,
        options.toDate
      );

      if (flipsideResult.isErr()) {
        return err({
          type: "FLIPSIDE_ERROR",
          message: `Failed to fetch buyback data: ${flipsideResult.error.message}`,
          error: flipsideResult.error,
        });
      }

      const records = flipsideResult.value;
      let imported = 0;
      let skipped = 0;
      let errors = 0;

      const allAccounts = await this.prisma.account.findMany({
        where: {
          publicKey: {
            in: options.companyWallets || [],
          },
        },
      });

      // Process records in transaction
      await this.prisma.$transaction(
        async (prisma) => {
          for (const record of records) {
            try {
              // Check if this transaction already exists
              const existing = await prisma.cryptoExpense.findUnique({
                where: { txId: record.tx_id },
              });

              const account = allAccounts.find((a) => a.publicKey === record.swapper);

              if (!account) {
                console.error(`Account for ${record.swapper} does not exist in the database`);
                errors++;
                continue;
              }

              if (existing) {
                skipped++;
                continue;
              }

              // Create crypto expense record with buyback details
              const cryptoExpense = await prisma.cryptoExpense.create({
                data: {
                  type: "BUYBACK",
                  txId: record.tx_id,
                  blockTimestamp: new Date(record.block_timestamp),
                  swapper: record.swapper,
                  amountUsd: new Decimal(record.amount_usd), // Main amount field
                  accountingPeriod: record.accounting_period,
                  rawFlipsideData: record,
                  buyback: {
                    create: {
                      swapFromSymbol: record.swap_from_symbol,
                      swapToSymbol: record.swap_to_symbol,
                      swapFromAmount: new Decimal(record.swap_from_amount),
                      swapToAmount: new Decimal(record.swap_to_amount),
                      swapFromAmountUsd: new Decimal(record.swap_from_amount_usd),
                      swapToAmountUsd: new Decimal(record.swap_to_amount_usd),
                      swapFromMint: record.swap_from_mint,
                      swapToMint: record.swap_to_mint,
                    },
                  },
                },
              });

              // Optionally create a transaction record
              if (options.createTransactions) {
                const transaction = await prisma.transaction.create({
                  data: {
                    executedAt: new Date(record.block_timestamp),
                    accountId: account.id,
                    metadata: {
                      source: "flipside-crypto-expense",
                      type: "buyback",
                      txId: record.tx_id,
                      swapper: record.swapper,
                    },
                  },
                });

                // Link the crypto expense to the transaction
                await prisma.cryptoExpense.update({
                  where: { id: cryptoExpense.id },
                  data: { transactionId: transaction.id },
                });
              }

              imported++;
            } catch (error) {
              console.error(`Failed to import record ${record.tx_id}:`, error);
              errors++;
            }
          }
        },
        { timeout: 300000 }
      );

      // Calculate summary
      const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);
      const dateRange = {
        from: records.length > 0 ? new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
        to: records.length > 0 ? new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
      };

      return ok({
        imported,
        skipped,
        errors,
        summary: {
          totalUsd,
          dateRange,
        },
      });
    } catch (error) {
      return err({
        type: "IMPORT_ERROR",
        message: `Failed to import buybacks: ${error instanceof Error ? error.message : String(error)}`,
        error,
      });
    }
  }

  /**
   * Import floor sweep expenses from Flipside API
   */
  async importFloorSweeps(
    options: CryptoExpenseImportOptions = {}
  ): Promise<Result<CryptoExpenseImportResult, { type: string; message: string; error?: any }>> {
    try {
      // Validate collection IDs
      if (!options.collectionIds || options.collectionIds.length === 0) {
        return err({
          type: "VALIDATION_ERROR",
          message: "No collection IDs provided. Please specify NFT collection IDs to track.",
        });
      }

      // Fetch data from Flipside
      const flipsideResult = await CryptoExpenseQueries.getFloorSweepsWithAccountingPeriod(
        options.companyWallets,
        options.collectionIds,
        options.fromDate,
        options.toDate
      );

      if (flipsideResult.isErr()) {
        return err({
          type: "FLIPSIDE_ERROR",
          message: `Failed to fetch floor sweep data: ${flipsideResult.error.message}`,
          error: flipsideResult.error,
        });
      }

      const records = flipsideResult.value;
      let imported = 0;
      let skipped = 0;
      let errors = 0;

      // Get all accounts for wallet lookup
      const allAccounts = await this.prisma.account.findMany({
        where: { type: "WALLET" },
        select: { id: true, publicKey: true },
      });

      // Process records in transaction
      await this.prisma.$transaction(
        async (prisma) => {
          for (const record of records) {
            try {
              // Check if this transaction already exists
              const existing = await prisma.cryptoExpense.findUnique({
                where: { txId: record.tx_id },
              });

              const account = allAccounts.find((a) => a.publicKey === record.buyer_address);

              if (!account) {
                console.error(`Account for ${record.buyer_address} does not exist in the database`);
                errors++;
                continue;
              }

              if (existing) {
                skipped++;
                continue;
              }

              // Create crypto expense record with floor sweep details
              const cryptoExpense = await prisma.cryptoExpense.create({
                data: {
                  type: "FLOOR_SWEEP",
                  txId: record.tx_id,
                  blockTimestamp: new Date(record.block_timestamp),
                  swapper: record.buyer_address,
                  amountUsd: new Decimal(record.amount_usd), // Main amount field
                  accountingPeriod: record.accounting_period,
                  rawFlipsideData: record,
                  floorSweep: {
                    create: {
                      collectionId: record.collection_id,
                      collectionName: record.collection_name,
                      tokenId: record.token_id,
                      mintAddress: record.mint_address,
                      priceUsd: new Decimal(record.price_usd),
                    },
                  },
                },
              });

              // Optionally create a transaction record
              if (options.createTransactions && options.accountId) {
                const transaction = await prisma.transaction.create({
                  data: {
                    executedAt: new Date(record.block_timestamp),
                    accountId: options.accountId,
                    metadata: {
                      source: "flipside-crypto-expense",
                      type: "floor-sweep",
                      txId: record.tx_id,
                      buyer: record.buyer_address,
                      collection: record.collection_id,
                    },
                  },
                });

                // Link the crypto expense to the transaction
                await prisma.cryptoExpense.update({
                  where: { id: cryptoExpense.id },
                  data: { transactionId: transaction.id },
                });
              }

              imported++;
            } catch (error) {
              console.error(`Failed to import record ${record.tx_id}:`, error);
              errors++;
            }
          }
        },
        { timeout: 300000 } // 5 minute timeout
      );

      // Calculate summary
      const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);
      const dateRange = {
        from: records.length > 0 ? new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
        to: records.length > 0 ? new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
      };

      return ok({
        imported,
        skipped,
        errors,
        summary: {
          totalUsd,
          dateRange,
        },
      });
    } catch (error) {
      return err({
        type: "IMPORT_ERROR",
        message: `Failed to import floor sweeps: ${error instanceof Error ? error.message : String(error)}`,
        error,
      });
    }
  }

  /**
   * Import liquidity pool action expenses from Flipside API
   */
  async importLiquidityPoolActions(
    options: CryptoExpenseImportOptions = {}
  ): Promise<Result<CryptoExpenseImportResult, { type: string; message: string; error?: any }>> {
    try {
      // Validate token mints
      if (!options.liquidityPoolTokenMints || options.liquidityPoolTokenMints.length === 0) {
        return err({
          type: "VALIDATION_ERROR",
          message: "No token mints provided. Please specify token mint addresses to track.",
        });
      }

      // Fetch data from Flipside
      const flipsideResult = await CryptoExpenseQueries.getLiquidityPoolActionsWithAccountingPeriod(
        options.companyWallets,
        options.liquidityPoolTokenMints,
        options.fromDate,
        options.toDate
      );

      if (flipsideResult.isErr()) {
        return err({
          type: "FLIPSIDE_ERROR",
          message: `Failed to fetch liquidity pool action data: ${flipsideResult.error.message}`,
          error: flipsideResult.error,
        });
      }

      const records = flipsideResult.value;
      let imported = 0;
      let skipped = 0;
      let errors = 0;

      // Get all accounts for wallet lookup
      const allAccounts = await this.prisma.account.findMany({
        where: { type: "WALLET" },
        select: { id: true, publicKey: true },
      });

      // Process records in transaction
      await this.prisma.$transaction(
        async (prisma) => {
          for (const record of records) {
            try {
              // Check if this transaction already exists
              const existing = await prisma.cryptoExpense.findUnique({
                where: { txId: record.tx_id },
              });

              const account = allAccounts.find((a) => a.publicKey === record.signer);

              if (!account) {
                console.error(`Account for ${record.signer} does not exist in the database`);
                errors++;
                continue;
              }

              if (existing) {
                skipped++;
                continue;
              }

              // Create crypto expense record with liquidity pool details
              const cryptoExpense = await prisma.cryptoExpense.create({
                data: {
                  type: "LIQUIDITY_POOL",
                  txId: record.tx_id,
                  blockTimestamp: new Date(record.block_timestamp),
                  swapper: record.signer,
                  amountUsd: new Decimal(record.amount_usd), // Main amount field (token_b_amount_usd)
                  accountingPeriod: record.accounting_period,
                  rawFlipsideData: record,
                  liquidityPool: {
                    create: {
                      action: record.action,
                      poolAddress: record.pool_address,
                      tokenAMint: record.token_a_mint,
                      tokenBMint: record.token_b_mint,
                      tokenASymbol: record.token_a_symbol || "",
                      tokenBSymbol: record.token_b_symbol || "",
                      tokenAAmount: new Decimal(record.token_a_amount),
                      tokenBAmount: new Decimal(record.token_b_amount),
                      tokenAAmountUsd: new Decimal(record.token_a_amount_usd),
                      tokenBAmountUsd: new Decimal(record.token_b_amount_usd),
                    },
                  },
                },
              });

              // Optionally create a transaction record
              if (options.createTransactions && options.accountId) {
                const transaction = await prisma.transaction.create({
                  data: {
                    executedAt: new Date(record.block_timestamp),
                    accountId: options.accountId,
                    metadata: {
                      source: "flipside-crypto-expense",
                      type: "liquidity-pool",
                      txId: record.tx_id,
                      signer: record.signer,
                      action: record.action,
                      pool: record.pool_address,
                    },
                  },
                });

                // Link the crypto expense to the transaction
                await prisma.cryptoExpense.update({
                  where: { id: cryptoExpense.id },
                  data: { transactionId: transaction.id },
                });
              }

              imported++;
            } catch (error) {
              console.error(`Failed to import record ${record.tx_id}:`, error);
              errors++;
            }
          }
        },
        { timeout: 300000 } // 5 minute timeout
      );

      // Calculate summary
      const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);
      const dateRange = {
        from: records.length > 0 ? new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
        to: records.length > 0 ? new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))) : null,
      };

      return ok({
        imported,
        skipped,
        errors,
        summary: {
          totalUsd,
          dateRange,
        },
      });
    } catch (error) {
      return err({
        type: "IMPORT_ERROR",
        message: `Failed to import liquidity pool actions: ${error instanceof Error ? error.message : String(error)}`,
        error,
      });
    }
  }

  /**
   * Get crypto expenses with optional filtering
   */
  async getCryptoExpenses(
    options: {
      type?: "BUYBACK" | "FLOOR_SWEEP" | "LIQUIDITY_POOL" | "OTHER";
      fromDate?: Date;
      toDate?: Date;
      limit?: number;
      offset?: number;
    } = {}
  ) {
    const where: any = {};

    if (options.type) {
      where.type = options.type;
    }

    if (options.fromDate || options.toDate) {
      where.blockTimestamp = {};
      if (options.fromDate) {
        where.blockTimestamp.gte = options.fromDate;
      }
      if (options.toDate) {
        where.blockTimestamp.lte = options.toDate;
      }
    }

    const [expenses, total] = await Promise.all([
      this.prisma.cryptoExpense.findMany({
        where,
        include: {
          transaction: {
            include: {
              account: true,
            },
          },
        },
        orderBy: { blockTimestamp: "desc" },
        take: options.limit || 50,
        skip: options.offset || 0,
      }),
      this.prisma.cryptoExpense.count({ where }),
    ]);

    return { expenses, total };
  }

  /**
   * Get crypto expense statistics
   */
  async getCryptoExpenseStats(
    options: {
      type?: "BUYBACK" | "FLOOR_SWEEP" | "LIQUIDITY_POOL" | "OTHER";
      fromDate?: Date;
      toDate?: Date;
    } = {}
  ) {
    const where: any = {};

    if (options.type) {
      where.type = options.type;
    }

    if (options.fromDate || options.toDate) {
      where.blockTimestamp = {};
      if (options.fromDate) {
        where.blockTimestamp.gte = options.fromDate;
      }
      if (options.toDate) {
        where.blockTimestamp.lte = options.toDate;
      }
    }

    const stats = await this.prisma.cryptoExpense.aggregate({
      where,
      _sum: {
        amountUsd: true,
      },
      _count: {
        id: true,
      },
    });

    const uniqueWallets = await this.prisma.cryptoExpense.findMany({
      where,
      select: { swapper: true },
      distinct: ["swapper"],
    });

    return {
      totalTransactions: stats._count.id || 0,
      totalAmountUsd: stats._sum.amountUsd || 0,
      uniqueWallets: uniqueWallets.length,
    };
  }

  /**
   * Delete crypto expenses (for cleanup/re-import)
   */
  async deleteCryptoExpenses(
    options: {
      type?: "BUYBACK" | "FLOOR_SWEEP" | "LIQUIDITY_POOL" | "OTHER";
      fromDate?: Date;
      toDate?: Date;
      txIds?: string[];
    } = {}
  ) {
    const where: any = {};

    if (options.type) {
      where.type = options.type;
    }

    if (options.fromDate || options.toDate) {
      where.blockTimestamp = {};
      if (options.fromDate) {
        where.blockTimestamp.gte = options.fromDate;
      }
      if (options.toDate) {
        where.blockTimestamp.lte = options.toDate;
      }
    }

    if (options.txIds) {
      where.txId = { in: options.txIds };
    }

    const result = await this.prisma.cryptoExpense.deleteMany({ where });
    return result.count;
  }
}
