import { envVars } from "@/envVars";
import { PrismaClient } from "@/prisma/generated";

// const adapter = new PrismaPg({ connectionString: envVars.DATABASE_URL });

export const prisma = new PrismaClient({
  // adapter,
  datasourceUrl: envVars.DATABASE_URL,

  // errorFormat: process.env.NODE_ENV === "development" ? "minimal" : "minimal",
});
// .$extends({
//   result: {
//     invoice: {
//       // amu: {
//       //   compute: (invoice) => {
//       //     console.log("invoice.conversionRateToBaseCurrency", invoice.conversionRateToBaseCurrency);
//       //     if (!invoice.conversionRateToBaseCurrency)
//       //       return {
//       //         gross: new Decimal(invoice.totalAmountGross),
//       //         ...(invoice.totalAmountNet && invoice.totalAmountNet && { net: new Decimal(invoice.totalAmountNet) }),
//       //         ...(invoice.totalAmountVat && invoice.totalAmountVat && { vat: new Decimal(invoice.totalAmountVat) }),
//       //       };
//       //     return {
//       //       gross: new Decimal(invoice.totalAmountGross).mul(invoice.conversionRateToBaseCurrency),
//       //       ...(invoice.totalAmountNet && invoice.totalAmountNet && { net: new Decimal(invoice.totalAmountNet).mul(invoice.conversionRateToBaseCurrency) }),
//       //     };
//       //   },
//       // },
//     },
//   },
// });
