#!/usr/bin/env tsx

/**
 * Cleanup Script for Duplicate Transfer Records
 * 
 * This script identifies and removes Transfer records that duplicate 
 * CryptoExpense transactions (buybacks, floor sweeps, liquidity pool actions).
 * 
 * Usage:
 *   npm run script:cleanup-duplicate-transfers -- [options]
 */

import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { PrismaClient } from "@/prisma/generated";
import inquirer from "inquirer";

const prisma = new PrismaClient();

interface CleanupOptions {
  dryRun: boolean;
  interactive: boolean;
  batchSize: number;
  logFile?: string;
}

interface DuplicateTransfer {
  transferId: string;
  transactionId: string;
  txId: string;
  cryptoExpenseType: string;
  amount: number;
  token: string;
  counterparty: string;
  executedAt: Date;
}

async function findDuplicateTransfers(): Promise<DuplicateTransfer[]> {
  console.log("🔍 Scanning for duplicate Transfer records...");

  const allCryptoExpenses = await prisma.cryptoExpense.findMany({
    where: {
      type: {
        in: ['BUYBACK', 'FLOOR_SWEEP', 'LIQUIDITY_POOL']
      }
    },
    select: {
      txId: true,
      type: true
    }
  });

  const allCryptoExpenseTransactionIds = allCryptoExpenses.map(cr => cr.txId)

  const cryptoExpenseMap = new Map()
  allCryptoExpenses.forEach(cr => cryptoExpenseMap.set(cr.txId, cr.type))

  console.log(`📊 Found ${allCryptoExpenseTransactionIds.length} crypto expenses with txId`);

  // First, get all unique solanaTransactionIds from transfers
  const duplicateTransfers = await prisma.transfer.findMany({
    where: {
      transaction: {
        solanaTransactionId: { in: allCryptoExpenseTransactionIds },
      }
    },
    include: {
      transaction: true
    }
  });

  if (duplicateTransfers.length === 0) {
    console.log("ℹ️  No matching transfers with solanaTransactionId found");
    return [];
  }

  console.log(`🔍 Found ${duplicateTransfers.length} matching transactions for crypto expenses`);

  // Build the duplicate transfer records
  const duplicates: DuplicateTransfer[] = duplicateTransfers.map(transfer => ({
    transferId: transfer.id,
    transactionId: transfer.transaction.id,
    txId: transfer.transaction.solanaTransactionId!,
    cryptoExpenseType: cryptoExpenseMap.get(transfer.transaction.solanaTransactionId!)!,
    amount: Number(transfer.amount),
    token: transfer.currencyCode,
    counterparty: transfer.counterparty,
    executedAt: transfer.transaction.executedAt
  }));

  // Sort by execution date descending
  duplicates.sort((a, b) => b.executedAt.getTime() - a.executedAt.getTime());

  return duplicates;
}

async function previewDuplicates(duplicates: DuplicateTransfer[]) {
  console.log(`\n📊 Found ${duplicates.length} duplicate Transfer records:`);
  
  if (duplicates.length === 0) {
    console.log("✅ No duplicates found - database is clean!");
    return;
  }

  // Group by crypto expense type
  const byType = duplicates.reduce((acc, dup) => {
    if (!acc[dup.cryptoExpenseType]) {
      acc[dup.cryptoExpenseType] = [];
    }
    acc[dup.cryptoExpenseType].push(dup);
    return acc;
  }, {} as Record<string, DuplicateTransfer[]>);

  console.log("\n📋 Breakdown by type:");
  Object.entries(byType).forEach(([type, dups]) => {
    console.log(`   ${type}: ${dups.length} duplicates`);
  });

  console.log("\n🔍 Sample duplicates:");
  const sampleSize = Math.min(10, duplicates.length);
  for (let i = 0; i < sampleSize; i++) {
    const dup = duplicates[i];
    console.log(`   ${i + 1}. TX: ${dup.txId.slice(0, 12)}... (${dup.cryptoExpenseType})`);
    console.log(`      Amount: ${dup.amount} ${dup.token}`);
    console.log(`      To: ${dup.counterparty.slice(0, 12)}...`);
    console.log(`      Date: ${dup.executedAt.toISOString().split('T')[0]}`);
    console.log("");
  }

  if (duplicates.length > sampleSize) {
    console.log(`   ... and ${duplicates.length - sampleSize} more duplicates`);
  }
}

async function deleteDuplicates(
  duplicates: DuplicateTransfer[], 
  options: CleanupOptions
): Promise<number> {
  if (duplicates.length === 0) {
    return 0;
  }

  let deletedCount = 0;
  const batchSize = options.batchSize;
  
  console.log(`\n🗑️  Deleting ${duplicates.length} duplicate transfers in batches of ${batchSize}...`);

  for (let i = 0; i < duplicates.length; i += batchSize) {
    const batch = duplicates.slice(i, i + batchSize);
    const transferIds = batch.map(d => d.transferId);
    const transactionIds = batch.map(d => d.transactionId);

    try {
      // Delete transfers first (due to foreign key constraints)
      await prisma.transfer.deleteMany({
        where: {
          id: {
            in: transferIds
          }
        }
      });

      // Delete the associated transactions
      await prisma.transaction.deleteMany({
        where: {
          id: {
            in: transactionIds
          }
        }
      });

      deletedCount += batch.length;
      
      // Log the deletions
      if (options.logFile) {
        const logEntries = batch.map(d => 
          `${new Date().toISOString()},${d.transferId},${d.transactionId},${d.txId},${d.cryptoExpenseType},${d.amount},${d.token},${d.counterparty}`
        ).join('\n');
        
        const fs = await import('fs');
        fs.appendFileSync(options.logFile, logEntries + '\n');
      }

      console.log(`   Deleted batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(duplicates.length / batchSize)} (${deletedCount}/${duplicates.length})`);
      
    } catch (error) {
      console.error(`   Error deleting batch starting at index ${i}:`, error);
      throw error;
    }
  }

  return deletedCount;
}

async function confirmDeletion(duplicates: DuplicateTransfer[]): Promise<boolean> {
  const { confirmed } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirmed',
      message: `Are you sure you want to delete ${duplicates.length} duplicate Transfer records? This action cannot be undone.`,
      default: false
    }
  ]);

  return confirmed;
}

async function initializeLogFile(logFile: string) {
  const fs = await import('fs');
  const header = 'timestamp,transferId,transactionId,txId,cryptoExpenseType,amount,token,counterparty\n';
  fs.writeFileSync(logFile, header);
  console.log(`📝 Logging deletions to: ${logFile}`);
}

async function validateDuplicateDetection() {
  console.log("🔍 Validating duplicate detection logic...");

  // Get count of transfers with solanaTransactionId
  const transferCount = await prisma.transaction.count({
    where: {
      solanaTransactionId: { not: null },
      transfer: { isNot: null }
    }
  });

  // Get count of relevant CryptoExpenses
  const cryptoExpenseCount = await prisma.cryptoExpense.count({
    where: {
      type: { in: ['BUYBACK', 'FLOOR_SWEEP', 'LIQUIDITY_POOL'] }
    }
  });

  console.log(`📊 Database state:`);
  console.log(`   Transfers with solanaTransactionId: ${transferCount}`);
  console.log(`   Relevant CryptoExpenses: ${cryptoExpenseCount}`);

  if (transferCount > 0 && cryptoExpenseCount > 0) {
    // Test the matching logic with a small sample
    const sampleTransactionIds = await prisma.transaction.findMany({
      where: {
        solanaTransactionId: { not: null },
        transfer: { isNot: null }
      },
      select: { solanaTransactionId: true },
      take: 5
    });

    const sampleIds = sampleTransactionIds
      .map(t => t.solanaTransactionId)
      .filter((id): id is string => id !== null);

    if (sampleIds.length > 0) {
      const matchingCount = await prisma.cryptoExpense.count({
        where: {
          txId: { in: sampleIds },
          type: { in: ['BUYBACK', 'FLOOR_SWEEP', 'LIQUIDITY_POOL'] }
        }
      });

      console.log(`✅ Validation: ${matchingCount}/${sampleIds.length} sample transfers have matching CryptoExpenses`);
    }
  } else {
    console.log(`ℹ️  Validation: Insufficient data for testing (need both transfers and CryptoExpenses)`);
  }
}

async function runCleanup(options: CleanupOptions) {
  try {
    console.log("🧹 Starting duplicate transfer cleanup...");
    console.log(`Mode: ${options.dryRun ? 'DRY RUN' : 'LIVE DELETION'}`);

    if (options.logFile && !options.dryRun) {
      await initializeLogFile(options.logFile);
    }

    // Run validation in dry-run mode
    if (options.dryRun) {
      await validateDuplicateDetection();
    }

    const duplicates = await findDuplicateTransfers();
    await previewDuplicates(duplicates);

    if (duplicates.length === 0) {
      return;
    }

    if (options.dryRun) {
      console.log("\n✅ Dry run completed. Use --no-dry-run to perform actual deletion.");
      return;
    }

    if (options.interactive) {
      const confirmed = await confirmDeletion(duplicates);
      if (!confirmed) {
        console.log("❌ Cleanup cancelled by user.");
        return;
      }
    }

    const deletedCount = await deleteDuplicates(duplicates, options);
    
    console.log(`\n✅ Cleanup completed successfully!`);
    console.log(`   Deleted: ${deletedCount} duplicate transfers`);
    
    if (options.logFile) {
      console.log(`   Log file: ${options.logFile}`);
    }

  } catch (error) {
    console.error("❌ Cleanup failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  const argv = await yargs(hideBin(process.argv))
    .option("dry-run", {
      type: "boolean",
      default: true,
      description: "Preview what would be deleted without actually deleting",
    })
    .option("interactive", {
      type: "boolean", 
      default: true,
      description: "Ask for confirmation before deleting",
    })
    .option("batch-size", {
      type: "number",
      default: 50,
      description: "Number of records to delete in each batch",
    })
    .option("log-file", {
      type: "string",
      description: "File to log all deletions (CSV format)",
    })
    .help()
    .parse();

  const options: CleanupOptions = {
    dryRun: argv["dry-run"],
    interactive: argv.interactive,
    batchSize: argv["batch-size"],
    logFile: argv["log-file"]
  };

  try {
    await runCleanup(options);
  } catch (error) {
    console.error("❌ Script failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
}
