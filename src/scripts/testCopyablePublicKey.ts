#!/usr/bin/env tsx

/**
 * Test script to verify copyable public key functionality
 * This creates test accounts to verify the UI components work correctly
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function createTestAccounts() {
  console.log("🧪 Creating test accounts for copyable public key testing...\n");

  try {
    // Create a few test wallet accounts with different public keys
    const testAccounts = [
      {
        name: "Test Solana Wallet 1",
        type: "WALLET" as const,
        publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
      },
      {
        name: "Test Solana Wallet 2", 
        type: "WALLET" as const,
        publicKey: "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB",
      },
      {
        name: "Test Solana Wallet 3",
        type: "WALLET" as const,
        publicKey: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      },
      {
        name: "Test Bank Account",
        type: "BANK_ACCOUNT" as const,
        // No public key for bank account
      },
    ];

    const createdAccounts = [];

    for (const accountData of testAccounts) {
      try {
        const account = await prisma.account.create({
          data: accountData,
        });
        createdAccounts.push(account);
        console.log(`✅ Created ${account.type} account: ${account.name}`);
        if (account.publicKey) {
          console.log(`   Public Key: ${account.publicKey}`);
        }
      } catch (error) {
        if (error instanceof Error && error.message.includes("Unique constraint")) {
          console.log(`⚠️  Account with public key ${accountData.publicKey} already exists, skipping...`);
        } else {
          console.error(`❌ Failed to create account ${accountData.name}:`, error);
        }
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`   Created ${createdAccounts.length} new test accounts`);
    console.log(`   Wallet accounts: ${createdAccounts.filter(a => a.type === "WALLET").length}`);
    console.log(`   Non-wallet accounts: ${createdAccounts.filter(a => a.type !== "WALLET").length}`);

    console.log(`\n🎯 Test Instructions:`);
    console.log(`1. Open the accounts page in your browser`);
    console.log(`2. Look for the test accounts created above`);
    console.log(`3. Verify that wallet accounts show truncated public keys with copy buttons`);
    console.log(`4. Click the copy buttons to test clipboard functionality`);
    console.log(`5. Click on a wallet account to open details and test the full public key copy`);
    console.log(`6. Verify that non-wallet accounts show "—" in the public key column`);

    console.log(`\n🧹 Cleanup:`);
    console.log(`Run this script with --cleanup flag to remove test accounts`);

    return createdAccounts;

  } catch (error) {
    console.error("❌ Failed to create test accounts:", error);
    throw error;
  }
}

async function cleanupTestAccounts() {
  console.log("🧹 Cleaning up test accounts...\n");

  try {
    const testAccountNames = [
      "Test Solana Wallet 1",
      "Test Solana Wallet 2", 
      "Test Solana Wallet 3",
      "Test Bank Account",
    ];

    const result = await prisma.account.deleteMany({
      where: {
        name: {
          in: testAccountNames,
        },
      },
    });

    console.log(`✅ Cleaned up ${result.count} test accounts`);

  } catch (error) {
    console.error("❌ Failed to cleanup test accounts:", error);
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const isCleanup = args.includes("--cleanup");

  try {
    if (isCleanup) {
      await cleanupTestAccounts();
    } else {
      await createTestAccounts();
    }
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
