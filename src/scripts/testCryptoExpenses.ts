#!/usr/bin/env tsx

/**
 * Test script for crypto expenses functionality
 * 
 * This script tests the crypto expenses module without requiring real data.
 */

import { PrismaClient } from "@/prisma/generated";
import { CryptoExpenseService } from "@/modules/crypto-expenses/cryptoExpenseService";
import { CryptoExpenseQueries } from "@/modules/flipside/cryptoExpenseQueries";

const prisma = new PrismaClient();

async function testFlipsideConnection() {
  console.log("🔗 Testing Flipside connection...");
  
  try {
    // Test with a simple query that should return no results but validate the connection
    const result = await CryptoExpenseQueries.getBuybacks(
      ["********************************"], // Invalid wallet address
      ["********************************"], // Invalid mint address
      new Date("2024-01-01"),
      new Date("2024-01-02")
    );

    if (result.isErr()) {
      console.log("❌ Flipside connection failed:", result.error.message);
      return false;
    }

    console.log("✅ Flipside connection successful");
    console.log(`   Query returned ${result.value.length} records (expected 0 for test data)`);
    return true;
  } catch (error) {
    console.log("❌ Flipside connection error:", error instanceof Error ? error.message : String(error));
    return false;
  }
}

async function testDatabaseOperations() {
  console.log("\n💾 Testing database operations...");
  
  try {
    const service = new CryptoExpenseService(prisma);

    // Test getting crypto expenses (should return empty initially)
    const expenses = await service.getCryptoExpenses({ limit: 1 });
    console.log("✅ Database read successful");
    console.log(`   Found ${expenses.total} existing crypto expenses`);

    // Test getting stats (should return zeros initially)
    const stats = await service.getCryptoExpenseStats();
    console.log("✅ Database stats query successful");
    console.log(`   Total transactions: ${stats.totalTransactions}`);
    console.log(`   Total USD: $${stats.totalSwapFromUsd}`);

    return true;
  } catch (error) {
    console.log("❌ Database operation failed:", error instanceof Error ? error.message : String(error));
    return false;
  }
}

async function testConfigurationValidation() {
  console.log("\n⚙️  Testing configuration...");
  
  try {
    // Test configuration imports
    const { 
      getCompanyWallets, 
      getBuybackTokenMints, 
      validateDateRange,
      validateWalletAddress 
    } = await import("@/modules/crypto-expenses/config");

    // Test date range validation
    validateDateRange(new Date("2024-01-01"), new Date("2024-01-31"));
    console.log("✅ Date range validation working");

    // Test wallet address validation
    const validAddress = "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj";
    const isValid = validateWalletAddress(validAddress);
    console.log(`✅ Wallet address validation working (${validAddress} is ${isValid ? 'valid' : 'invalid'})`);

    // Test token mints
    const mints = getBuybackTokenMints();
    console.log(`✅ Token mints configured: ${mints.length} mints`);

    // Test company wallets (may throw if not configured)
    try {
      const wallets = getCompanyWallets();
      console.log(`✅ Company wallets configured: ${wallets.length} wallets`);
    } catch (error) {
      console.log("⚠️  Company wallets not configured (this is expected for initial setup)");
      console.log("   Add your wallet addresses to src/modules/crypto-expenses/config.ts");
    }

    return true;
  } catch (error) {
    console.log("❌ Configuration test failed:", error instanceof Error ? error.message : String(error));
    return false;
  }
}

async function testImportWithMockData() {
  console.log("\n🧪 Testing import with mock data...");
  
  try {
    const service = new CryptoExpenseService(prisma);

    // Test import with non-existent wallets (should return 0 imported)
    const result = await service.importBuybacks({
      companyWallets: ["********************************"], // Invalid wallet
      targetMints: ["********************************"], // Invalid mint
      fromDate: new Date("2024-01-01"),
      toDate: new Date("2024-01-02"),
      createTransactions: false,
    });

    if (result.isErr()) {
      console.log("❌ Import test failed:", result.error.message);
      return false;
    }

    console.log("✅ Import functionality working");
    console.log(`   Imported: ${result.value.imported} records`);
    console.log(`   Skipped: ${result.value.skipped} records`);
    console.log(`   Errors: ${result.value.errors} records`);

    return true;
  } catch (error) {
    console.log("❌ Import test failed:", error instanceof Error ? error.message : String(error));
    return false;
  }
}

async function main() {
  console.log("🚀 Crypto Expenses Module Test Suite");
  console.log("=====================================\n");

  const tests = [
    { name: "Configuration Validation", fn: testConfigurationValidation },
    { name: "Database Operations", fn: testDatabaseOperations },
    { name: "Flipside Connection", fn: testFlipsideConnection },
    { name: "Import with Mock Data", fn: testImportWithMockData },
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const success = await test.fn();
      if (success) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test "${test.name}" threw an error:`, error instanceof Error ? error.message : String(error));
      failed++;
    }
  }

  console.log("\n📊 Test Results:");
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log("\n🎉 All tests passed! The crypto expenses module is ready to use.");
    console.log("\nNext steps:");
    console.log("1. Configure your company wallets in src/modules/crypto-expenses/config.ts");
    console.log("2. Run the import script with your actual wallet addresses");
    console.log("3. Check the README.md for detailed usage instructions");
  } else {
    console.log("\n⚠️  Some tests failed. Please check the errors above and fix any issues.");
  }
}

if (require.main === module) {
  main()
    .catch((error) => {
      console.error("❌ Test suite failed:", error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}
