#!/usr/bin/env tsx

/**
 * Test script to verify the refactored crypto expense structure
 * Creates test data for all expense types in the new table structure
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function createTestRefactoredCryptoExpenses() {
  console.log("🧪 Testing Refactored Crypto Expense Structure");
  console.log("==============================================\n");

  try {
    // Create a test wallet account
    console.log("1. Creating test wallet account...");
    const testWallet = await prisma.account.create({
      data: {
        name: "Test Refactored Wallet",
        type: "WALLET",
        publicKey: "RefactoredTest123456789012345678901234567890",
      },
    });
    console.log(`✅ Created wallet account: ${testWallet.name} (${testWallet.id})`);

    // Create test crypto expenses with the new structure
    console.log("\n2. Creating test crypto expenses with new structure...");
    
    const testExpenses = [];

    // 1. Create a BUYBACK expense
    console.log("\n   Creating BUYBACK expense...");
    const buybackTransaction = await prisma.transaction.create({
      data: {
        executedAt: new Date(),
        accountId: testWallet.id,
        metadata: {
          source: "test-refactored-crypto-expense",
          type: "buyback",
        },
      },
    });

    const buybackExpense = await prisma.cryptoExpense.create({
      data: {
        type: "BUYBACK",
        txId: `RefactoredBuyback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        blockTimestamp: new Date(),
        swapper: testWallet.publicKey!,
        amountUsd: 1500.75,
        transactionId: buybackTransaction.id,
        buyback: {
          create: {
            swapFromSymbol: "USDC",
            swapToSymbol: "ALL",
            swapFromAmount: 1500.75,
            swapToAmount: 75037.5,
            swapFromAmountUsd: 1500.75,
            swapToAmountUsd: 1500.75,
            swapFromMint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            swapToMint: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
          },
        },
      },
    });
    testExpenses.push({ type: "BUYBACK", expense: buybackExpense, transaction: buybackTransaction });
    console.log(`   ✅ Created BUYBACK expense: ${buybackExpense.id}`);

    // 2. Create a FLOOR_SWEEP expense
    console.log("\n   Creating FLOOR_SWEEP expense...");
    const floorSweepTransaction = await prisma.transaction.create({
      data: {
        executedAt: new Date(),
        accountId: testWallet.id,
        metadata: {
          source: "test-refactored-crypto-expense",
          type: "floor-sweep",
        },
      },
    });

    const floorSweepExpense = await prisma.cryptoExpense.create({
      data: {
        type: "FLOOR_SWEEP",
        txId: `RefactoredFloorSweep_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        blockTimestamp: new Date(),
        swapper: testWallet.publicKey!,
        amountUsd: 350.25,
        transactionId: floorSweepTransaction.id,
        floorSweep: {
          create: {
            collectionId: "test-nft-collection-123",
            collectionName: "Test NFT Collection",
            tokenId: "42",
            mintAddress: "TestNFTMint123456789012345678901234567890",
            priceUsd: 350.25,
          },
        },
      },
    });
    testExpenses.push({ type: "FLOOR_SWEEP", expense: floorSweepExpense, transaction: floorSweepTransaction });
    console.log(`   ✅ Created FLOOR_SWEEP expense: ${floorSweepExpense.id}`);

    // 3. Create a LIQUIDITY_POOL expense
    console.log("\n   Creating LIQUIDITY_POOL expense...");
    const liquidityPoolTransaction = await prisma.transaction.create({
      data: {
        executedAt: new Date(),
        accountId: testWallet.id,
        metadata: {
          source: "test-refactored-crypto-expense",
          type: "liquidity-pool",
        },
      },
    });

    const liquidityPoolExpense = await prisma.cryptoExpense.create({
      data: {
        type: "LIQUIDITY_POOL",
        txId: `RefactoredLP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        blockTimestamp: new Date(),
        swapper: testWallet.publicKey!,
        amountUsd: 2250.50,
        transactionId: liquidityPoolTransaction.id,
        liquidityPool: {
          create: {
            action: "add_liquidity",
            poolAddress: "TestPoolAddress123456789012345678901234567890",
            tokenAMint: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
            tokenBMint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            tokenASymbol: "ALL",
            tokenBSymbol: "USDC",
            tokenAAmount: 112525.0,
            tokenBAmount: 2250.50,
            tokenAAmountUsd: 2250.50,
            tokenBAmountUsd: 2250.50,
          },
        },
      },
    });
    testExpenses.push({ type: "LIQUIDITY_POOL", expense: liquidityPoolExpense, transaction: liquidityPoolTransaction });
    console.log(`   ✅ Created LIQUIDITY_POOL expense: ${liquidityPoolExpense.id}`);

    // Verify the data can be queried correctly with the new structure
    console.log("\n3. Verifying transactions with refactored crypto expenses...");
    for (let i = 0; i < testExpenses.length; i++) {
      const { transaction, type } = testExpenses[i];
      const transactionWithCryptoExpense = await prisma.transaction.findUnique({
        where: { id: transaction.id },
        include: {
          cryptoExpense: {
            include: {
              buyback: true,
              floorSweep: true,
              liquidityPool: true,
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      if (transactionWithCryptoExpense?.cryptoExpense) {
        console.log(`   ✅ ${type} transaction query successful`);
        console.log(`      Transaction ID: ${transactionWithCryptoExpense.id}`);
        console.log(`      Account: ${transactionWithCryptoExpense.account.name}`);
        console.log(`      Crypto Expense Type: ${transactionWithCryptoExpense.cryptoExpense.type}`);
        console.log(`      Amount USD: $${transactionWithCryptoExpense.cryptoExpense.amountUsd}`);
        
        if (transactionWithCryptoExpense.cryptoExpense.buyback) {
          const bb = transactionWithCryptoExpense.cryptoExpense.buyback;
          console.log(`      Buyback: ${bb.swapFromSymbol} → ${bb.swapToSymbol} (${bb.swapFromAmount} → ${bb.swapToAmount})`);
        }
        if (transactionWithCryptoExpense.cryptoExpense.floorSweep) {
          const fs = transactionWithCryptoExpense.cryptoExpense.floorSweep;
          console.log(`      Floor Sweep: ${fs.collectionName} - Token ${fs.tokenId} ($${fs.priceUsd})`);
        }
        if (transactionWithCryptoExpense.cryptoExpense.liquidityPool) {
          const lp = transactionWithCryptoExpense.cryptoExpense.liquidityPool;
          console.log(`      LP Action: ${lp.action} - ${lp.tokenASymbol}/${lp.tokenBSymbol} (${lp.tokenAAmount}/${lp.tokenBAmount})`);
        }
      } else {
        console.log(`   ❌ Failed to query ${type} transaction with crypto expense`);
      }
    }

    console.log("\n4. Testing transaction stats...");
    const stats = await prisma.transaction.count({
      where: {
        cryptoExpense: {
          isNot: null,
        },
      },
    });
    console.log(`   ✅ Total transactions with crypto expenses: ${stats}`);

    const statsByType = await prisma.cryptoExpense.groupBy({
      by: ['type'],
      _count: { type: true },
    });
    console.log("   📊 Crypto expenses by type:");
    statsByType.forEach(group => {
      console.log(`      ${group.type}: ${group._count.type}`);
    });

    console.log("\n📋 Test Results Summary:");
    console.log("========================");
    console.log(`✅ Wallet Account Created: ${testWallet.name}`);
    console.log(`✅ Transactions Created: ${testExpenses.length}`);
    console.log(`✅ Crypto Expenses Created: ${testExpenses.length}`);
    
    testExpenses.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.type}: $${item.expense.amountUsd}`);
    });

    console.log("\n🎯 Frontend Testing Instructions:");
    console.log("=================================");
    console.log("1. Open the transactions page in your browser");
    console.log("2. Look for the test transactions created above");
    console.log("3. Verify the following displays correctly:");
    console.log(`   • Type badges: "Buyback", "Floor Sweep", "LP Action"`);
    console.log(`   • Amount: EUR formatted (e.g., €1,500.75, €350.25, €2,250.50)`);
    console.log(`   • Description: Type-specific descriptions`);
    console.log(`   • Account: "Test Refactored Wallet"`);
    console.log("4. Click on each transaction to open details");
    console.log("5. Verify type-specific details sections show correctly");
    console.log("6. Verify all copy buttons and Solscan links work");

    console.log("\n🧹 Cleanup:");
    console.log("===========");
    console.log("Run this script with --cleanup flag to remove test data");

    return {
      walletId: testWallet.id,
      transactionIds: testExpenses.map(item => item.transaction.id),
      cryptoExpenseIds: testExpenses.map(item => item.expense.id),
    };

  } catch (error) {
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function cleanupTestData() {
  console.log("🧹 Cleaning up test data...\n");

  try {
    // Delete test crypto expenses and related records
    const deletedExpenses = await prisma.cryptoExpense.deleteMany({
      where: {
        txId: {
          startsWith: "Refactored",
        },
      },
    });
    console.log(`✅ Deleted ${deletedExpenses.count} test crypto expenses`);

    // Delete test transactions
    const deletedTransactions = await prisma.transaction.deleteMany({
      where: {
        metadata: {
          path: ["source"],
          equals: "test-refactored-crypto-expense",
        },
      },
    });
    console.log(`✅ Deleted ${deletedTransactions.count} test transactions`);

    // Delete test wallet accounts
    const deletedWallets = await prisma.account.deleteMany({
      where: {
        name: "Test Refactored Wallet",
      },
    });
    console.log(`✅ Deleted ${deletedWallets.count} test wallet accounts`);

    console.log("\n✅ Cleanup completed successfully");

  } catch (error) {
    console.error("❌ Cleanup failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const isCleanup = args.includes("--cleanup");

  try {
    if (isCleanup) {
      await cleanupTestData();
    } else {
      await createTestRefactoredCryptoExpenses();
    }
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
