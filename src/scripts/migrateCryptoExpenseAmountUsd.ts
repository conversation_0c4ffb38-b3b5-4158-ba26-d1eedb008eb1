#!/usr/bin/env tsx

/**
 * Migration script to populate the amountUsd field for existing crypto expenses
 * This script updates existing BUYBACK records to set amountUsd = swapFromAmountUsd
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function migrateCryptoExpenseAmountUsd() {
  console.log("🔄 Migrating crypto expense amountUsd field...\n");

  try {
    // Get all crypto expenses that don't have amountUsd set
    const cryptoExpenses = await prisma.cryptoExpense.findMany({
      where: {
        amountUsd: null,
      },
      select: {
        id: true,
        type: true,
        swapFromAmountUsd: true,
        priceUsd: true,
      },
    });

    console.log(`Found ${cryptoExpenses.length} crypto expenses to migrate`);

    if (cryptoExpenses.length === 0) {
      console.log("✅ No migration needed - all records already have amountUsd set");
      return;
    }

    let updated = 0;
    let errors = 0;

    // Update records in batches
    for (const expense of cryptoExpenses) {
      try {
        let amountUsd: number;

        // Determine the amount based on type
        if (expense.type === "BUYBACK") {
          amountUsd = Number(expense.swapFromAmountUsd || 0);
        } else if (expense.type === "FLOOR_SWEEP") {
          amountUsd = Number(expense.priceUsd || 0);
        } else {
          // For OTHER type, use swapFromAmountUsd as fallback
          amountUsd = Number(expense.swapFromAmountUsd || 0);
        }

        await prisma.cryptoExpense.update({
          where: { id: expense.id },
          data: { amountUsd },
        });

        updated++;
        
        if (updated % 10 === 0) {
          console.log(`   Updated ${updated}/${cryptoExpenses.length} records...`);
        }
      } catch (error) {
        console.error(`Failed to update record ${expense.id}:`, error);
        errors++;
      }
    }

    console.log(`\n✅ Migration completed:`);
    console.log(`   Updated: ${updated} records`);
    console.log(`   Errors: ${errors} records`);

    if (errors === 0) {
      console.log("\n🎉 All crypto expenses now have amountUsd populated!");
    } else {
      console.log(`\n⚠️  ${errors} records failed to update. Please check the logs above.`);
    }

  } catch (error) {
    console.error("❌ Migration failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function verifyMigration() {
  console.log("\n🔍 Verifying migration...");

  try {
    const totalRecords = await prisma.cryptoExpense.count();
    const recordsWithAmountUsd = await prisma.cryptoExpense.count({
      where: {
        amountUsd: { not: null },
      },
    });

    console.log(`   Total crypto expenses: ${totalRecords}`);
    console.log(`   Records with amountUsd: ${recordsWithAmountUsd}`);
    console.log(`   Missing amountUsd: ${totalRecords - recordsWithAmountUsd}`);

    if (totalRecords === recordsWithAmountUsd) {
      console.log("✅ Migration verification successful - all records have amountUsd");
    } else {
      console.log("⚠️  Migration verification failed - some records still missing amountUsd");
    }

    // Show sample of migrated data
    const sampleRecords = await prisma.cryptoExpense.findMany({
      take: 3,
      select: {
        id: true,
        type: true,
        amountUsd: true,
        swapFromAmountUsd: true,
        priceUsd: true,
      },
    });

    if (sampleRecords.length > 0) {
      console.log("\n📋 Sample migrated records:");
      sampleRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. Type: ${record.type}, amountUsd: ${record.amountUsd}, swapFromAmountUsd: ${record.swapFromAmountUsd}, priceUsd: ${record.priceUsd}`);
      });
    }

  } catch (error) {
    console.error("❌ Verification failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  try {
    await migrateCryptoExpenseAmountUsd();
    await verifyMigration();
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
