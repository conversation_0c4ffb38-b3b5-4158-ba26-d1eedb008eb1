#!/usr/bin/env tsx

/**
 * Test script to verify that full transaction IDs are displayed correctly
 * Creates test crypto expenses with realistic Solana transaction IDs
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function createTestTransactionWithFullSignature() {
  console.log("🧪 Testing Full Transaction ID Display");
  console.log("======================================\n");

  try {
    // Create a test wallet account
    console.log("1. Creating test wallet account...");
    const testWallet = await prisma.account.create({
      data: {
        name: "Test Full Signature Wallet",
        type: "WALLET",
        publicKey: "FullSigTest123456789012345678901234567890",
      },
    });
    console.log(`✅ Created wallet account: ${testWallet.name} (${testWallet.id})`);

    // Create test crypto expenses with realistic Solana transaction signatures
    console.log("\n2. Creating test crypto expenses with full signatures...");

    const testSignatures = [
      // Realistic Solana transaction signatures (base58 encoded, ~88 characters)
      "5VfYmGC5GKKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjG",
      "3RdHumGC5GKKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjG",
      "4AbCdEfGhIjKlMnOpQrStUvWxYz123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
    ];

    const cryptoExpenses = [];
    const transactions = [];

    for (let i = 0; i < testSignatures.length; i++) {
      const signature = testSignatures[i];
      const expenseType = i === 0 ? "BUYBACK" : i === 1 ? "FLOOR_SWEEP" : "OTHER";

      // Create a separate transaction for each crypto expense
      const testTransaction = await prisma.transaction.create({
        data: {
          executedAt: new Date(),
          accountId: testWallet.id,
          metadata: {
            source: "test-full-signature-display",
            expenseType,
          },
        },
      });
      transactions.push(testTransaction);

      let cryptoExpense;

      if (expenseType === "BUYBACK") {
        cryptoExpense = await prisma.cryptoExpense.create({
          data: {
            type: "BUYBACK",
            txId: signature,
            blockTimestamp: new Date(),
            swapper: testWallet.publicKey!,
            swapFromSymbol: "USDC",
            swapToSymbol: "TOKEN",
            swapFromAmount: 1000.50,
            swapToAmount: 500000.25,
            swapFromAmountUsd: 1000.50,
            swapToAmountUsd: 1000.50,
            amountUsd: 1000.50,
            transactionId: testTransaction.id,
            rawFlipsideData: {
              test: true,
              source: "test-script",
            },
          },
        });
      } else if (expenseType === "FLOOR_SWEEP") {
        cryptoExpense = await prisma.cryptoExpense.create({
          data: {
            type: "FLOOR_SWEEP",
            txId: signature,
            blockTimestamp: new Date(),
            swapper: testWallet.publicKey!,
            nftCollectionId: "test-collection-123",
            nftCollectionName: "Test NFT Collection",
            nftTokenId: "12345",
            nftMintAddress: "NFTMintAddress123456789012345678901234567890",
            priceUsd: 250.75,
            amountUsd: 250.75,
            transactionId: testTransaction.id,
            rawFlipsideData: {
              test: true,
              source: "test-script",
            },
          },
        });
      } else {
        cryptoExpense = await prisma.cryptoExpense.create({
          data: {
            type: "OTHER",
            txId: signature,
            blockTimestamp: new Date(),
            swapper: testWallet.publicKey!,
            amountUsd: 100.00,
            transactionId: testTransaction.id,
            rawFlipsideData: {
              test: true,
              source: "test-script",
            },
          },
        });
      }

      cryptoExpenses.push(cryptoExpense);
      console.log(`✅ Created ${expenseType} crypto expense: ${cryptoExpense.id}`);
      console.log(`   Transaction: ${testTransaction.id}`);
      console.log(`   Full Transaction ID: ${signature}`);
      console.log(`   Length: ${signature.length} characters`);
    }

    // Verify the data can be queried correctly
    console.log("\n3. Verifying transactions with crypto expenses...");
    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      const transactionWithCryptoExpense = await prisma.transaction.findUnique({
        where: { id: transaction.id },
        include: {
          cryptoExpense: {
            select: {
              id: true,
              type: true,
              txId: true,
              swapFromSymbol: true,
              swapToSymbol: true,
              nftCollectionName: true,
              nftMintAddress: true,
              amountUsd: true,
              blockTimestamp: true,
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      if (transactionWithCryptoExpense?.cryptoExpense) {
        console.log(`✅ Transaction ${i + 1} query successful`);
        console.log(`   Transaction ID: ${transactionWithCryptoExpense.id}`);
        console.log(`   Account: ${transactionWithCryptoExpense.account.name}`);
        console.log(`   Crypto Expense Type: ${transactionWithCryptoExpense.cryptoExpense.type}`);
        console.log(`   Full Transaction Signature: ${transactionWithCryptoExpense.cryptoExpense.txId}`);
        console.log(`   Signature Length: ${transactionWithCryptoExpense.cryptoExpense.txId.length} characters`);
      } else {
        console.log(`❌ Failed to query transaction ${i + 1} with crypto expense`);
      }
    }

    console.log("\n📋 Test Results Summary:");
    console.log("========================");
    console.log(`✅ Wallet Account Created: ${testWallet.name}`);
    console.log(`✅ Transactions Created: ${transactions.length}`);
    console.log(`✅ Crypto Expenses Created: ${cryptoExpenses.length}`);

    cryptoExpenses.forEach((expense, index) => {
      console.log(`   ${index + 1}. ${expense.type}: ${expense.txId}`);
    });

    console.log("\n🎯 Frontend Testing Instructions:");
    console.log("=================================");
    console.log("1. Open the transactions page in your browser");
    console.log("2. Look for the test transaction created above");
    console.log("3. Click on the transaction to open details");
    console.log("4. Verify the crypto expense details section shows:");
    console.log("   • Full transaction ID (not truncated)");
    console.log("   • Transaction ID is selectable and copyable");
    console.log("   • Copy button works correctly");
    console.log("   • Solscan button opens the correct URL");
    console.log("   • For floor sweeps: NFT mint address is also full and copyable");

    console.log("\n🔗 Expected Solscan URLs:");
    testSignatures.forEach((sig, index) => {
      console.log(`   ${index + 1}. https://solscan.io/tx/${sig}`);
    });

    console.log("\n🧹 Cleanup:");
    console.log("===========");
    console.log("Run this script with --cleanup flag to remove test data");

    return {
      walletId: testWallet.id,
      transactionIds: transactions.map(t => t.id),
      cryptoExpenseIds: cryptoExpenses.map(e => e.id),
    };

  } catch (error) {
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function cleanupTestData() {
  console.log("🧹 Cleaning up test data...\n");

  try {
    // Delete test crypto expenses
    const deletedExpenses = await prisma.cryptoExpense.deleteMany({
      where: {
        txId: {
          in: [
            "5VfYmGC5GKKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjG",
            "3RdHumGC5GKKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjGKjKQtKjjG",
            "4AbCdEfGhIjKlMnOpQrStUvWxYz123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
          ],
        },
      },
    });
    console.log(`✅ Deleted ${deletedExpenses.count} test crypto expenses`);

    // Delete test transactions
    const deletedTransactions = await prisma.transaction.deleteMany({
      where: {
        metadata: {
          path: ["source"],
          equals: "test-full-signature-display",
        },
      },
    });
    console.log(`✅ Deleted ${deletedTransactions.count} test transactions`);

    // Delete test wallet accounts
    const deletedWallets = await prisma.account.deleteMany({
      where: {
        name: "Test Full Signature Wallet",
      },
    });
    console.log(`✅ Deleted ${deletedWallets.count} test wallet accounts`);

    console.log("\n✅ Cleanup completed successfully");

  } catch (error) {
    console.error("❌ Cleanup failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const isCleanup = args.includes("--cleanup");

  try {
    if (isCleanup) {
      await cleanupTestData();
    } else {
      await createTestTransactionWithFullSignature();
    }
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
