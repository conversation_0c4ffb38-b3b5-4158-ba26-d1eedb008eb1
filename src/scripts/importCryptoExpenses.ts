#!/usr/bin/env tsx

/**
 * Interactive script to import crypto expenses from Flipside API
 *
 * Usage:
 *   pnpm tsx src/scripts/importCryptoExpenses.ts [options]
 *
 * Interactive Mode (default):
 *   - Select wallets from database
 *   - Choose date range
 *   - Configure import options
 *
 * CLI Options:
 *   --type <type>              Type of crypto expense to import (buybacks, other) [default: buybacks]
 *   --from-date <date>         Start date (YYYY-MM-DD format)
 *   --to-date <date>           End date (YYYY-MM-DD format)
 *   --company-wallets <wallets> Comma-separated list of company wallet addresses
 *   --target-mints <mints>     Comma-separated list of target token mint addresses
 *   --create-transactions      Create transaction records in addition to crypto expense records
 *   --account-id <id>          Account ID to use for transactions (required if --create-transactions)
 *   --preview                  Preview data without importing
 *   --dry-run                  Show what would be imported without actually importing
 *   --delete-existing          Delete existing crypto expenses before importing
 *   --non-interactive          Skip interactive prompts and use CLI args only
 *   --help                     Show this help message
 */

import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import inquirer from "inquirer";
import { PrismaClient } from "@/prisma/generated";
import { CryptoExpenseService } from "@/modules/crypto-expenses/cryptoExpenseService";
import { CryptoExpenseQueries } from "@/modules/flipside/cryptoExpenseQueries";
import fs from 'fs'
import asyncBatch from "async-batch"

const prisma = new PrismaClient();

interface ImportOptions {
  type: "buybacks" | "floor-sweeps" | "liquidity-pools" | "transfers" | "other";
  fromDate?: Date;
  toDate?: Date;
  companyWallets?: string[];
  targetMints?: string[];
  collectionIds?: string[];
  liquidityPoolTokenMints?: string[];
  createTransactions: boolean;
  accountId?: string;
  preview: boolean;
  dryRun: boolean;
  deleteExisting: boolean;
  nonInteractive: boolean;
  skipCryptoExpenseCheck: boolean;
}

interface WalletAccount {
  id: string;
  name: string;
  publicKey: string;
  createdAt: Date;
}

async function fetchWalletAccounts(): Promise<WalletAccount[]> {
  try {
    const wallets = await prisma.account.findMany({
      where: {
        type: "WALLET",
        publicKey: { not: null },
      },
      select: {
        id: true,
        name: true,
        publicKey: true,
        createdAt: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    return wallets.filter((wallet): wallet is WalletAccount => wallet.publicKey !== null);
  } catch (error) {
    console.error("❌ Failed to fetch wallet accounts:", error);
    return [];
  }
}

async function interactiveWalletSelection(wallets: WalletAccount[]): Promise<string[]> {
  if (wallets.length === 0) {
    console.log("⚠️  No wallet accounts with public keys found in the database.");
    console.log("   Please create wallet accounts with public keys first.");
    return [];
  }

  console.log(`\n📋 Found ${wallets.length} wallet account(s) in the database:\n`);

  // Display available wallets
  wallets.forEach((wallet, index) => {
    const truncatedKey = `${wallet.publicKey.slice(0, 8)}...${wallet.publicKey.slice(-4)}`;
    console.log(`   ${index + 1}. ${wallet.name} (${truncatedKey})`);
  });

  const { walletSelection } = await inquirer.prompt([
    {
      type: "list",
      name: "walletSelection",
      message: "How would you like to select wallets?",
      choices: [
        { name: "Import from all wallets", value: "all" },
        { name: "Select specific wallets", value: "select" },
        { name: "Enter custom wallet addresses", value: "custom" },
      ],
    },
  ]);

  if (walletSelection === "all") {
    console.log("✅ Selected all wallet accounts");
    return wallets.map((w) => w.publicKey);
  }

  if (walletSelection === "select") {
    const { selectedWallets } = await inquirer.prompt([
      {
        type: "checkbox",
        name: "selectedWallets",
        message: "Select wallets to import from (use space to select, enter to continue):",
        choices: wallets.map((wallet) => ({
          name: `${wallet.name} (${wallet.publicKey.slice(0, 8)}...${wallet.publicKey.slice(-4)})`,
          value: wallet.publicKey,
          checked: false,
        })),
        validate: (input) => {
          if (input.length === 0) {
            return "Please select at least one wallet";
          }
          return true;
        },
      },
    ]);

    console.log(`✅ Selected ${selectedWallets.length} wallet(s)`);
    return selectedWallets;
  }

  if (walletSelection === "custom") {
    const { customWallets } = await inquirer.prompt([
      {
        type: "input",
        name: "customWallets",
        message: "Enter wallet addresses (comma-separated):",
        validate: (input) => {
          if (!input.trim()) {
            return "Please enter at least one wallet address";
          }
          const addresses = input.split(",").map((addr) => addr.trim());
          const solanaRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
          const invalid = addresses.filter((addr) => !solanaRegex.test(addr));
          if (invalid.length > 0) {
            return `Invalid wallet address format: ${invalid.join(", ")}`;
          }
          return true;
        },
      },
    ]);

    const addresses = customWallets.split(",").map((addr: string) => addr.trim());
    console.log(`✅ Using ${addresses.length} custom wallet address(es)`);
    return addresses;
  }

  return [];
}

async function interactiveConfiguration(): Promise<Partial<ImportOptions>> {
  console.log("\n⚙️  Configuration Options\n");

  const config = await inquirer.prompt([
    {
      type: "input",
      name: "fromDate",
      message: "From date (YYYY-MM-DD, leave empty for all time):",
      validate: (input) => {
        if (!input.trim()) return true;
        const date = new Date(input);
        if (isNaN(date.getTime())) {
          return "Invalid date format. Use YYYY-MM-DD";
        }
        return true;
      },
    },
    {
      type: "input",
      name: "toDate",
      message: "To date (YYYY-MM-DD, leave empty for now):",
      validate: (input) => {
        if (!input.trim()) return true;
        const date = new Date(input);
        if (isNaN(date.getTime())) {
          return "Invalid date format. Use YYYY-MM-DD";
        }
        return true;
      },
    },
    {
      type: "list",
      name: "action",
      message: "What would you like to do?",
      choices: [
        { name: "Preview data (no import)", value: "preview" },
        { name: "Import crypto expenses only", value: "import" },
        { name: "Import crypto expenses and create transactions", value: "import-transactions" },
        { name: "import transfers from accounts", value: "transfers" }
      ],
    },
  ]);

  const options: Partial<ImportOptions> = {
    fromDate: config.fromDate ? new Date(config.fromDate) : undefined,
    toDate: config.toDate ? new Date(config.toDate) : undefined,
    preview: config.action === "preview",
    createTransactions: config.action === "import-transactions",
  };

  return options;
}

async function previewTransfers(options: ImportOptions) {
  console.log("🔍 Previewing transfer data...");

  const result = await CryptoExpenseQueries.getAllNativeTransfers(
    options.companyWallets,
    options.fromDate,
    options.toDate
  );

  if (result.isErr()) {
    console.error("❌ Preview failed:", result.error.message);
    process.exit(1);
  }

  const records = result.value;
  console.log(`📊 Found ${records.length} transfer records`);

  if (records.length > 0) {
    console.log("\n📋 Sample records:");
    const sampleSize = Math.min(5, records.length);
    for (let i = 0; i < sampleSize; i++) {
      const record = records[i];
      console.log(`   ${i + 1}. ${record.tx_from} → ${record.tx_to}`);
      console.log(`      Amount: ${record.amount} ${record.token}`);
      console.log(`      Date: ${record.block_timestamp}`);
      console.log(`      TX: ${record.tx_id}`);
      console.log("");
    }

    if (records.length > sampleSize) {
      console.log(`   ... and ${records.length - sampleSize} more records`);
    }

    // Group by token for summary
    const tokenSummary = records.reduce((acc, record) => {
      if (!acc[record.token]) {
        acc[record.token] = { count: 0, totalAmount: 0 };
      }
      acc[record.token].count++;
      acc[record.token].totalAmount += record.amount;
      return acc;
    }, {} as Record<string, { count: number; totalAmount: number }>);

    console.log("\n📈 Summary by token:");
    Object.entries(tokenSummary).forEach(([token, summary]) => {
      console.log(`   ${token}: ${summary.count} transfers, ${summary.totalAmount.toFixed(6)} total`);
    });

    // Check for potential self-transfers by looking up existing accounts
    const allAccounts = await prisma.account.findMany({
      where: { publicKey: { not: null } }
    });
    const accountLookup = new Map<string, string>();
    allAccounts.forEach((account: any) => {
      if (account.publicKey) {
        accountLookup.set(account.publicKey, account.name);
      }
    });

    // Group by counterparty for summary
    const counterpartySummary = records.reduce((acc, record) => {
      if (!acc[record.tx_to]) {
        acc[record.tx_to] = {
          count: 0,
          tokens: {} as Record<string, number>,
          isSelfTransfer: accountLookup.has(record.tx_to),
          accountName: accountLookup.get(record.tx_to)
        };
      }
      acc[record.tx_to].count++;
      if (!acc[record.tx_to].tokens[record.token]) {
        acc[record.tx_to].tokens[record.token] = 0;
      }
      acc[record.tx_to].tokens[record.token] += record.amount;
      return acc;
    }, {} as Record<string, { count: number; tokens: Record<string, number>; isSelfTransfer: boolean; accountName?: string }>);

    console.log("\n🏢 Top counterparties:");
    const topCounterparties = Object.entries(counterpartySummary)
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 10);

    topCounterparties.forEach(([counterparty, summary]) => {
      const typeLabel = summary.isSelfTransfer ? ' (Self-Transfer)' : '';
      const accountLabel = summary.accountName ? ` - ${summary.accountName}` : '';
      console.log(`   ${counterparty}${typeLabel}${accountLabel}: ${summary.count} transfers`);
      Object.entries(summary.tokens).forEach(([token, amount]) => {
        console.log(`     ${token}: ${amount.toFixed(6)}`);
      });
    });

    // Show self-transfer summary
    const selfTransfers = Object.entries(counterpartySummary).filter(([_, summary]) => summary.isSelfTransfer);
    if (selfTransfers.length > 0) {
      console.log(`\n🔄 Self-transfers detected: ${selfTransfers.length} of our own accounts`);
      const totalSelfTransfers = selfTransfers.reduce((sum, [_, summary]) => sum + summary.count, 0);
      console.log(`   Total self-transfers: ${totalSelfTransfers}`);
    }
  }
}

async function getCompanyWalletsFromDB() {
  const wallets = await prisma.account.findMany({
    where: {
      type: "WALLET",
      publicKey: { not: null }
    },
    select: {
      publicKey: true
    }
  });
  return wallets.map((w) => w.publicKey!);
}

async function importTransfers(options: ImportOptions) {
  console.log("📥 Importing transfer data...");

  const companyWallets = await (options.companyWallets || getCompanyWalletsFromDB());

  if (options.deleteExisting) {
    console.log("🗑️  Deleting existing transfers...");
    const deletedCount = await prisma.transfer.deleteMany({
      where: {
        transaction: {
          account: {
            publicKey: {
              in: companyWallets
            }
          }
        }
      }
    });
    console.log(`   Deleted ${deletedCount.count} existing transfer records`);
  }

  const result = await CryptoExpenseQueries.getAllNativeTransfers(
    companyWallets,
    options.fromDate,
    options.toDate
  );

  if (result.isErr()) {
    console.error("❌ Import failed:", result.error.message);
    process.exit(1);
  }

  const records = result.value;
  console.log(`📊 Processing ${records.length} transfer records`);

  if (records.length === 0) {
    console.log("✅ No transfers to import");
    return;
  }

  // export all records to json
  fs.writeFileSync("all-wallet-transfers.json", JSON.stringify(records, null, 2));

  // Get or create accounts for the company wallets
  const accountMap = new Map<string, string>();

  for (const wallet of options.companyWallets || []) {
    let account = await prisma.account.findFirst({
      where: { publicKey: wallet }
    });

    if (!account) {
      account = await prisma.account.create({
        data: {
          name: `${wallet.slice(0, 8)}...`,
          type: "WALLET",
          publicKey: wallet
        }
      });
      console.log(`Created account for wallet ${wallet}`);
    }

    accountMap.set(wallet, account.id);
  }

  // Also get all existing accounts to check for counterparty matches
  const allAccounts = await prisma.account.findMany({
    where: { publicKey: { not: null } }
  });
  const counterpartyAccountMap = new Map<string, string>();
  allAccounts.forEach(account => {
    if (account.publicKey) {
      counterpartyAccountMap.set(account.publicKey, account.id);
    }
  });

  let importedCount = 0;
  let skippedCount = 0;
  let cryptoExpenseSkippedCount = 0;

  await asyncBatch(records, async (record) => {
    try {
      const accountId = accountMap.get(record.tx_from);
      if (!accountId) {
        console.warn(`   Skipping transfer from unknown wallet: ${record.tx_from}`);
        skippedCount++;
        return;
      }

      // Check if this transaction is already tracked as a CryptoExpense (unless disabled)
      if (!options.skipCryptoExpenseCheck) {
        const existingCryptoExpense = await prisma.cryptoExpense.findFirst({
          where: {
            txId: record.tx_id,
            type: {
              in: ['BUYBACK', 'FLOOR_SWEEP', 'LIQUIDITY_POOL']
            }
          }
        });

        if (existingCryptoExpense) {
          console.log(`   Skipping transfer ${record.tx_id} - already tracked as ${existingCryptoExpense.type}`);
          cryptoExpenseSkippedCount++;
          return;
        }
      }

      // Check if transfer already exists
      const existingTransfer = await prisma.transfer.findFirst({
        where: {
          transaction: {
            account: { publicKey: record.tx_from },
            executedAt: new Date(record.block_timestamp)
          },
          counterparty: record.tx_to,
          amount: record.amount,
          currencyCode: record.token
        }
      });

      if (existingTransfer) {
        skippedCount++;
        return;
      }

      // Check if counterparty is one of our own accounts (self-transfer)
      const counterpartyAccountId = counterpartyAccountMap.get(record.tx_to);
      const isSelfTransfer = !!counterpartyAccountId;

      // Create transaction and transfer
      await prisma.transaction.create({
        data: {
          executedAt: new Date(record.block_timestamp),
          accountId: accountId,
          isSelfTransfer,
          solanaTransactionId: record.tx_id,
          metadata: {
            txId: record.tx_id,
            source: "flipside_native_transfers",
            isSelfTransfer
          },
          transfer: {
            create: {
              counterparty: record.tx_to,
              counterpartyAccountId: counterpartyAccountId,
              amount: record.amount,
              usdValue: record.usd_value,
              currencyCode: record.token,
              description: isSelfTransfer
                ? `Self-transfer: ${record.amount.toFixed(2)} ${record.token} to ${record.tx_to.slice(0, 8)}...`
                : `Native ${record.amount.toFixed(2)} ${record.token} transfer to ${record.tx_to.slice(0, 8)}...`
            }
          }
        }
      });

      importedCount++;

      if (importedCount % 100 === 0) {
        console.log(`   Processed ${importedCount} transfers...`);
      }
    } catch (error) {
      console.error(`   Error importing transfer ${record.tx_id}:`, error);
      skippedCount++;
    }
  }, 20)

  console.log(`✅ Import completed:`);
  console.log(`   Imported: ${importedCount} transfers`);
  console.log(`   Skipped (duplicates): ${skippedCount} transfers`);
  if (!options.skipCryptoExpenseCheck) {
    console.log(`   Skipped (crypto expenses): ${cryptoExpenseSkippedCount} transfers`);
  } else {
    console.log(`   Crypto expense check: DISABLED`);
  }
}

async function parseArgs(): Promise<ImportOptions> {
  const argv = await yargs(hideBin(process.argv))
    .option("type", {
      type: "string",
      choices: ["buybacks", "floor-sweeps", "liquidity-pools", "transfers", "other"],
      default: "buybacks",
      description: "Type of crypto expense to import",
    })
    .option("from-date", {
      type: "string",
      description: "Start date (YYYY-MM-DD format)",
    })
    .option("to-date", {
      type: "string",
      description: "End date (YYYY-MM-DD format)",
    })
    .option("company-wallets", {
      type: "string",
      description: "Comma-separated list of company wallet addresses",
    })
    .option("target-mints", {
      type: "string",
      description: "Comma-separated list of target token mint addresses",
    })
    .option("collection-ids", {
      type: "string",
      description: "Comma-separated list of NFT collection IDs (for floor sweeps)",
    })
    .option("liquidity-pool-token-mints", {
      type: "string",
      description: "Comma-separated list of token mint addresses (for liquidity pools)",
    })
    .option("create-transactions", {
      type: "boolean",
      default: false,
      description: "Create transaction records in addition to crypto expense records",
    })

    .option("preview", {
      type: "boolean",
      default: false,
      description: "Preview data without importing",
    })
    .option("dry-run", {
      type: "boolean",
      default: false,
      description: "Show what would be imported without actually importing",
    })
    .option("delete-existing", {
      type: "boolean",
      default: false,
      description: "Delete existing crypto expenses before importing",
    })
    .option("non-interactive", {
      type: "boolean",
      default: false,
      description: "Skip interactive prompts and use CLI args only",
    })
    .option("skip-crypto-expense-check", {
      type: "boolean",
      default: false,
      description: "Skip checking for existing CryptoExpense records when importing transfers",
    })
    .help()
    .parseAsync();

  const options: ImportOptions = {
    type: argv.type as "buybacks" | "floor-sweeps" | "liquidity-pools" | "transfers" | "other",
    preview: argv.preview,
    dryRun: argv.dryRun,
    deleteExisting: argv.deleteExisting,
    createTransactions: argv.createTransactions,
    nonInteractive: argv.nonInteractive,
    skipCryptoExpenseCheck: argv["skip-crypto-expense-check"],
  };

  if (argv.fromDate) {
    options.fromDate = new Date(argv.fromDate);
    if (isNaN(options.fromDate.getTime())) {
      throw new Error(`Invalid from-date: ${argv.fromDate}. Use YYYY-MM-DD format.`);
    }
  }

  if (argv.toDate) {
    options.toDate = new Date(argv.toDate);
    if (isNaN(options.toDate.getTime())) {
      throw new Error(`Invalid to-date: ${argv.toDate}. Use YYYY-MM-DD format.`);
    }
  }

  if (argv.companyWallets) {
    options.companyWallets = argv.companyWallets.split(",").map((w) => w.trim());
  }

  if (argv.targetMints) {
    options.targetMints = argv.targetMints.split(",").map((m) => m.trim());
  }

  if (argv.collectionIds) {
    options.collectionIds = argv.collectionIds.split(",").map((c) => c.trim());
  }

  if (argv.liquidityPoolTokenMints) {
    options.liquidityPoolTokenMints = argv.liquidityPoolTokenMints.split(",").map((m) => m.trim());
  }

  // Validation
  if (options.createTransactions && !options.accountId) {
    throw new Error("--account-id is required when --create-transactions is specified");
  }

  return options;
}

async function previewBuybacks(options: ImportOptions) {
  console.log("🔍 Previewing buyback data from Flipside...");

  const result = await CryptoExpenseQueries.getBuybacks(options.companyWallets, options.targetMints, options.fromDate, options.toDate);

  if (result.isErr()) {
    console.error("❌ Failed to fetch buyback data:", result.error.message);
    process.exit(1);
  }

  const records = result.value;
  console.log(`\n📊 Preview Results:`);
  console.log(`   Total records: ${records.length}`);

  if (records.length > 0) {
    const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);
    const uniqueWallets = new Set(records.map((r) => r.swapper)).size;
    const dateRange = {
      from: new Date(Math.min(...records.map((r) => new Date(r.block_timestamp).getTime()))),
      to: new Date(Math.max(...records.map((r) => new Date(r.block_timestamp).getTime()))),
    };

    console.log(`   Total USD value: $${totalUsd.toLocaleString()}`);
    console.log(`   Unique wallets: ${uniqueWallets}`);
    console.log(`   Date range: ${dateRange.from.toISOString().split("T")[0]} to ${dateRange.to.toISOString().split("T")[0]}`);

    console.log(`\n📋 Sample records (first 5):`);
    records.slice(0, 5).forEach((record, index) => {
      console.log(
        `   ${index + 1}. ${record.block_timestamp} | ${record.swapper.slice(0, 8)}... | ${record.swap_from_symbol} → ${
          record.swap_to_symbol
        } | $${record.amount_usd.toLocaleString()}`
      );
    });
  }
}

async function importBuybacks(options: ImportOptions) {
  const service = new CryptoExpenseService(prisma);

  if (options.deleteExisting) {
    console.log("🗑️  Deleting existing crypto expenses...");
    const deletedCount = await service.deleteCryptoExpenses({
      type: "BUYBACK",
      fromDate: options.fromDate,
      toDate: options.toDate,
    });
    console.log(`   Deleted ${deletedCount} existing records`);
  }

  console.log("📥 Importing buyback data...");

  const result = await service.importBuybacks({
    companyWallets: options.companyWallets,
    targetMints: options.targetMints,
    fromDate: options.fromDate,
    toDate: options.toDate,
    createTransactions: options.createTransactions,
  });

  if (result.isErr()) {
    console.error("❌ Import failed:", result.error.message);
    process.exit(1);
  }

  const importResult = result.value;
  console.log(`\n✅ Import completed:`);
  console.log(`   Imported: ${importResult.imported} records`);
  console.log(`   Skipped: ${importResult.skipped} records (already exist)`);
  console.log(`   Errors: ${importResult.errors} records`);
  console.log(`   Total USD value: $${importResult.summary.totalUsd.toLocaleString()}`);

  if (importResult.summary.dateRange.from && importResult.summary.dateRange.to) {
    console.log(
      `   Date range: ${importResult.summary.dateRange.from.toISOString().split("T")[0]} to ${importResult.summary.dateRange.to.toISOString().split("T")[0]}`
    );
  }
}

async function previewFloorSweeps(options: ImportOptions) {
  console.log("🔍 Previewing floor sweep data from Flipside...");

  if (!options.collectionIds || options.collectionIds.length === 0) {
    console.error("❌ No collection IDs provided. Please specify NFT collection IDs to track.");
    process.exit(1);
  }

  const result = await CryptoExpenseQueries.getFloorSweeps(
    options.companyWallets,
    options.collectionIds,
    options.fromDate,
    options.toDate
  );

  if (result.isErr()) {
    console.error("❌ Failed to fetch floor sweep data:", result.error.message);
    process.exit(1);
  }

  const records = result.value;
  console.log(`\n📊 Preview Results:`);
  console.log(`   Total records: ${records.length}`);

  if (records.length > 0) {
    const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);
    const uniqueWallets = new Set(records.map(r => r.buyer_address)).size;
    const uniqueCollections = new Set(records.map(r => r.collection_id)).size;
    const dateRange = {
      from: new Date(Math.min(...records.map(r => new Date(r.block_timestamp).getTime()))),
      to: new Date(Math.max(...records.map(r => new Date(r.block_timestamp).getTime()))),
    };

    console.log(`   Total USD value: $${totalUsd.toLocaleString()}`);
    console.log(`   Unique wallets: ${uniqueWallets}`);
    console.log(`   Unique collections: ${uniqueCollections}`);
    console.log(`   Date range: ${dateRange.from.toISOString().split('T')[0]} to ${dateRange.to.toISOString().split('T')[0]}`);

    console.log(`\n📋 Sample records (first 5):`);
    records.slice(0, 5).forEach((record, index) => {
      const collectionName = record.collection_name || record.collection_id;
      console.log(`   ${index + 1}. ${record.block_timestamp} | ${record.buyer_address.slice(0, 8)}... | ${collectionName} | $${record.amount_usd.toLocaleString()}`);
    });
  }
}

async function importFloorSweeps(options: ImportOptions) {
  const service = new CryptoExpenseService(prisma);

  if (options.deleteExisting) {
    console.log("🗑️  Deleting existing crypto expenses...");
    const deletedCount = await service.deleteCryptoExpenses({
      type: "FLOOR_SWEEP",
      fromDate: options.fromDate,
      toDate: options.toDate,
    });
    console.log(`   Deleted ${deletedCount} existing records`);
  }

  console.log("📥 Importing floor sweep data...");

  const result = await service.importFloorSweeps({
    companyWallets: options.companyWallets,
    collectionIds: options.collectionIds,
    fromDate: options.fromDate,
    toDate: options.toDate,
    createTransactions: options.createTransactions,
    accountId: options.accountId,
  });

  if (result.isErr()) {
    console.error("❌ Import failed:", result.error.message);
    process.exit(1);
  }

  const importResult = result.value;
  console.log(`\n✅ Import completed:`);
  console.log(`   Imported: ${importResult.imported} records`);
  console.log(`   Skipped: ${importResult.skipped} records (already exist)`);
  console.log(`   Errors: ${importResult.errors} records`);
  console.log(`   Total USD value: $${importResult.summary.totalUsd.toLocaleString()}`);

  if (importResult.summary.dateRange.from && importResult.summary.dateRange.to) {
    console.log(`   Date range: ${importResult.summary.dateRange.from.toISOString().split('T')[0]} to ${importResult.summary.dateRange.to.toISOString().split('T')[0]}`);
  }
}

async function previewLiquidityPoolActions(options: ImportOptions) {
  console.log("🔍 Previewing liquidity pool action data from Flipside...");

  if (!options.liquidityPoolTokenMints || options.liquidityPoolTokenMints.length === 0) {
    console.error("❌ No token mints provided. Please specify token mint addresses to track.");
    process.exit(1);
  }

  const result = await CryptoExpenseQueries.getLiquidityPoolActions(
    options.companyWallets,
    options.liquidityPoolTokenMints,
    options.fromDate,
    options.toDate
  );

  if (result.isErr()) {
    console.error("❌ Failed to fetch liquidity pool action data:", result.error.message);
    process.exit(1);
  }

  const records = result.value;
  console.log(`\n📊 Preview Results:`);
  console.log(`   Total records: ${records.length}`);

  if (records.length > 0) {
    const totalUsd = records.reduce((sum, record) => sum + record.amount_usd, 0);
    const uniqueWallets = new Set(records.map(r => r.signer)).size;
    const uniqueActions = new Set(records.map(r => r.action)).size;
    const uniquePools = new Set(records.map(r => r.pool_address)).size;
    const dateRange = {
      from: new Date(Math.min(...records.map(r => new Date(r.block_timestamp).getTime()))),
      to: new Date(Math.max(...records.map(r => new Date(r.block_timestamp).getTime()))),
    };

    console.log(`   Total USD value: $${totalUsd.toLocaleString()}`);
    console.log(`   Unique wallets: ${uniqueWallets}`);
    console.log(`   Unique actions: ${uniqueActions}`);
    console.log(`   Unique pools: ${uniquePools}`);
    console.log(`   Date range: ${dateRange.from.toISOString().split('T')[0]} to ${dateRange.to.toISOString().split('T')[0]}`);

    console.log(`\n📋 Sample records (first 5):`);
    records.slice(0, 5).forEach((record, index) => {
      const tokenPair = `${record.token_a_symbol}/${record.token_b_symbol}`;
      console.log(`   ${index + 1}. ${record.block_timestamp} | ${record.signer.slice(0, 8)}... | ${record.action} | ${tokenPair} | $${record.amount_usd.toLocaleString()}`);
    });
  }
}

async function importLiquidityPoolActions(options: ImportOptions) {
  const service = new CryptoExpenseService(prisma);

  if (options.deleteExisting) {
    console.log("🗑️  Deleting existing crypto expenses...");
    const deletedCount = await service.deleteCryptoExpenses({
      type: "LIQUIDITY_POOL",
      fromDate: options.fromDate,
      toDate: options.toDate,
    });
    console.log(`   Deleted ${deletedCount} existing records`);
  }

  console.log("📥 Importing liquidity pool action data...");

  const result = await service.importLiquidityPoolActions({
    companyWallets: options.companyWallets,
    liquidityPoolTokenMints: options.liquidityPoolTokenMints,
    fromDate: options.fromDate,
    toDate: options.toDate,
    createTransactions: options.createTransactions,
    accountId: options.accountId,
  });

  if (result.isErr()) {
    console.error("❌ Import failed:", result.error.message);
    process.exit(1);
  }

  const importResult = result.value;
  console.log(`\n✅ Import completed:`);
  console.log(`   Imported: ${importResult.imported} records`);
  console.log(`   Skipped: ${importResult.skipped} records (already exist)`);
  console.log(`   Errors: ${importResult.errors} records`);
  console.log(`   Total USD value: $${importResult.summary.totalUsd.toLocaleString()}`);

  if (importResult.summary.dateRange.from && importResult.summary.dateRange.to) {
    console.log(`   Date range: ${importResult.summary.dateRange.from.toISOString().split('T')[0]} to ${importResult.summary.dateRange.to.toISOString().split('T')[0]}`);
  }
}

async function main() {
  try {
    console.log("🚀 Crypto Expense Import Script");
    console.log("=====================================\n");

    let options = await parseArgs();

    // Interactive mode if not explicitly disabled
    if (!options.nonInteractive) {
      console.log("🔄 Running in interactive mode...");
      console.log("   Use --non-interactive flag to skip prompts");
      console.log("   Press Ctrl+C to exit at any time\n");

      try {
        // Fetch wallets from database
        const wallets = await fetchWalletAccounts();

        if (wallets.length === 0) {
          console.log("❌ No wallet accounts found in database.");
          console.log("   Please create wallet accounts with public keys first.");
          process.exit(1);
        }

        // Interactive wallet selection
        const selectedWallets = await interactiveWalletSelection(wallets);
        if (selectedWallets.length === 0) {
          console.log("❌ No wallets selected. Exiting.");
          process.exit(1);
        }

        // Interactive configuration
        const interactiveConfig = await interactiveConfiguration();

        // Merge interactive config with CLI options
        options = {
          ...options,
          ...interactiveConfig,
          companyWallets: selectedWallets,
        };

        console.log("\n📋 Final Configuration:");
        console.log(`   Type: ${options.type}`);
        console.log(`   From: ${options.fromDate?.toISOString().split("T")[0] || "all time"}`);
        console.log(`   To: ${options.toDate?.toISOString().split("T")[0] || "now"}`);
        console.log(`   Wallets: ${options.companyWallets?.length || 0} selected`);
        console.log(`   Target mints: ${options.targetMints?.length || "default"} mints`);
        console.log(`   Create transactions: ${options.createTransactions}`);
        console.log(`   Account ID: ${options.accountId || "none"}`);
        console.log(`   Mode: ${options.preview ? "preview" : "import"}`);
        console.log("");

        // Confirmation prompt
        const { confirm } = await inquirer.prompt([
          {
            type: "confirm",
            name: "confirm",
            message: "Proceed with these settings?",
            default: true,
          },
        ]);

        if (!confirm) {
          console.log("❌ Operation cancelled by user.");
          process.exit(0);
        }
      } catch (error) {
        if (error instanceof Error && error.message.includes("User force closed")) {
          console.log("\n❌ Operation cancelled by user (Ctrl+C).");
          process.exit(0);
        }
        throw error;
      }
    } else {
      // Non-interactive mode - show configuration
      console.log("⚡ Running in non-interactive mode\n");
      console.log("📋 Configuration:");
      console.log(`   Type: ${options.type}`);
      console.log(`   From: ${options.fromDate?.toISOString().split("T")[0] || "all time"}`);
      console.log(`   To: ${options.toDate?.toISOString().split("T")[0] || "now"}`);
      console.log(`   Company wallets: ${options.companyWallets?.length || "default"} wallets`);
      console.log(`   Target mints: ${options.targetMints?.length || "default"} mints`);
      console.log(`   Create transactions: ${options.createTransactions}`);
      console.log(`   Account ID: ${options.accountId || "none"}`);
      console.log("");
    }

    if (options.type === "buybacks") {
      if (options.preview || options.dryRun) {
        await previewBuybacks(options);
      } else {
        await importBuybacks(options);
      }
    } else if (options.type === "floor-sweeps") {
      if (options.preview || options.dryRun) {
        await previewFloorSweeps(options);
      } else {
        await importFloorSweeps(options);
      }
    } else if (options.type === "liquidity-pools") {
      if (options.preview || options.dryRun) {
        await previewLiquidityPoolActions(options);
      } else {
        await importLiquidityPoolActions(options);
      }
    } else if (options.type === "transfers") {
      if (options.preview || options.dryRun) {
        await previewTransfers(options);
      } else {
        await importTransfers(options);
      }
    } else {
      console.log("❌ Other crypto expense types not yet implemented");
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ Script failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
