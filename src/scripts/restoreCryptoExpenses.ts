#!/usr/bin/env tsx

/**
 * <PERSON><PERSON> script for crypto expenses after refactoring
 * This script restores crypto expense data to the new table structure
 */

import { PrismaClient } from "@/prisma/generated";
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function restoreCryptoExpenses() {
  console.log("🔄 Restoring crypto expenses to new table structure...\n");

  try {
    // Find the most recent backup file
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      throw new Error("Backup directory not found");
    }

    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.startsWith('crypto-expenses-backup-') && file.endsWith('.json'))
      .sort()
      .reverse();

    if (backupFiles.length === 0) {
      throw new Error("No backup files found");
    }

    const latestBackup = backupFiles[0];
    const backupFile = path.join(backupDir, latestBackup);
    
    console.log(`📁 Using backup file: ${latestBackup}`);

    // Read backup data
    const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf8'));
    console.log(`Found ${backupData.length} records to restore`);

    let restored = 0;
    let errors = 0;

    // Process records in transaction
    await prisma.$transaction(
      async (prisma) => {
        for (const oldExpense of backupData) {
          try {
            // Create the new base crypto expense record
            const newCryptoExpense = await prisma.cryptoExpense.create({
              data: {
                type: oldExpense.type,
                txId: oldExpense.txId,
                blockTimestamp: new Date(oldExpense.blockTimestamp),
                swapper: oldExpense.swapper,
                amountUsd: oldExpense.amountUsd || oldExpense.swapFromAmountUsd || 0,
                companyWalletName: oldExpense.companyWalletName,
                accountingPeriod: oldExpense.accountingPeriod,
                rawFlipsideData: oldExpense.rawFlipsideData,
                transactionId: oldExpense.transactionId,
              },
            });

            // Create type-specific records based on expense type
            if (oldExpense.type === "BUYBACK") {
              // Extract data from rawFlipsideData if direct fields are empty
              const rawData = oldExpense.rawFlipsideData || {};

              await prisma.cryptoBuyback.create({
                data: {
                  cryptoExpenseId: newCryptoExpense.id,
                  swapFromSymbol: oldExpense.swapFromSymbol || rawData.swap_from_symbol || "",
                  swapToSymbol: oldExpense.swapToSymbol || rawData.swap_to_symbol || "",
                  swapFromAmount: oldExpense.swapFromAmount || rawData.swap_from_amount || 0,
                  swapToAmount: oldExpense.swapToAmount || rawData.swap_to_amount || 0,
                  swapFromAmountUsd: oldExpense.swapFromAmountUsd || rawData.swap_from_amount_usd || 0,
                  swapToAmountUsd: oldExpense.swapToAmountUsd || rawData.swap_to_amount_usd || 0,
                  swapFromMint: oldExpense.swapFromMint || rawData.swap_from_mint,
                  swapToMint: oldExpense.swapToMint || rawData.swap_to_mint,
                },
              });
            } else if (oldExpense.type === "FLOOR_SWEEP") {
              await prisma.cryptoFloorSweep.create({
                data: {
                  cryptoExpenseId: newCryptoExpense.id,
                  collectionId: oldExpense.nftCollectionId || "",
                  collectionName: oldExpense.nftCollectionName,
                  tokenId: oldExpense.nftTokenId,
                  mintAddress: oldExpense.nftMintAddress,
                  priceUsd: oldExpense.priceUsd || oldExpense.amountUsd || 0,
                },
              });
            } else if (oldExpense.type === "LIQUIDITY_POOL") {
              await prisma.cryptoLiquidityPool.create({
                data: {
                  cryptoExpenseId: newCryptoExpense.id,
                  action: oldExpense.lpAction || "",
                  poolAddress: oldExpense.lpPoolAddress || "",
                  tokenAMint: oldExpense.lpTokenAMint || "",
                  tokenBMint: oldExpense.lpTokenBMint || "",
                  tokenASymbol: oldExpense.lpTokenASymbol || "",
                  tokenBSymbol: oldExpense.lpTokenBSymbol || "",
                  tokenAAmount: oldExpense.lpTokenAAmount || 0,
                  tokenBAmount: oldExpense.lpTokenBAmount || 0,
                  tokenAAmountUsd: oldExpense.lpTokenAAmountUsd || 0,
                  tokenBAmountUsd: oldExpense.lpTokenBAmountUsd || 0,
                },
              });
            }

            restored++;
            
            if (restored % 5 === 0) {
              console.log(`   Restored ${restored}/${backupData.length} records...`);
            }
          } catch (error) {
            console.error(`Failed to restore record ${oldExpense.id}:`, error);
            errors++;
          }
        }
      },
      { timeout: 300000 } // 5 minute timeout
    );

    console.log(`\n✅ Restore completed:`);
    console.log(`   Restored: ${restored} records`);
    console.log(`   Errors: ${errors} records`);

    if (errors === 0) {
      console.log("\n🎉 All crypto expenses successfully restored to new table structure!");
    } else {
      console.log(`\n⚠️  ${errors} records failed to restore. Please check the logs above.`);
    }

  } catch (error) {
    console.error("❌ Restore failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function verifyRestore() {
  console.log("\n🔍 Verifying restore...");

  try {
    const totalCryptoExpenses = await prisma.cryptoExpense.count();
    const buybackCount = await prisma.cryptoBuyback.count();
    const floorSweepCount = await prisma.cryptoFloorSweep.count();
    const liquidityPoolCount = await prisma.cryptoLiquidityPool.count();

    console.log(`   Total crypto expenses: ${totalCryptoExpenses}`);
    console.log(`   Buyback records: ${buybackCount}`);
    console.log(`   Floor sweep records: ${floorSweepCount}`);
    console.log(`   Liquidity pool records: ${liquidityPoolCount}`);

    // Verify data integrity
    const expensesByType = await prisma.cryptoExpense.groupBy({
      by: ['type'],
      _count: { type: true },
    });

    console.log("\n📊 Expenses by type:");
    expensesByType.forEach(group => {
      console.log(`   ${group.type}: ${group._count.type}`);
    });

    // Sample verification
    const sampleExpenses = await prisma.cryptoExpense.findMany({
      take: 3,
      include: {
        buyback: true,
        floorSweep: true,
        liquidityPool: true,
      },
    });

    if (sampleExpenses.length > 0) {
      console.log("\n📋 Sample restored records:");
      sampleExpenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. Type: ${expense.type}, Amount: $${expense.amountUsd}`);
        if (expense.buyback) {
          console.log(`      Buyback: ${expense.buyback.swapFromSymbol} → ${expense.buyback.swapToSymbol}`);
        }
        if (expense.floorSweep) {
          console.log(`      Floor Sweep: ${expense.floorSweep.collectionName || expense.floorSweep.collectionId}`);
        }
        if (expense.liquidityPool) {
          console.log(`      LP Action: ${expense.liquidityPool.action} (${expense.liquidityPool.tokenASymbol}/${expense.liquidityPool.tokenBSymbol})`);
        }
      });
    }

    console.log("\n✅ Restore verification completed");

  } catch (error) {
    console.error("❌ Verification failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  try {
    await restoreCryptoExpenses();
    await verifyRestore();
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
