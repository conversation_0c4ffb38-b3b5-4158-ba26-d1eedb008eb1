#!/usr/bin/env tsx

/**
 * Migration script to refactor crypto expenses to use separate tables
 * This script migrates existing crypto expenses to the new table structure
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function migrateCryptoExpenseRefactor() {
  console.log("🔄 Migrating crypto expenses to new table structure...\n");

  try {
    // Get all existing crypto expenses
    const existingExpenses = await prisma.cryptoExpense.findMany({
      select: {
        id: true,
        type: true,
        txId: true,
        blockTimestamp: true,
        swapper: true,
        amountUsd: true,
        companyWalletName: true,
        accountingPeriod: true,
        rawFlipsideData: true,
        transactionId: true,
        // Old fields that will be moved to separate tables
        swapFromSymbol: true,
        swapToSymbol: true,
        swapFromAmount: true,
        swapToAmount: true,
        swapFromAmountUsd: true,
        swapToAmountUsd: true,
        swapFromMint: true,
        swapToMint: true,
        nftCollectionId: true,
        nftCollectionName: true,
        nftTokenId: true,
        nftMintAddress: true,
        priceUsd: true,
        lpAction: true,
        lpPoolAddress: true,
        lpTokenAMint: true,
        lpTokenBMint: true,
        lpTokenASymbol: true,
        lpTokenBSymbol: true,
        lpTokenAAmount: true,
        lpTokenBAmount: true,
        lpTokenAAmountUsd: true,
        lpTokenBAmountUsd: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log(`Found ${existingExpenses.length} existing crypto expenses to migrate`);

    if (existingExpenses.length === 0) {
      console.log("✅ No migration needed - no existing crypto expenses found");
      return;
    }

    let migrated = 0;
    let errors = 0;

    // Process records in transaction
    await prisma.$transaction(
      async (prisma) => {
        for (const expense of existingExpenses) {
          try {
            // Create the new base crypto expense record
            const newCryptoExpense = await prisma.cryptoExpense.create({
              data: {
                type: expense.type,
                txId: `migrated_${expense.txId}`, // Temporary unique txId
                blockTimestamp: expense.blockTimestamp,
                swapper: expense.swapper,
                amountUsd: expense.amountUsd || 0,
                companyWalletName: expense.companyWalletName,
                accountingPeriod: expense.accountingPeriod,
                rawFlipsideData: expense.rawFlipsideData,
                transactionId: expense.transactionId,
              },
            });

            // Create type-specific records based on expense type
            if (expense.type === "BUYBACK" && expense.swapFromSymbol && expense.swapToSymbol) {
              await prisma.cryptoBuyback.create({
                data: {
                  cryptoExpenseId: newCryptoExpense.id,
                  swapFromSymbol: expense.swapFromSymbol,
                  swapToSymbol: expense.swapToSymbol,
                  swapFromAmount: expense.swapFromAmount || 0,
                  swapToAmount: expense.swapToAmount || 0,
                  swapFromAmountUsd: expense.swapFromAmountUsd || 0,
                  swapToAmountUsd: expense.swapToAmountUsd || 0,
                  swapFromMint: expense.swapFromMint,
                  swapToMint: expense.swapToMint,
                },
              });
            } else if (expense.type === "FLOOR_SWEEP" && expense.nftCollectionId) {
              await prisma.cryptoFloorSweep.create({
                data: {
                  cryptoExpenseId: newCryptoExpense.id,
                  collectionId: expense.nftCollectionId,
                  collectionName: expense.nftCollectionName,
                  tokenId: expense.nftTokenId,
                  mintAddress: expense.nftMintAddress,
                  priceUsd: expense.priceUsd || 0,
                },
              });
            } else if (expense.type === "LIQUIDITY_POOL" && expense.lpAction) {
              await prisma.cryptoLiquidityPool.create({
                data: {
                  cryptoExpenseId: newCryptoExpense.id,
                  action: expense.lpAction,
                  poolAddress: expense.lpPoolAddress || "",
                  tokenAMint: expense.lpTokenAMint || "",
                  tokenBMint: expense.lpTokenBMint || "",
                  tokenASymbol: expense.lpTokenASymbol || "",
                  tokenBSymbol: expense.lpTokenBSymbol || "",
                  tokenAAmount: expense.lpTokenAAmount || 0,
                  tokenBAmount: expense.lpTokenBAmount || 0,
                  tokenAAmountUsd: expense.lpTokenAAmountUsd || 0,
                  tokenBAmountUsd: expense.lpTokenBAmountUsd || 0,
                },
              });
            }

            // Update the new record with the original txId
            await prisma.cryptoExpense.update({
              where: { id: newCryptoExpense.id },
              data: { txId: expense.txId },
            });

            // Delete the old record
            await prisma.cryptoExpense.delete({
              where: { id: expense.id },
            });

            migrated++;
            
            if (migrated % 10 === 0) {
              console.log(`   Migrated ${migrated}/${existingExpenses.length} records...`);
            }
          } catch (error) {
            console.error(`Failed to migrate record ${expense.id}:`, error);
            errors++;
          }
        }
      },
      { timeout: 600000 } // 10 minute timeout
    );

    console.log(`\n✅ Migration completed:`);
    console.log(`   Migrated: ${migrated} records`);
    console.log(`   Errors: ${errors} records`);

    if (errors === 0) {
      console.log("\n🎉 All crypto expenses successfully migrated to new table structure!");
    } else {
      console.log(`\n⚠️  ${errors} records failed to migrate. Please check the logs above.`);
    }

  } catch (error) {
    console.error("❌ Migration failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function verifyMigration() {
  console.log("\n🔍 Verifying migration...");

  try {
    const totalCryptoExpenses = await prisma.cryptoExpense.count();
    const buybackCount = await prisma.cryptoBuyback.count();
    const floorSweepCount = await prisma.cryptoFloorSweep.count();
    const liquidityPoolCount = await prisma.cryptoLiquidityPool.count();

    console.log(`   Total crypto expenses: ${totalCryptoExpenses}`);
    console.log(`   Buyback records: ${buybackCount}`);
    console.log(`   Floor sweep records: ${floorSweepCount}`);
    console.log(`   Liquidity pool records: ${liquidityPoolCount}`);

    // Verify data integrity
    const expensesByType = await prisma.cryptoExpense.groupBy({
      by: ['type'],
      _count: { type: true },
    });

    console.log("\n📊 Expenses by type:");
    expensesByType.forEach(group => {
      console.log(`   ${group.type}: ${group._count.type}`);
    });

    // Sample verification
    const sampleExpenses = await prisma.cryptoExpense.findMany({
      take: 3,
      include: {
        buyback: true,
        floorSweep: true,
        liquidityPool: true,
      },
    });

    if (sampleExpenses.length > 0) {
      console.log("\n📋 Sample migrated records:");
      sampleExpenses.forEach((expense, index) => {
        console.log(`   ${index + 1}. Type: ${expense.type}, Amount: $${expense.amountUsd}`);
        if (expense.buyback) {
          console.log(`      Buyback: ${expense.buyback.swapFromSymbol} → ${expense.buyback.swapToSymbol}`);
        }
        if (expense.floorSweep) {
          console.log(`      Floor Sweep: ${expense.floorSweep.collectionName || expense.floorSweep.collectionId}`);
        }
        if (expense.liquidityPool) {
          console.log(`      LP Action: ${expense.liquidityPool.action} (${expense.liquidityPool.tokenASymbol}/${expense.liquidityPool.tokenBSymbol})`);
        }
      });
    }

    console.log("\n✅ Migration verification completed");

  } catch (error) {
    console.error("❌ Verification failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  try {
    await migrateCryptoExpenseRefactor();
    await verifyMigration();
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
