#!/usr/bin/env tsx

/**
 * Test script for account public key functionality
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function testAccountWithPublicKey() {
  console.log("🧪 Testing Account with Public Key functionality...\n");

  try {
    // Test 1: Create a wallet account with public key
    console.log("1. Creating wallet account with public key...");
    const walletAccount = await prisma.account.create({
      data: {
        name: "Test Solana Wallet",
        type: "WALLET",
        publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj",
      },
    });
    console.log("✅ Wallet account created:", {
      id: walletAccount.id,
      name: walletAccount.name,
      type: walletAccount.type,
      publicKey: walletAccount.publicKey,
    });

    // Test 2: Create a bank account without public key
    console.log("\n2. Creating bank account without public key...");
    const bankAccount = await prisma.account.create({
      data: {
        name: "Test Bank Account",
        type: "BANK_ACCOUNT",
        // publicKey is optional for non-wallet accounts
      },
    });
    console.log("✅ Bank account created:", {
      id: bankAccount.id,
      name: bankAccount.name,
      type: bankAccount.type,
      publicKey: bankAccount.publicKey,
    });

    // Test 3: Try to create duplicate public key (should fail)
    console.log("\n3. Testing duplicate public key validation...");
    try {
      await prisma.account.create({
        data: {
          name: "Duplicate Wallet",
          type: "WALLET",
          publicKey: "7ScYHk4VDgSRnQngAUtQk4Eyf7fGat8P4wXq6e2dkzLj", // Same as first wallet
        },
      });
      console.log("❌ Should have failed with duplicate public key");
    } catch (error) {
      console.log("✅ Correctly prevented duplicate public key");
    }

    // Test 4: Update account public key
    console.log("\n4. Testing account update with new public key...");
    const updatedAccount = await prisma.account.update({
      where: { id: walletAccount.id },
      data: {
        publicKey: "G9tt98aYSznRk7jWsfuz9FnTdokxS6Brohdo9hSmjTRB",
      },
    });
    console.log("✅ Account updated with new public key:", {
      id: updatedAccount.id,
      publicKey: updatedAccount.publicKey,
    });

    // Test 5: Query accounts by type
    console.log("\n5. Querying wallet accounts...");
    const walletAccounts = await prisma.account.findMany({
      where: { type: "WALLET" },
      select: {
        id: true,
        name: true,
        type: true,
        publicKey: true,
      },
    });
    console.log("✅ Found wallet accounts:", walletAccounts);

    // Test 6: Query accounts with public keys
    console.log("\n6. Querying accounts with public keys...");
    const accountsWithPublicKeys = await prisma.account.findMany({
      where: { 
        publicKey: { not: null },
      },
      select: {
        id: true,
        name: true,
        type: true,
        publicKey: true,
      },
    });
    console.log("✅ Found accounts with public keys:", accountsWithPublicKeys);

    // Cleanup
    console.log("\n7. Cleaning up test data...");
    await prisma.account.deleteMany({
      where: {
        id: {
          in: [walletAccount.id, bankAccount.id],
        },
      },
    });
    console.log("✅ Test data cleaned up");

    console.log("\n🎉 All tests passed! Account public key functionality is working correctly.");

  } catch (error) {
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  try {
    await testAccountWithPublicKey();
  } catch (error) {
    console.error("❌ Test suite failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
