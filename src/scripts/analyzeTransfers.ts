#!/usr/bin/env tsx

/**
 * Transfer Analysis CLI Script
 * 
 * This script provides command-line tools for analyzing transfer data
 * and generating counterparty reports.
 */

import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { PrismaClient } from "@/prisma/generated";
import { TransferAnalytics } from "@/modules/transfers";
import fs from "fs";
import path from "path";

const prisma = new PrismaClient();

interface AnalysisOptions {
  wallets?: string[];
  fromDate?: Date;
  toDate?: Date;
  minTransfers?: number;
  tokens?: string[];
  limit?: number;
  output?: string;
  format: "console" | "csv" | "json";
}

async function analyzeCounterparties(options: AnalysisOptions) {
  console.log("🔍 Analyzing counterparty transfer patterns...");
  
  const analytics = new TransferAnalytics(prisma);
  
  const results = await analytics.getCounterpartyAggregation({
    walletAddresses: options.wallets,
    fromDate: options.fromDate,
    toDate: options.toDate,
    minTransferCount: options.minTransfers || 1,
    tokens: options.tokens
  });

  const limitedResults = options.limit ? results.slice(0, options.limit) : results;

  console.log(`📊 Found ${results.length} counterparties (showing ${limitedResults.length})`);

  if (options.format === "console") {
    console.log("\n🏢 Counterparty Analysis:");
    console.log("=" .repeat(80));
    
    limitedResults.forEach((cp, index) => {
      console.log(`\n${index + 1}. ${cp.counterparty}`);
      console.log(`   Total Transfers: ${cp.totalTransfers}`);
      console.log(`   Type: ${cp.isSelfTransfer ? 'Self-Transfer' : 'External Transfer'}`);
      if (cp.isSelfTransfer && cp.counterpartyAccountName) {
        console.log(`   Account Name: ${cp.counterpartyAccountName}`);
      }
      console.log(`   Active Period: ${cp.firstTransferDate.toISOString().split('T')[0]} to ${cp.lastTransferDate.toISOString().split('T')[0]}`);
      console.log("   Token Breakdown:");

      Object.entries(cp.tokens).forEach(([token, data]) => {
        console.log(`     ${token}: ${data.count} transfers, ${data.totalAmount} total (avg: ${data.averageAmount})`);
      });
    });
  } else if (options.format === "csv") {
    const csvData = await analytics.exportCounterpartyDataToCsv({
      walletAddresses: options.wallets,
      fromDate: options.fromDate,
      toDate: options.toDate,
      minTransferCount: options.minTransfers || 1,
      tokens: options.tokens
    });

    if (options.output) {
      fs.writeFileSync(options.output, csvData);
      console.log(`✅ CSV data exported to ${options.output}`);
    } else {
      console.log("\n📄 CSV Data:");
      console.log(csvData);
    }
  } else if (options.format === "json") {
    const jsonData = JSON.stringify(limitedResults, null, 2);
    
    if (options.output) {
      fs.writeFileSync(options.output, jsonData);
      console.log(`✅ JSON data exported to ${options.output}`);
    } else {
      console.log("\n📄 JSON Data:");
      console.log(jsonData);
    }
  }
}

async function analyzeOverallStats(options: AnalysisOptions) {
  console.log("📈 Generating overall transfer statistics...");
  
  const analytics = new TransferAnalytics(prisma);
  
  const stats = await analytics.getOverallTransferStats({
    walletAddresses: options.wallets,
    fromDate: options.fromDate,
    toDate: options.toDate,
    tokens: options.tokens
  });

  console.log("\n📊 Overall Transfer Statistics:");
  console.log("=" .repeat(50));
  console.log(`Total Transfers: ${stats.totalTransfers}`);
  console.log(`Unique Counterparties: ${stats.uniqueCounterparties}`);
  
  if (stats.dateRange.from && stats.dateRange.to) {
    console.log(`Date Range: ${stats.dateRange.from.toISOString().split('T')[0]} to ${stats.dateRange.to.toISOString().split('T')[0]}`);
  }

  console.log("\n💰 Token Breakdown:");
  Object.entries(stats.tokenBreakdown).forEach(([token, data]) => {
    console.log(`  ${token}:`);
    console.log(`    Transfers: ${data.count}`);
    console.log(`    Total Amount: ${data.totalAmount}`);
    console.log(`    Unique Counterparties: ${data.uniqueCounterparties}`);
  });
}

async function analyzeTopRecipients(options: AnalysisOptions) {
  console.log("🏆 Finding top recipients by token...");

  const analytics = new TransferAnalytics(prisma);
  const tokens = options.tokens || ['USDC', 'USDT', 'SOL'];

  for (const token of tokens) {
    console.log(`\n💎 Top ${token} Recipients:`);
    console.log("-" .repeat(40));

    const topRecipients = await analytics.getTopCounterpartiesByTokenAmount(
      token,
      options.limit || 10,
      {
        walletAddresses: options.wallets,
        fromDate: options.fromDate,
        toDate: options.toDate
      }
    );

    topRecipients.forEach((cp, index) => {
      const tokenData = cp.tokens[token];
      if (tokenData) {
        console.log(`${index + 1}. ${cp.counterparty} ${cp.isSelfTransfer ? '(Self)' : ''}`);
        console.log(`   Amount: ${tokenData.totalAmount} ${token}`);
        console.log(`   Transfers: ${tokenData.count}`);
        console.log(`   Average: ${tokenData.averageAmount} ${token}`);
        if (cp.isSelfTransfer && cp.counterpartyAccountName) {
          console.log(`   Account: ${cp.counterpartyAccountName}`);
        }
      }
    });
  }
}

async function analyzeTransferTypes(options: AnalysisOptions) {
  console.log("🔄 Analyzing transfer types (self vs external)...");

  const analytics = new TransferAnalytics(prisma);

  const breakdown = await analytics.getTransferTypeBreakdown({
    walletAddresses: options.wallets,
    fromDate: options.fromDate,
    toDate: options.toDate,
    tokens: options.tokens
  });

  console.log("\n📊 Transfer Type Breakdown:");
  console.log("=" .repeat(50));

  console.log("\n🔄 Self-Transfers (between our own accounts):");
  console.log(`   Total Transfers: ${breakdown.selfTransfers.count}`);
  console.log(`   Unique Accounts: ${breakdown.selfTransfers.counterparties}`);
  console.log("   Token Breakdown:");
  Object.entries(breakdown.selfTransfers.totalAmount).forEach(([token, amount]) => {
    console.log(`     ${token}: ${amount}`);
  });

  console.log("\n🌐 External Transfers (to external counterparties):");
  console.log(`   Total Transfers: ${breakdown.externalTransfers.count}`);
  console.log(`   Unique Counterparties: ${breakdown.externalTransfers.counterparties}`);
  console.log("   Token Breakdown:");
  Object.entries(breakdown.externalTransfers.totalAmount).forEach(([token, amount]) => {
    console.log(`     ${token}: ${amount}`);
  });

  // Show self-transfers in detail
  console.log("\n🔄 Self-Transfer Details:");
  const selfTransfers = await analytics.getSelfTransfers({
    walletAddresses: options.wallets,
    fromDate: options.fromDate,
    toDate: options.toDate,
    tokens: options.tokens
  });

  if (selfTransfers.length > 0) {
    selfTransfers.slice(0, options.limit || 10).forEach((cp, index) => {
      console.log(`\n${index + 1}. ${cp.counterpartyAccountName || cp.counterparty}`);
      console.log(`   Address: ${cp.counterparty}`);
      console.log(`   Transfers: ${cp.totalTransfers}`);
      Object.entries(cp.tokens).forEach(([token, data]) => {
        console.log(`   ${token}: ${data.totalAmount} in ${data.count} transfers`);
      });
    });
  } else {
    console.log("   No self-transfers found");
  }
}

async function main() {
  const argv = await yargs(hideBin(process.argv))
    .command(
      "counterparties",
      "Analyze counterparty transfer patterns",
      (yargs) => yargs,
      (argv) => analyzeCounterparties(argv as any)
    )
    .command(
      "stats",
      "Generate overall transfer statistics", 
      (yargs) => yargs,
      (argv) => analyzeOverallStats(argv as any)
    )
    .command(
      "top-recipients",
      "Find top recipients by token",
      (yargs) => yargs,
      (argv) => analyzeTopRecipients(argv as any)
    )
    .command(
      "transfer-types",
      "Analyze self-transfers vs external transfers",
      (yargs) => yargs,
      (argv) => analyzeTransferTypes(argv as any)
    )
    .option("wallets", {
      type: "string",
      description: "Comma-separated list of wallet addresses to analyze",
    })
    .option("from-date", {
      type: "string",
      description: "Start date (YYYY-MM-DD format)",
    })
    .option("to-date", {
      type: "string", 
      description: "End date (YYYY-MM-DD format)",
    })
    .option("min-transfers", {
      type: "number",
      default: 1,
      description: "Minimum number of transfers to include counterparty",
    })
    .option("tokens", {
      type: "string",
      description: "Comma-separated list of tokens to analyze (USDC,USDT,SOL)",
    })
    .option("limit", {
      type: "number",
      default: 10,
      description: "Maximum number of results to show",
    })
    .option("output", {
      type: "string",
      description: "Output file path (for CSV/JSON formats)",
    })
    .option("format", {
      type: "string",
      choices: ["console", "csv", "json"],
      default: "console",
      description: "Output format",
    })
    .demandCommand(1, "Please specify a command")
    .help()
    .parse();

  // Parse options
  const options: AnalysisOptions = {
    wallets: argv.wallets ? argv.wallets.split(",").map(w => w.trim()) : undefined,
    fromDate: argv["from-date"] ? new Date(argv["from-date"]) : undefined,
    toDate: argv["to-date"] ? new Date(argv["to-date"]) : undefined,
    minTransfers: argv["min-transfers"],
    tokens: argv.tokens ? argv.tokens.split(",").map(t => t.trim()) : undefined,
    limit: argv.limit,
    output: argv.output,
    format: argv.format as "console" | "csv" | "json"
  };

  try {
    // Command is handled by yargs command handlers
  } catch (error) {
    console.error("❌ Analysis failed:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
}
