#!/usr/bin/env tsx

/**
 * Test script to verify crypto expense display in transactions table
 * Creates test crypto expenses and transactions to verify the UI shows amounts correctly
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function createTestCryptoExpenseTransaction() {
  console.log("🧪 Testing Crypto Expense Display in Transactions Table");
  console.log("======================================================\n");

  try {
    // First, create a test wallet account
    console.log("1. Creating test wallet account...");
    const testWallet = await prisma.account.create({
      data: {
        name: "Test Crypto Wallet",
        type: "WALLET",
        publicKey: "TestWallet123456789012345678901234567890",
      },
    });
    console.log(`✅ Created wallet account: ${testWallet.name} (${testWallet.id})`);

    // Create a test transaction
    console.log("\n2. Creating test transaction...");
    const testTransaction = await prisma.transaction.create({
      data: {
        executedAt: new Date(),
        accountId: testWallet.id,
        metadata: {
          source: "test-crypto-expense-display",
        },
      },
    });
    console.log(`✅ Created transaction: ${testTransaction.id}`);

    // Create a test crypto expense linked to the transaction
    console.log("\n3. Creating test crypto expense...");
    const testCryptoExpense = await prisma.cryptoExpense.create({
      data: {
        type: "BUYBACK",
        txId: "TestTx123456789012345678901234567890123456789012345",
        blockTimestamp: new Date(),
        swapper: testWallet.publicKey!,
        swapFromSymbol: "USDC",
        swapToSymbol: "TOKEN",
        swapFromAmount: 1000.50,
        swapToAmount: 500000.25,
        swapFromAmountUsd: 1000.50, // This should show as €1,000.50 in the table
        swapToAmountUsd: 1000.50,
        transactionId: testTransaction.id,
        rawFlipsideData: {
          test: true,
          source: "test-script",
        },
      },
    });
    console.log(`✅ Created crypto expense: ${testCryptoExpense.id}`);
    console.log(`   Type: ${testCryptoExpense.type}`);
    console.log(`   Amount USD: $${testCryptoExpense.swapFromAmountUsd}`);
    console.log(`   Swap: ${testCryptoExpense.swapFromSymbol} → ${testCryptoExpense.swapToSymbol}`);

    // Verify the data can be queried correctly
    console.log("\n4. Verifying transaction with crypto expense...");
    const transactionWithCryptoExpense = await prisma.transaction.findUnique({
      where: { id: testTransaction.id },
      include: {
        cryptoExpense: {
          select: {
            id: true,
            type: true,
            txId: true,
            swapFromSymbol: true,
            swapToSymbol: true,
            swapFromAmount: true,
            swapToAmount: true,
            swapFromAmountUsd: true,
            swapToAmountUsd: true,
            blockTimestamp: true,
          },
        },
        account: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });

    if (transactionWithCryptoExpense?.cryptoExpense) {
      console.log("✅ Transaction query successful");
      console.log(`   Transaction ID: ${transactionWithCryptoExpense.id}`);
      console.log(`   Account: ${transactionWithCryptoExpense.account.name}`);
      console.log(`   Crypto Expense Type: ${transactionWithCryptoExpense.cryptoExpense.type}`);
      console.log(`   Amount to display: €${transactionWithCryptoExpense.cryptoExpense.swapFromAmountUsd}`);
      console.log(`   Description: ${transactionWithCryptoExpense.cryptoExpense.type}: ${transactionWithCryptoExpense.cryptoExpense.swapFromSymbol} → ${transactionWithCryptoExpense.cryptoExpense.swapToSymbol}`);
    } else {
      console.log("❌ Failed to query transaction with crypto expense");
    }

    console.log("\n5. Testing transaction stats...");
    const stats = await prisma.transaction.count({
      where: {
        cryptoExpense: {
          isNot: null,
        },
      },
    });
    console.log(`✅ Transactions with crypto expenses: ${stats}`);

    console.log("\n📋 Test Results Summary:");
    console.log("========================");
    console.log(`✅ Wallet Account Created: ${testWallet.name}`);
    console.log(`✅ Transaction Created: ${testTransaction.id.slice(0, 8)}...`);
    console.log(`✅ Crypto Expense Created: ${testCryptoExpense.type}`);
    console.log(`✅ Amount to Display: €${testCryptoExpense.swapFromAmountUsd} (from USD amount)`);
    console.log(`✅ Transaction Type: Crypto Expense`);
    console.log(`✅ Description: ${testCryptoExpense.type}: ${testCryptoExpense.swapFromSymbol} → ${testCryptoExpense.swapToSymbol}`);

    console.log("\n🎯 Frontend Testing Instructions:");
    console.log("=================================");
    console.log("1. Open the transactions page in your browser");
    console.log("2. Look for the test transaction created above");
    console.log("3. Verify the following displays correctly:");
    console.log(`   • Type: "Crypto Expense" badge (purple)`);
    console.log(`   • Amount: "€1,000.50" (formatted as EUR)`);
    console.log(`   • Description: "BUYBACK: USDC → TOKEN"`);
    console.log(`   • Account: "Test Crypto Wallet"`);
    console.log("4. Click on the transaction to open details");
    console.log("5. Verify crypto expense details section shows:");
    console.log(`   • Type: BUYBACK`);
    console.log(`   • Amount Spent: €1,000.50 (in red)`);
    console.log(`   • From Token: 1,000.50 USDC`);
    console.log(`   • To Token: 500,000.25 TOKEN`);
    console.log(`   • Transaction ID: ${testCryptoExpense.txId}`);
    console.log(`   • Block Timestamp: ${testCryptoExpense.blockTimestamp.toLocaleString()}`);

    console.log("\n🧹 Cleanup:");
    console.log("===========");
    console.log("Run this script with --cleanup flag to remove test data");

    return {
      walletId: testWallet.id,
      transactionId: testTransaction.id,
      cryptoExpenseId: testCryptoExpense.id,
    };

  } catch (error) {
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function cleanupTestData() {
  console.log("🧹 Cleaning up test data...\n");

  try {
    // Delete test crypto expenses
    const deletedExpenses = await prisma.cryptoExpense.deleteMany({
      where: {
        txId: "TestTx123456789012345678901234567890123456789012345",
      },
    });
    console.log(`✅ Deleted ${deletedExpenses.count} test crypto expenses`);

    // Delete test transactions
    const deletedTransactions = await prisma.transaction.deleteMany({
      where: {
        metadata: {
          path: ["source"],
          equals: "test-crypto-expense-display",
        },
      },
    });
    console.log(`✅ Deleted ${deletedTransactions.count} test transactions`);

    // Delete test wallet accounts
    const deletedWallets = await prisma.account.deleteMany({
      where: {
        name: "Test Crypto Wallet",
      },
    });
    console.log(`✅ Deleted ${deletedWallets.count} test wallet accounts`);

    console.log("\n✅ Cleanup completed successfully");

  } catch (error) {
    console.error("❌ Cleanup failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const isCleanup = args.includes("--cleanup");

  try {
    if (isCleanup) {
      await cleanupTestData();
    } else {
      await createTestCryptoExpenseTransaction();
    }
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
