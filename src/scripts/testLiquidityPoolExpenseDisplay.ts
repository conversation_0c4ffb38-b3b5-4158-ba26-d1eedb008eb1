#!/usr/bin/env tsx

/**
 * Test script to verify liquidity pool expense display in transactions table
 * Creates test liquidity pool action crypto expenses and verifies UI display
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function createTestLiquidityPoolExpenseTransaction() {
  console.log("🧪 Testing Liquidity Pool Expense Display in Transactions Table");
  console.log("===============================================================\n");

  try {
    // First, create a test wallet account
    console.log("1. Creating test wallet account...");
    const testWallet = await prisma.account.create({
      data: {
        name: "Test LP Wallet",
        type: "WALLET",
        publicKey: "TestLPWallet123456789012345678901234567890",
      },
    });
    console.log(`✅ Created wallet account: ${testWallet.name} (${testWallet.id})`);

    // Create test transactions and liquidity pool expenses
    console.log("\n2. Creating test liquidity pool expense transactions...");
    
    const testLPActions = [
      {
        action: "add_liquidity",
        tokenASymbol: "SOL",
        tokenBSymbol: "USDC",
        tokenAAmount: 10.5,
        tokenBAmount: 2100.75,
        tokenAAmountUsd: 2100.75,
        tokenBAmountUsd: 2100.75, // This is the expense amount
        description: "Add Liquidity to SOL/USDC Pool",
      },
      {
        action: "remove_liquidity",
        tokenASymbol: "ALL",
        tokenBSymbol: "SOL",
        tokenAAmount: 50000.0,
        tokenBAmount: 5.25,
        tokenAAmountUsd: 1050.50,
        tokenBAmountUsd: 1050.50, // This is the expense amount
        description: "Remove Liquidity from ALL/SOL Pool",
      },
      {
        action: "swap",
        tokenASymbol: "USDC",
        tokenBSymbol: "ALL",
        tokenAAmount: 500.0,
        tokenBAmount: 25000.0,
        tokenAAmountUsd: 500.0,
        tokenBAmountUsd: 500.0, // This is the expense amount
        description: "Swap USDC for ALL",
      },
    ];

    const createdExpenses = [];

    for (let i = 0; i < testLPActions.length; i++) {
      const lpAction = testLPActions[i];
      
      // Create a transaction for each liquidity pool action
      const testTransaction = await prisma.transaction.create({
        data: {
          executedAt: new Date(),
          accountId: testWallet.id,
          metadata: {
            source: "test-liquidity-pool-expense-display",
            action: lpAction.action,
          },
        },
      });

      // Create the liquidity pool crypto expense
      const testCryptoExpense = await prisma.cryptoExpense.create({
        data: {
          type: "LIQUIDITY_POOL",
          txId: `TestLPTx${i + 1}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          blockTimestamp: new Date(),
          swapper: testWallet.publicKey!,
          lpAction: lpAction.action,
          lpPoolAddress: `TestPoolAddress${i + 1}_123456789012345678901234567890`,
          lpTokenAMint: `TestTokenAMint${i + 1}_123456789012345678901234567890`,
          lpTokenBMint: `TestTokenBMint${i + 1}_123456789012345678901234567890`,
          lpTokenASymbol: lpAction.tokenASymbol,
          lpTokenBSymbol: lpAction.tokenBSymbol,
          lpTokenAAmount: lpAction.tokenAAmount,
          lpTokenBAmount: lpAction.tokenBAmount,
          lpTokenAAmountUsd: lpAction.tokenAAmountUsd,
          lpTokenBAmountUsd: lpAction.tokenBAmountUsd,
          amountUsd: lpAction.tokenBAmountUsd, // Main amount field for display
          transactionId: testTransaction.id,
          rawFlipsideData: {
            test: true,
            source: "test-script",
            description: lpAction.description,
          },
        },
      });

      createdExpenses.push({
        transaction: testTransaction,
        expense: testCryptoExpense,
        action: lpAction,
      });

      console.log(`✅ Created ${lpAction.action} liquidity pool expense: ${testCryptoExpense.id}`);
      console.log(`   Transaction: ${testTransaction.id}`);
      console.log(`   Action: ${lpAction.action}`);
      console.log(`   Token Pair: ${lpAction.tokenASymbol}/${lpAction.tokenBSymbol}`);
      console.log(`   Expense Amount: $${lpAction.tokenBAmountUsd} (Token B Amount USD)`);
    }

    // Verify the data can be queried correctly
    console.log("\n3. Verifying transactions with liquidity pool expenses...");
    for (let i = 0; i < createdExpenses.length; i++) {
      const { transaction } = createdExpenses[i];
      const transactionWithCryptoExpense = await prisma.transaction.findUnique({
        where: { id: transaction.id },
        include: {
          cryptoExpense: {
            select: {
              id: true,
              type: true,
              txId: true,
              lpAction: true,
              lpTokenASymbol: true,
              lpTokenBSymbol: true,
              lpTokenAAmount: true,
              lpTokenBAmount: true,
              lpTokenAAmountUsd: true,
              lpTokenBAmountUsd: true,
              amountUsd: true,
              lpPoolAddress: true,
              blockTimestamp: true,
            },
          },
          account: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
      });

      if (transactionWithCryptoExpense?.cryptoExpense) {
        console.log(`✅ Transaction ${i + 1} query successful`);
        console.log(`   Transaction ID: ${transactionWithCryptoExpense.id}`);
        console.log(`   Account: ${transactionWithCryptoExpense.account.name}`);
        console.log(`   Crypto Expense Type: ${transactionWithCryptoExpense.cryptoExpense.type}`);
        console.log(`   LP Action: ${transactionWithCryptoExpense.cryptoExpense.lpAction}`);
        console.log(`   Token Pair: ${transactionWithCryptoExpense.cryptoExpense.lpTokenASymbol}/${transactionWithCryptoExpense.cryptoExpense.lpTokenBSymbol}`);
        console.log(`   Amount to Display: €${transactionWithCryptoExpense.cryptoExpense.amountUsd}`);
      } else {
        console.log(`❌ Failed to query transaction ${i + 1} with crypto expense`);
      }
    }

    console.log("\n4. Testing transaction stats...");
    const stats = await prisma.transaction.count({
      where: {
        cryptoExpense: {
          type: "LIQUIDITY_POOL",
        },
      },
    });
    console.log(`✅ Transactions with liquidity pool expenses: ${stats}`);

    console.log("\n📋 Test Results Summary:");
    console.log("========================");
    console.log(`✅ Wallet Account Created: ${testWallet.name}`);
    console.log(`✅ Transactions Created: ${createdExpenses.length}`);
    console.log(`✅ Liquidity Pool Expenses Created: ${createdExpenses.length}`);
    
    createdExpenses.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.action.action}: ${item.action.tokenASymbol}/${item.action.tokenBSymbol} - €${item.action.tokenBAmountUsd}`);
    });

    console.log("\n🎯 Frontend Testing Instructions:");
    console.log("=================================");
    console.log("1. Open the transactions page in your browser");
    console.log("2. Look for the test transactions created above");
    console.log("3. Verify the following displays correctly:");
    console.log(`   • Type: "Crypto Expense" badge (purple)`);
    console.log(`   • Amount: EUR formatted (e.g., €2,100.75, €1,050.50, €500.00)`);
    console.log(`   • Description: "LIQUIDITY_POOL: action (TOKEN_A/TOKEN_B)"`);
    console.log(`   • Account: "Test LP Wallet"`);
    console.log("4. Click on each transaction to open details");
    console.log("5. Verify liquidity pool expense details section shows:");
    console.log(`   • Type: LIQUIDITY_POOL`);
    console.log(`   • Amount Spent: EUR amount (in red)`);
    console.log(`   • Action: add_liquidity, remove_liquidity, or swap`);
    console.log(`   • Token Pair: SOL/USDC, ALL/SOL, USDC/ALL`);
    console.log(`   • Token A Amount: with symbol and USD value`);
    console.log(`   • Token B Amount: with symbol and USD value`);
    console.log(`   • Pool Address: copyable`);
    console.log(`   • Token A Mint: copyable`);
    console.log(`   • Token B Mint: copyable`);
    console.log(`   • Transaction ID: copyable with Solscan link`);
    console.log(`   • Block Timestamp: formatted date/time`);

    console.log("\n🧹 Cleanup:");
    console.log("===========");
    console.log("Run this script with --cleanup flag to remove test data");

    return {
      walletId: testWallet.id,
      transactionIds: createdExpenses.map(item => item.transaction.id),
      cryptoExpenseIds: createdExpenses.map(item => item.expense.id),
    };

  } catch (error) {
    console.error("❌ Test failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function cleanupTestData() {
  console.log("🧹 Cleaning up test data...\n");

  try {
    // Delete test crypto expenses
    const deletedExpenses = await prisma.cryptoExpense.deleteMany({
      where: {
        txId: {
          startsWith: "TestLPTx",
        },
      },
    });
    console.log(`✅ Deleted ${deletedExpenses.count} test crypto expenses`);

    // Delete test transactions
    const deletedTransactions = await prisma.transaction.deleteMany({
      where: {
        metadata: {
          path: ["source"],
          equals: "test-liquidity-pool-expense-display",
        },
      },
    });
    console.log(`✅ Deleted ${deletedTransactions.count} test transactions`);

    // Delete test wallet accounts
    const deletedWallets = await prisma.account.deleteMany({
      where: {
        name: "Test LP Wallet",
      },
    });
    console.log(`✅ Deleted ${deletedWallets.count} test wallet accounts`);

    console.log("\n✅ Cleanup completed successfully");

  } catch (error) {
    console.error("❌ Cleanup failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const isCleanup = args.includes("--cleanup");

  try {
    if (isCleanup) {
      await cleanupTestData();
    } else {
      await createTestLiquidityPoolExpenseTransaction();
    }
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
