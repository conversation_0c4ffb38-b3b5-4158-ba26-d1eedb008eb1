#!/usr/bin/env tsx

/**
 * Test script to demonstrate the interactive crypto expense import functionality
 * This script shows how the interactive prompts work without actually running them
 */

import { PrismaClient } from "@/prisma/generated";

const prisma = new PrismaClient();

async function demonstrateInteractiveFeatures() {
  console.log("🧪 Interactive Crypto Expense Import - Feature Demonstration");
  console.log("============================================================\n");

  try {
    // Show available wallet accounts
    console.log("1️⃣  Fetching wallet accounts from database...\n");
    
    const wallets = await prisma.account.findMany({
      where: {
        type: "WALLET",
        publicKey: { not: null },
      },
      select: {
        id: true,
        name: true,
        publicKey: true,
        createdAt: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    if (wallets.length === 0) {
      console.log("❌ No wallet accounts found in database.");
      console.log("   Please create wallet accounts with public keys first.");
      return;
    }

    console.log(`📋 Found ${wallets.length} wallet account(s):`);
    wallets.forEach((wallet, index) => {
      const truncatedKey = `${wallet.publicKey!.slice(0, 8)}...${wallet.publicKey!.slice(-4)}`;
      console.log(`   ${index + 1}. ${wallet.name} (${truncatedKey})`);
    });

    // Show available accounts for transactions
    console.log("\n2️⃣  Available accounts for transaction creation...\n");
    
    const accounts = await prisma.account.findMany({
      select: { id: true, name: true, type: true },
      orderBy: { name: "asc" },
    });

    console.log(`📋 Found ${accounts.length} account(s):`);
    accounts.forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.name} (${account.type})`);
    });

    console.log("\n3️⃣  Interactive Features Available:\n");
    
    console.log("🔹 Wallet Selection Options:");
    console.log("   • Import from all wallets");
    console.log("   • Select specific wallets (multi-select with space)");
    console.log("   • Enter custom wallet addresses");
    
    console.log("\n🔹 Configuration Options:");
    console.log("   • From date (YYYY-MM-DD format)");
    console.log("   • To date (YYYY-MM-DD format)");
    console.log("   • Action: Preview / Import / Import with transactions");
    console.log("   • Account selection for transaction creation");
    
    console.log("\n🔹 Validation Features:");
    console.log("   • Date format validation");
    console.log("   • Wallet address format validation");
    console.log("   • Required field validation");
    console.log("   • Final confirmation prompt");

    console.log("\n4️⃣  Usage Examples:\n");
    
    console.log("🚀 Interactive Mode (default):");
    console.log("   pnpm tsx src/scripts/importCryptoExpenses.ts");
    console.log("   → Prompts for wallet selection and configuration");
    
    console.log("\n⚡ Non-Interactive Mode:");
    console.log("   pnpm tsx src/scripts/importCryptoExpenses.ts --non-interactive \\");
    console.log("     --preview --company-wallets \"wallet1,wallet2\" \\");
    console.log("     --from-date \"2024-01-01\"");
    
    console.log("\n🔍 Preview Mode:");
    console.log("   pnpm tsx src/scripts/importCryptoExpenses.ts --preview");
    console.log("   → Interactive wallet selection + preview data");
    
    console.log("\n📥 Import Mode:");
    console.log("   pnpm tsx src/scripts/importCryptoExpenses.ts");
    console.log("   → Interactive selection + actual import");

    console.log("\n5️⃣  Interactive Flow:\n");
    
    console.log("Step 1: Wallet Selection");
    console.log("   ┌─ All wallets");
    console.log("   ├─ Select specific (checkbox with space/enter)");
    console.log("   └─ Custom addresses");
    
    console.log("\nStep 2: Date Range");
    console.log("   ┌─ From date (optional)");
    console.log("   └─ To date (optional)");
    
    console.log("\nStep 3: Action");
    console.log("   ┌─ Preview only");
    console.log("   ├─ Import crypto expenses");
    console.log("   └─ Import + create transactions");
    
    console.log("\nStep 4: Account Selection (if creating transactions)");
    console.log("   └─ Choose account for transaction records");
    
    console.log("\nStep 5: Confirmation");
    console.log("   └─ Review settings and confirm");

    console.log("\n6️⃣  Error Handling:\n");
    
    console.log("🛡️  Built-in Protections:");
    console.log("   • Ctrl+C handling (graceful exit)");
    console.log("   • Invalid date format detection");
    console.log("   • Invalid wallet address validation");
    console.log("   • Empty selection prevention");
    console.log("   • Database connection error handling");
    console.log("   • Flipside API error handling");

    console.log("\n✅ The interactive import script is ready to use!");
    console.log("   Run it without flags to start the interactive experience.");

  } catch (error) {
    console.error("❌ Demonstration failed:", error instanceof Error ? error.message : String(error));
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  await demonstrateInteractiveFeatures();
}

if (require.main === module) {
  main();
}
