#!/usr/bin/env tsx

/**
 * Backup script for crypto expenses before refactoring
 * This script creates a backup of all crypto expense data
 */

import { PrismaClient } from "@/prisma/generated";
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

async function backupCryptoExpenses() {
  console.log("💾 Creating backup of crypto expenses...\n");

  try {
    // Get all existing crypto expenses with all fields
    const allExpenses = await prisma.cryptoExpense.findMany({
      include: {
        transaction: {
          select: {
            id: true,
            executedAt: true,
            account: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
      },
    });

    console.log(`Found ${allExpenses.length} crypto expenses to backup`);

    if (allExpenses.length === 0) {
      console.log("✅ No crypto expenses to backup");
      return;
    }

    // Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Create backup file with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = path.join(backupDir, `crypto-expenses-backup-${timestamp}.json`);

    // Write backup data
    fs.writeFileSync(backupFile, JSON.stringify(allExpenses, null, 2));

    console.log(`✅ Backup created: ${backupFile}`);
    console.log(`   Total records: ${allExpenses.length}`);

    // Show summary by type
    const summary = allExpenses.reduce((acc, expense) => {
      acc[expense.type] = (acc[expense.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log("\n📊 Backup summary by type:");
    Object.entries(summary).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} records`);
    });

    return backupFile;

  } catch (error) {
    console.error("❌ Backup failed:", error instanceof Error ? error.message : String(error));
    throw error;
  }
}

async function main() {
  try {
    await backupCryptoExpenses();
  } catch (error) {
    console.error("❌ Script failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}
