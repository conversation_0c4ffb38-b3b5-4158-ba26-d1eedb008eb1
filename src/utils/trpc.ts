import { httpLink, loggerLink, TRPCClientError } from "@trpc/client";
import { keepPreviousData } from "@tanstack/react-query";
import { createTRPCNext } from "@trpc/next";
import { type inferRouterInputs, type inferRouterOutputs } from "@trpc/server";
import superjson from "superjson";
import { toast } from "sonner";
import { type AppRouter } from "@/server/routers/_app";

const getBaseUrl = () => {
  if (typeof window !== "undefined") return ""; // browser should use relative url
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`; // SSR should use vercel url
  return `http://localhost:${process.env.PORT ?? 3000}`; // dev SSR should use localhost
};

/** A set of type-safe react-query hooks for your tRPC API. */
export const trpc = createTRPCNext<AppRouter>({
  // @ts-ignore
  config() {
    return {
      queryClientConfig: {
        defaultOptions: {
          queries: {
            placeholderData: keepPreviousData,
            retry: (failureCount, error) => {
              // Don't retry on client errors (4xx)
              if (error instanceof TRPCClientError && error.data?.httpStatus && error.data.httpStatus >= 400 && error.data.httpStatus < 500) {
                return false;
              }
              // Retry up to 3 times for server errors
              return failureCount < 3;
            },
            onError: (error: TRPCClientError<AppRouter>) => {
              // Global query error handling
              if (typeof window !== "undefined") {
                const errorMessage = error.message || "An error occurred while fetching data";
                toast.error("Failed to load data", {
                  description: errorMessage,
                  duration: 4000,
                });
              }
            },
          },
          mutations: {
            onError: (error: TRPCClientError<AppRouter>) => {
              // Global mutation error handling
              if (typeof window !== "undefined") {
                const errorMessage = error.message || "An error occurred";
                toast.error("Operation failed", {
                  description: errorMessage,
                  duration: 5000,
                  action: {
                    label: "Dismiss",
                    onClick: () => {},
                  },
                });
              }
            },
          },
        },
      },
      /**
       * Transformer used for data de-serialization from the server.
       *
       * @see https://trpc.io/docs/data-transformers
       */

      transformer: superjson,

      /**
       * Links used to determine request flow from client to server.
       *
       * @see https://trpc.io/docs/links
       */
      links: [
        loggerLink({
          enabled: (opts) => process.env.NODE_ENV === "development" || (opts.direction === "down" && opts.result instanceof Error),
        }),
        httpLink({
          url: `${getBaseUrl()}/api/trpc`,

          transformer: superjson,
        }),
      ],
    };
  },
  /**
   * Whether tRPC should await queries when server rendering pages.
   *
   * @see https://trpc.io/docs/nextjs#ssr-boolean-default-false
   */
  ssr: false,
});

/**
 * Inference helper for inputs.
 *
 * @example type HelloInput = RouterInputs['example']['hello']
 */
export type RouterInputs = inferRouterInputs<AppRouter>;

/**
 * Inference helper for outputs.
 *
 * @example type HelloOutput = RouterOutputs['example']['hello']
 */
export type RouterOutputs = inferRouterOutputs<AppRouter>;
