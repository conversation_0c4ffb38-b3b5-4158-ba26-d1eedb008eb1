import { streamText } from "ai";
import { createOpenAI } from "@ai-sdk/openai";
import { z } from "zod";
import { prisma } from "@/prisma/prisma";
import { getAccountingYearRange } from "@/modules/settings/settingsUtils";
import { wrapAISDKModel } from "braintrust";
import { csvUtils } from "@/modules/core/utils/csvUtils";
import { accountTools } from "@/modules/ai/tools/accountTools";

// Create OpenAI client
const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Helper function to prepare CSV data for LLM analysis
function analyzeForInvoiceImport(headers: string[], columnTypes: Record<string, string>) {
  return {
    headers,
    columnTypes,
    message:
      "Please analyze these CSV headers and suggest how they could map to invoice fields (date, amount, vendor, reference, description, currency, VAT, etc.)",
  };
}

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Process any CSV files in the messages
    const processedMessages = await Promise.all(
      messages.map(async (message: any) => {
        if (message.experimental_attachments) {
          const csvAttachments = message.experimental_attachments.filter(
            (attachment: any) => attachment.contentType === "text/csv" || attachment.name?.endsWith(".csv")
          );

          if (csvAttachments.length > 0) {
            // Process the first CSV file
            const csvAttachment = csvAttachments[0];
            const csvContent = await fetch(csvAttachment.url).then((res) => res.text());

            // Add CSV processing instruction to the message
            message.content += `\n\nI've uploaded a CSV file "${csvAttachment.name}". Please process this CSV file with analysisType "import" to analyze its structure and suggest field mappings for invoice import. Here's the CSV content:\n\n${csvContent}`;
          }
        }
        return message;
      })
    );

    const result = streamText({
      // model: wrapAISDKModel(openai("gpt-4.1-mini")),
      model: wrapAISDKModel(openai("gpt-4.1")),
      onError: (error) => {
        console.error("Chat API error:", error);
      },
      messages: processedMessages,
      tools: {
        // Get basic invoice statistics
        getInvoiceStats: {
          description: "Get basic invoice statistics including total count, amounts, VAT information",
          parameters: z.object({}),
          execute: async () => {
            const [total, withVAT, reverseCharge] = await Promise.all([
              prisma.invoice.count(),
              prisma.invoice.count({ where: { hasVAT: true } }),
              prisma.invoice.count({ where: { isReverseCharge: true } }),
            ]);

            const amountStats = await prisma.invoice.aggregate({
              _sum: {
                amountGross: true,
                amountNet: true,
                amountVat: true,
              },
            });

            return {
              total,
              withVAT,
              reverseCharge,
              withoutVAT: total - withVAT,
              totalAmountGross: amountStats._sum?.amountGross?.toString() || "0",
              totalAmountNet: amountStats._sum?.amountNet?.toString() || "0",
              totalAmountVat: amountStats._sum?.amountVat?.toString() || "0",
            };
          },
        },

        // Get invoice statistics by year
        getInvoiceStatsByYear: {
          description: "Get invoice statistics for a specific year (accounting year)",
          parameters: z.object({
            year: z.number().describe("The year to get statistics for"),
          }),
          execute: async ({ year }) => {
            // Get accounting year settings
            const settings = await prisma.settings.findFirst();
            let whereClause: any = {};

            if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
              const yearRange = getAccountingYearRange(year, {
                day: settings.accountingYearStartDay,
                month: settings.accountingYearStartMonth,
              });
              whereClause.date = {
                gte: yearRange.start,
                lte: yearRange.end,
              };
            } else {
              // Fallback to calendar year
              whereClause.date = {
                gte: new Date(year, 0, 1),
                lte: new Date(year, 11, 31),
              };
            }

            const [total, withVAT, reverseCharge] = await Promise.all([
              prisma.invoice.count({ where: whereClause }),
              prisma.invoice.count({ where: { ...whereClause, hasVAT: true } }),
              prisma.invoice.count({ where: { ...whereClause, isReverseCharge: true } }),
            ]);

            const amountStats = await prisma.invoice.aggregate({
              _sum: {
                amountGross: true,
                amountNet: true,
                amountVat: true,
              },
              where: whereClause,
            });

            return {
              year,
              total,
              withVAT,
              reverseCharge,
              withoutVAT: total - withVAT,
              totalAmountGross: amountStats._sum?.amountGross?.toString() || "0",
              totalAmountNet: amountStats._sum?.amountNet?.toString() || "0",
              totalAmountVat: amountStats._sum?.amountVat?.toString() || "0",
            };
          },
        },

        // Get top vendors by invoice count
        getTopVendorsByCount: {
          description: "Get the top vendors by number of invoices",
          parameters: z.object({
            limit: z.number().default(10).describe("Number of top vendors to return"),
          }),
          execute: async ({ limit }) => {
            console.log("in getTopVendorsByCount", limit);
            const vendors = await prisma.vendor.findMany({
              include: {
                _count: {
                  select: { invoices: true },
                },
              },
              orderBy: {
                invoices: {
                  _count: "desc",
                },
              },
              take: limit,
            });

            return vendors.map((vendor) => ({
              id: vendor.id,
              name: vendor.name,
              city: vendor.city,
              country: vendor.country,
              invoiceCount: vendor._count.invoices,
            }));
          },
        },

        // Get top vendors by total amount
        getTopVendorsByAmount: {
          description: "Get the top vendors by total invoice amount",
          parameters: z.object({
            limit: z.number().default(10).describe("Number of top vendors to return"),
          }),
          execute: async ({ limit }) => {
            const vendorAmounts = await prisma.invoice.groupBy({
              by: ["vendorId"],
              _sum: {
                amountGross: true,
              },
              _count: {
                id: true,
              },
              orderBy: {
                _sum: {
                  amountGross: "desc",
                },
              },
              take: limit,
            });

            // Get vendor details
            const vendorIds = vendorAmounts.map((v) => v.vendorId);
            const vendors = await prisma.vendor.findMany({
              where: { id: { in: vendorIds } },
            });

            const vendorMap = new Map(vendors.map((v) => [v.id, v]));

            return vendorAmounts.map((va) => {
              const vendor = vendorMap.get(va.vendorId);
              return {
                id: va.vendorId,
                name: vendor?.name || "Unknown",
                city: vendor?.city || "",
                country: vendor?.country || "",
                totalAmount: va._sum.amountGross?.toString() || "0",
                invoiceCount: va._count.id,
              };
            });
          },
        },

        // Get available years
        getAvailableYears: {
          description: "Get the list of years that have invoices",
          parameters: z.object({}),
          execute: async () => {
            const dateRange = await prisma.invoice.aggregate({
              _min: { date: true },
              _max: { date: true },
            });

            if (!dateRange._min?.date || !dateRange._max?.date) {
              return [];
            }

            const settings = await prisma.settings.findFirst();
            const years: number[] = [];

            if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
              // Use accounting years
              const startYear = dateRange._min.date.getFullYear();
              const endYear = dateRange._max.date.getFullYear();

              for (let year = startYear; year <= endYear + 1; year++) {
                const yearRange = getAccountingYearRange(year, {
                  day: settings.accountingYearStartDay,
                  month: settings.accountingYearStartMonth,
                });

                const hasInvoices = await prisma.invoice.count({
                  where: {
                    date: {
                      gte: yearRange.start,
                      lte: yearRange.end,
                    },
                  },
                });

                if (hasInvoices > 0) {
                  years.push(year);
                }
              }
            } else {
              // Use calendar years
              const startYear = dateRange._min.date.getFullYear();
              const endYear = dateRange._max.date.getFullYear();

              for (let year = startYear; year <= endYear; year++) {
                years.push(year);
              }
            }

            return years.sort((a, b) => b - a);
          },
        },

        // Get monthly invoice summary
        getMonthlyInvoiceSummary: {
          description: "Get invoice count and amounts grouped by month for a specific year",
          parameters: z.object({
            year: z.number().describe("The year to get monthly summary for"),
          }),
          execute: async ({ year }) => {
            const settings = await prisma.settings.findFirst();
            let whereClause: any = {};

            if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
              const yearRange = getAccountingYearRange(year, {
                day: settings.accountingYearStartDay,
                month: settings.accountingYearStartMonth,
              });
              whereClause.date = {
                gte: yearRange.start,
                lte: yearRange.end,
              };
            } else {
              whereClause.date = {
                gte: new Date(year, 0, 1),
                lte: new Date(year, 11, 31),
              };
            }

            const invoices = await prisma.invoice.findMany({
              where: whereClause,
              select: {
                date: true,
                amountGross: true,
                amountNet: true,
                amountVat: true,
              },
            });

            // Group by month
            const monthlyData = new Map<
              string,
              {
                count: number;
                totalGross: number;
                totalNet: number;
                totalVat: number;
              }
            >();

            invoices.forEach((invoice) => {
              const monthKey = `${invoice.date.getFullYear()}-${String(invoice.date.getMonth() + 1).padStart(2, "0")}`;

              if (!monthlyData.has(monthKey)) {
                monthlyData.set(monthKey, {
                  count: 0,
                  totalGross: 0,
                  totalNet: 0,
                  totalVat: 0,
                });
              }

              const data = monthlyData.get(monthKey)!;
              data.count++;
              data.totalGross += Number(invoice.amountGross);
              data.totalNet += Number(invoice.amountNet);
              data.totalVat += Number(invoice.amountVat);
            });

            return Array.from(monthlyData.entries())
              .map(([month, data]) => ({
                month,
                count: data.count,
                totalGross: data.totalGross.toString(),
                totalNet: data.totalNet.toString(),
                totalVat: data.totalVat.toString(),
              }))
              .sort((a, b) => a.month.localeCompare(b.month));
          },
        },

        // Get invoice statistics by currency
        getInvoiceStatsByCurrency: {
          description: "Get invoice statistics grouped by currency code",
          parameters: z.object({}),
          execute: async () => {
            const currencyStats = await prisma.invoice.groupBy({
              by: ["currencyCode"],
              _count: {
                id: true,
              },
              _sum: {
                amountGross: true,
                amountNet: true,
                amountVat: true,
              },
              orderBy: {
                _count: {
                  id: "desc",
                },
              },
            });

            return currencyStats.map((stat) => ({
              currencyCode: stat.currencyCode,
              count: stat._count.id,
              totalGross: stat._sum.amountGross?.toString() || "0",
              totalNet: stat._sum.amountNet?.toString() || "0",
              totalVat: stat._sum.amountVat?.toString() || "0",
            }));
          },
        },

        // Search invoices by vendor name or invoice reference
        searchInvoices: {
          description: "Search for invoices by vendor name or invoice reference",
          parameters: z.object({
            query: z.string().describe("Search term for vendor name or invoice reference"),
            limit: z.number().default(10).describe("Maximum number of results to return"),
          }),
          execute: async ({ query, limit }) => {
            const invoices = await prisma.invoice.findMany({
              where: {
                OR: [
                  {
                    vendor: {
                      name: {
                        contains: query,
                        mode: "insensitive",
                      },
                    },
                  },
                  {
                    invoiceReference: {
                      contains: query,
                      mode: "insensitive",
                    },
                  },
                ],
              },
              include: {
                vendor: {
                  select: {
                    name: true,
                    city: true,
                    country: true,
                  },
                },
              },
              orderBy: {
                date: "desc",
              },
              take: limit,
            });

            return invoices.map((invoice) => ({
              id: invoice.id,
              invoiceReference: invoice.invoiceReference,
              date: invoice.date.toISOString().split("T")[0],
              amountGross: invoice.amountGross.toString(),
              currencyCode: invoice.currencyCode,
              vendorName: invoice.vendor.name,
              vendorLocation: `${invoice.vendor.city}, ${invoice.vendor.country}`,
              hasVAT: invoice.hasVAT,
              isReverseCharge: invoice.isReverseCharge,
            }));
          },
        },

        // Get recent invoices
        getRecentInvoices: {
          description: "Get the most recent invoices",
          parameters: z.object({
            limit: z.number().default(10).describe("Number of recent invoices to return"),
          }),
          execute: async ({ limit }) => {
            const invoices = await prisma.invoice.findMany({
              include: {
                vendor: {
                  select: {
                    name: true,
                    city: true,
                    country: true,
                  },
                },
              },
              orderBy: {
                date: "desc",
              },
              take: limit,
            });

            return invoices.map((invoice) => ({
              id: invoice.id,
              invoiceReference: invoice.invoiceReference,
              date: invoice.date.toISOString().split("T")[0],
              amountGross: invoice.amountGross.toString(),
              currencyCode: invoice.currencyCode,
              vendorName: invoice.vendor.name,
              vendorLocation: `${invoice.vendor.city}, ${invoice.vendor.country}`,
              hasVAT: invoice.hasVAT,
              isReverseCharge: invoice.isReverseCharge,
              validatedAt: invoice.validatedAt?.toISOString().split("T")[0] || null,
            }));
          },
        },

        // Map CSV headers to invoice fields using AI
        mapCSVToInvoiceFields: {
          description: "Analyze CSV headers and suggest mapping to invoice fields using AI",
          parameters: z.object({
            headers: z.array(z.string()).describe("CSV column headers to analyze"),
            columnTypes: z.record(z.string()).describe("Data types of each column"),
            sampleData: z.string().optional().describe("Sample data from the CSV"),
          }),
          execute: async ({ headers, columnTypes, sampleData }) => {
            // This tool will be called by the LLM to get intelligent field mapping suggestions
            const invoiceFields = [
              "date - Invoice date",
              "amount_gross - Total gross amount",
              "amount_net - Net amount before tax",
              "amount_vat - VAT/tax amount",
              "vendor_name - Vendor/supplier name",
              "vendor_address - Vendor address",
              "invoice_reference - Invoice number/reference",
              "description - Invoice description/items",
              "currency_code - Currency (EUR, USD, etc.)",
              "vat_rate - VAT percentage rate",
              "payment_terms - Payment terms",
              "due_date - Payment due date",
            ];

            return {
              success: true,
              availableInvoiceFields: invoiceFields,
              csvHeaders: headers,
              columnTypes,
              sampleData,
              instructions:
                "Based on the CSV headers, column types, and sample data, suggest the best mapping between CSV columns and invoice fields. Consider language variations (English/German) and common naming conventions.",
            };
          },
        },

        // Account management tools
        createAccount: accountTools.createAccount,
        createAccounts: accountTools.createAccounts,
        getAccounts: accountTools.getAccounts,
        getAccountStats: accountTools.getAccountStats,

        // Process CSV file uploads
        processCSVFile: {
          description: "Process and analyze uploaded CSV files for data import or analysis",
          parameters: z.object({
            csvContent: z.string().describe("The content of the CSV file to process"),
            analysisType: z.enum(["structure", "import", "summary"]).default("structure").describe("Type of analysis to perform"),
          }),
          execute: async ({ csvContent, analysisType }) => {
            const parseResult = csvUtils.parseCSV(csvContent);

            if (parseResult.isErr()) {
              return {
                success: false,
                error: parseResult.error.message,
                type: "parse_error",
              };
            }

            const parsed = parseResult.value;
            const analysis = csvUtils.analyzeCSVStructure(parsed);

            switch (analysisType) {
              case "structure":
                return {
                  success: true,
                  type: "structure_analysis",
                  summary: analysis.summary,
                  headers: parsed.headers,
                  columnTypes: analysis.columnTypes,
                  rowCount: parsed.rowCount,
                  preview: parsed.preview,
                };

              case "summary":
                return {
                  success: true,
                  type: "data_summary",
                  summary: analysis.summary,
                  sampleData: analysis.sampleData,
                  columnTypes: analysis.columnTypes,
                };

              case "import":
                // For import analysis, provide data for LLM to analyze
                const importAnalysis = analyzeForInvoiceImport(parsed.headers, analysis.columnTypes);
                return {
                  success: true,
                  type: "import_analysis",
                  summary: analysis.summary,
                  headers: parsed.headers,
                  columnTypes: analysis.columnTypes,
                  preview: parsed.preview,
                  analysisMessage: importAnalysis.message,
                };

              default:
                return {
                  success: true,
                  type: "basic_analysis",
                  summary: analysis.summary,
                  preview: parsed.preview,
                };
            }
          },
        },
      },
      maxSteps: 5,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error("Chat API error:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
