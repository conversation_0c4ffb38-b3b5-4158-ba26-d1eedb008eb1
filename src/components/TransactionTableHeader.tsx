import * as React from "react";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DateRangePicker, DateRange } from "@/components/ui/date-range-picker";
import { AmountRangeFilter, AmountRange } from "@/components/ui/amount-range-filter";
import { MultiAccountFilter, Account } from "@/components/ui/account-filter";
import { SearchInput } from "@/components/SearchInput";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export interface SortConfig {
  field: string;
  direction: "asc" | "desc" | null;
}

interface TransactionTableHeaderProps {
  // Filter props
  dateRange?: DateRange;
  onDateRangeChange?: (dateRange: DateRange | undefined) => void;

  amountRange?: AmountRange;
  onAmountRangeChange?: (amountRange: AmountRange | undefined) => void;

  accounts: Account[];
  selectedAccountIds?: string[];
  onAccountChange?: (accountIds: string[]) => void;

  selectedType?: string;
  onTypeChange?: (type: string | undefined) => void;

  searchTerm: string;
  onSearchChange: (search: string) => void;

  // Sort props
  sortConfig?: SortConfig;
  onSortChange?: (sort: SortConfig) => void;

  // Helper functions
  getAccountTypeLabel?: (type: string) => string;
}

export function TransactionTableHeader({
  dateRange,
  onDateRangeChange,
  amountRange,
  onAmountRangeChange,
  accounts,
  selectedAccountIds,
  onAccountChange,
  selectedType,
  onTypeChange,
  searchTerm,
  onSearchChange,
  sortConfig,
  onSortChange,
  getAccountTypeLabel,
}: TransactionTableHeaderProps) {
  const handleSort = (field: string) => {
    if (!onSortChange) return;

    let direction: "asc" | "desc" | null = "asc";

    if (sortConfig?.field === field) {
      if (sortConfig.direction === "asc") {
        direction = "desc";
      } else if (sortConfig.direction === "desc") {
        direction = null;
      }
    }

    onSortChange({ field, direction });
  };

  const getSortIcon = (field: string) => {
    if (sortConfig?.field !== field || !sortConfig.direction) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />;
    }

    return sortConfig.direction === "asc" ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
  };

  return (
    <>
      {/* Filter Row */}
      <TableRow className="border-b-2">
        <TableHead className="p-2">
          <DateRangePicker dateRange={dateRange} onDateRangeChange={onDateRangeChange} placeholder="Filter by date" className="w-full" />
        </TableHead>
        <TableHead className="p-2">
          <MultiAccountFilter
            accounts={accounts}
            selectedAccountIds={selectedAccountIds}
            onAccountChange={onAccountChange}
            placeholder="All accounts"
            getAccountTypeLabel={getAccountTypeLabel}
            className="w-full"
          />
        </TableHead>
        <TableHead className="p-2">
          <Select value={selectedType || "all"} onValueChange={(value) => onTypeChange?.(value === "all" ? undefined : value)}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              <SelectItem value="transfer">Transfers</SelectItem>
              <SelectItem value="trade">Trades</SelectItem>
              <SelectItem value="crypto_expense">Crypto Expenses</SelectItem>
            </SelectContent>
          </Select>
        </TableHead>
        <TableHead className="p-2">
          <AmountRangeFilter amountRange={amountRange} onAmountRangeChange={onAmountRangeChange} placeholder="Filter by USD amount" className="w-full" />
        </TableHead>
        <TableHead className="p-2">
          <SearchInput value={searchTerm} onChange={onSearchChange} placeholder="Search description..." className="w-full" />
        </TableHead>
        <TableHead className="p-2">{/* Group column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* Actions column - no filter */}</TableHead>
      </TableRow>

      {/* Header Row with Sort */}
      <TableRow>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("executedAt")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Date
            {getSortIcon("executedAt")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("account")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Account
            {getSortIcon("account")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("type")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Type
            {getSortIcon("type")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("amount")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Amount
            {getSortIcon("amount")}
          </Button>
        </TableHead>
        <TableHead>Description</TableHead>
        <TableHead>Group</TableHead>
        <TableHead>Actions</TableHead>
      </TableRow>
    </>
  );
}
