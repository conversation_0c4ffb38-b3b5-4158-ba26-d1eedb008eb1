import { use<PERSON><PERSON>back, FormEvent } from "react";
import { useForm } from "@tanstack/react-form";
import { useStore } from "@tanstack/react-store";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Save, X } from "lucide-react";
import { Decimal } from "decimal.js-light";
import { monetary } from "@/modules/core/monetary";

export interface EditableLineItem {
  id?: string;
  name: string;
  description?: string;
  quantity: number;
  priceGross: number;
  priceNet?: number;
  vatRate?: number;
}

interface EditableLineItemsProps {
  lineItems: EditableLineItem[];
  onSave: (lineItems: EditableLineItem[]) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function EditableLineItems({ lineItems, onSave, onCancel, isLoading }: EditableLineItemsProps) {
  /*
   * TanStack Form setup
   */
  const form = useForm({
    defaultValues: {
      items: lineItems.length ? lineItems : ([] as EditableLineItem[]),
      globalVat: undefined as number | undefined,
    },
    onSubmit: async ({ value }: { value: { items: EditableLineItem[] } }) => {
      const validItems = value.items.filter((item) => item.name.trim() !== "" && item.priceGross > 0);
      onSave(validItems);
    },
  });

  // Reactively grab the items array from the form store so that re-renders are scoped only to relevant changes
  const items = useStore(form.store, (state: { values: { items: EditableLineItem[] } }) => state.values.items);

  const globalVat = useStore(form.store, (state: { values: { globalVat?: number } }) => state.values.globalVat);

  const formatCurrency = (amount: number | Decimal) => {
    const numericAmount = typeof amount === "number" ? amount : amount.toNumber();
    return `€${monetary.toNumber(monetary.toDecimal(numericAmount)).toFixed(2)}`;
  };

  const addItem = () => {
    form.setFieldValue("items", (prev: EditableLineItem[]) => [
      ...prev,
      {
        name: "",
        description: "",
        quantity: 1,
        priceGross: 0,
        priceNet: 0,
        vatRate: 0,
      },
    ]);
  };

  const removeItem = (index: number) => {
    form.setFieldValue("items", (prev: EditableLineItem[]) => prev.filter((_, i) => i !== index));
  };

  const updateItem = useCallback(
    (index: number, field: keyof EditableLineItem, value: string | number | undefined) => {
      const current = items[index];
      if (!current) return;

      let updated: EditableLineItem = { ...current, [field]: value } as EditableLineItem;

      if (field === "priceGross" && typeof value === "number") {
        const gross = monetary.toDecimal(value);
        if (updated.vatRate && updated.vatRate > 0) {
          const vatMultiplier = new Decimal(1).plus(monetary.roundVatRate(updated.vatRate).div(100));
          updated.priceNet = monetary.toNumber(gross.div(vatMultiplier));
        } else {
          updated.priceNet = monetary.toNumber(gross);
        }
      } else if (field === "priceNet" && typeof value === "number") {
        const net = monetary.toDecimal(value);
        if (updated.vatRate && updated.vatRate > 0) {
          const vatMultiplier = new Decimal(1).plus(monetary.roundVatRate(updated.vatRate).div(100));
          updated.priceGross = monetary.toNumber(net.mul(vatMultiplier));
        } else {
          updated.priceGross = monetary.toNumber(net);
        }
      } else if (field === "vatRate") {
        const rate = typeof value === "number" ? value : 0;
        if (rate > 0 && updated.priceGross > 0) {
          const vatMultiplier = new Decimal(1).plus(monetary.roundVatRate(rate).div(100));
          updated.priceNet = monetary.toNumber(monetary.toDecimal(updated.priceGross).div(vatMultiplier));
        } else {
          updated.priceNet = monetary.toNumber(monetary.toDecimal(updated.priceGross));
        }
      }

      form.setFieldValue(`items[${index}]`, updated);
    },
    [form, items]
  );

  const calculateTotals = useCallback(() => {
    let totalGross = new Decimal(0);
    let totalNet = new Decimal(0);
    let totalVat = new Decimal(0);

    items.forEach((item: EditableLineItem) => {
      const lineGross = monetary.toDecimal(item.priceGross).mul(item.quantity);
      totalGross = totalGross.plus(lineGross);

      if (item.vatRate) {
        const vatMultiplier = new Decimal(1).plus(monetary.roundVatRate(item.vatRate).div(100));
        const lineNet = lineGross.div(vatMultiplier);
        totalNet = totalNet.plus(lineNet);
        totalVat = totalVat.plus(lineGross.minus(lineNet));
      } else {
        totalNet = totalNet.plus(lineGross);
      }
    });

    return {
      totalGross: monetary.toNumber(totalGross),
      totalNet: monetary.toNumber(totalNet),
      totalVat: monetary.toNumber(totalVat),
    };
  }, [items]);

  const { totalGross, totalNet, totalVat } = calculateTotals();

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    e.stopPropagation();
    form.handleSubmit();
  };

  const updateGlobalVat = (rate: number | undefined) => {
    // Update the standalone globalVat value in the form
    form.setFieldValue("globalVat", rate);

    if (rate === undefined) return;

    // Apply VAT rate to every item and adjust net prices accordingly
    form.setFieldValue("items", (prev: EditableLineItem[]) => {
      return prev.map((itm) => {
        const vatMultiplier = new Decimal(1).plus(monetary.roundVatRate(rate).div(100));
        return {
          ...itm,
          vatRate: rate,
          priceNet: itm.priceGross > 0 ? monetary.toNumber(monetary.toDecimal(itm.priceGross).div(vatMultiplier)) : itm.priceNet,
        };
      });
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Edit Line Items</span>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={onCancel} disabled={isLoading}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button size="sm" type="submit" disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </CardTitle>
          <CardDescription>Add, edit, or remove invoice line items</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-20">Qty</TableHead>
                  <TableHead className="w-28">Net Price (€)</TableHead>
                  <TableHead className="w-28">Gross Price (€)</TableHead>
                  <TableHead className="w-20">VAT %</TableHead>
                  <TableHead className="w-28">Line Total</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {items.map((item: EditableLineItem, index: number) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Input value={item.name} onChange={(e) => updateItem(index, "name", e.target.value)} placeholder="Item name" className="min-w-32" />
                    </TableCell>
                    <TableCell>
                      <Input
                        value={item.description || ""}
                        onChange={(e) => updateItem(index, "description", e.target.value)}
                        placeholder="Description"
                        className="min-w-32"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, "quantity", parseInt(e.target.value) || 1)}
                        min="1"
                        className="w-20"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        step="0.01"
                        value={item.priceNet || ""}
                        onChange={(e) => updateItem(index, "priceNet", e.target.value ? parseFloat(e.target.value) : 0)}
                        min="0"
                        placeholder="0.00"
                        className="w-28"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        step="0.01"
                        value={item.priceGross}
                        onChange={(e) => updateItem(index, "priceGross", parseFloat(e.target.value) || 0)}
                        min="0"
                        className="w-28"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        step="0.1"
                        value={item.vatRate || ""}
                        onChange={(e) => updateItem(index, "vatRate", e.target.value ? parseFloat(e.target.value) : undefined)}
                        min="0"
                        max="100"
                        placeholder="0"
                        className="w-20"
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="text-right">
                        <div>{formatCurrency(monetary.toDecimal(item.priceGross).mul(item.quantity))}</div>
                        {item.priceNet && item.priceNet !== item.priceGross && (
                          <div className="text-xs text-muted-foreground">Net: {formatCurrency(monetary.toDecimal(item.priceNet || 0).mul(item.quantity))}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" onClick={() => removeItem(index)} className="text-destructive hover:text-destructive">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Controls Row */}
            <div className="flex flex-wrap items-center gap-4 justify-end">
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" type="button" onClick={addItem}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Global VAT %</span>
                  <Input
                    type="number"
                    step="0.1"
                    value={globalVat ?? ""}
                    onChange={(e) => updateGlobalVat(e.target.value === "" ? undefined : parseFloat(e.target.value))}
                    placeholder="0"
                    className="w-24"
                  />
                </div>
              </div>
            </div>

            {/* Totals Row */}
            <div className="text-right space-y-1 mt-4">
              <div className="text-sm text-muted-foreground">Net Total: {formatCurrency(totalNet)}</div>
              {totalVat > 0 && <div className="text-sm text-muted-foreground">VAT Total: {formatCurrency(totalVat)}</div>}
              <div className="text-lg font-semibold">Gross Total: {formatCurrency(totalGross)}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  );
}
