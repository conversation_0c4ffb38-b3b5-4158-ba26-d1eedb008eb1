import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ReconciliationStatus } from "@/prisma/generated";

interface ReconciliationStatusSelectProps {
  selectedStatus: ReconciliationStatus | undefined;
  onChange: (status: ReconciliationStatus | undefined) => void;
  placeholder?: string;
  className?: string;
}

const statusLabels: Record<ReconciliationStatus, string> = {
  [ReconciliationStatus.UNMATCHED]: "Unmatched",
  [ReconciliationStatus.AUTO_MATCHED]: "Auto Matched",
  [ReconciliationStatus.MANUALLY_MATCHED]: "Manually Matched",
  [ReconciliationStatus.MANUALLY_UNMATCHED]: "Manually Unmatched",
};

export function ReconciliationStatusSelect({
  selectedStatus,
  onChange,
  placeholder = "All statuses",
  className,
}: ReconciliationStatusSelectProps) {
  const handleValueChange = (value: string) => {
    if (value === "all") {
      onChange(undefined);
    } else {
      onChange(value as ReconciliationStatus);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">Reconciliation:</span>
      <Select
        value={selectedStatus || "all"}
        onValueChange={handleValueChange}
      >
        <SelectTrigger className={`w-40 ${className}`}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All statuses</SelectItem>
          {Object.entries(statusLabels).map(([status, label]) => (
            <SelectItem key={status} value={status}>
              {label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
