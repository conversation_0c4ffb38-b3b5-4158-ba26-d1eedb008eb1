import { trpc } from "@/utils/trpc";
import { Drawer, DrawerClose, DrawerContent, DrawerDes<PERSON>, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { X, ChevronLeft, ChevronRight, Wallet, Calendar, Activity, Copy, Check } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { AccountType } from "@/prisma/generated";
import { groupTransactionsByGroupId, getTransactionGroupDescription, getTransactionGroupAmount } from "@/modules/transactions/transactionUtils";
import { currency } from "@/modules/core/currency";
import { toast } from "sonner";
import * as React from "react";

interface AccountDetailsDrawerProps {
  accountId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNavigateNext?: () => void;
  onNavigatePrevious?: () => void;
  hasNext?: boolean;
  hasPrevious?: boolean;
}

const getAccountTypeColor = (type: AccountType) => {
  switch (type) {
    case "WALLET":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "BANK_ACCOUNT":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    case "EXCHANGE_ACCOUNT":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
    case "CREDIT_CARD":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  }
};

const getAccountTypeLabel = (type: AccountType) => {
  switch (type) {
    case "WALLET":
      return "Wallet";
    case "BANK_ACCOUNT":
      return "Bank Account";
    case "EXCHANGE_ACCOUNT":
      return "Exchange Account";
    case "CREDIT_CARD":
      return "Credit Card";
    default:
      return type;
  }
};

// Copy to clipboard component for full public key
const CopyableFullPublicKey = ({ publicKey }: { publicKey: string }) => {
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(publicKey);
      setCopied(true);
      toast.success("Public key copied to clipboard");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy public key");
    }
  };

  return (
    <div className="mt-1 p-3 bg-muted rounded-md">
      <div className="flex items-center justify-between gap-2">
        <p className="font-mono text-sm break-all select-all flex-1" title="Click to select all">
          {publicKey}
        </p>
        <Button
          variant="ghost"
          size="sm"
          onClick={copyToClipboard}
          className="h-8 w-8 p-0 flex-shrink-0"
          title="Copy public key"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-600" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  );
};

const getTransactionTypeLabel = (transaction: any) => {
  if (transaction.transfer) return "Transfer";
  if (transaction.trade) return "Trade";
  return "Unknown";
};

export function AccountDetailsDrawer({
  accountId,
  open,
  onOpenChange,
  onNavigateNext,
  onNavigatePrevious,
  hasNext = false,
  hasPrevious = false,
}: AccountDetailsDrawerProps) {
  const account = trpc.accounts.getById.useQuery(
    { id: accountId! },
    {
      enabled: !!accountId,
    }
  );

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "ArrowLeft" && hasPrevious && onNavigatePrevious) {
      onNavigatePrevious();
    } else if (event.key === "ArrowRight" && hasNext && onNavigateNext) {
      onNavigateNext();
    }
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="h-full !w-3/5 !max-w-none sm:!w-1/2 lg:!w-2/5" onKeyDown={handleKeyDown} tabIndex={0}>
        <DrawerHeader className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <DrawerTitle className="text-xl">Account Details</DrawerTitle>
              <DrawerDescription>View account information and transaction history</DrawerDescription>
            </div>
            <div className="flex items-center gap-2">
              {/* Navigation buttons */}
              {(hasNext || hasPrevious) && (
                <>
                  <Button variant="ghost" size="sm" onClick={onNavigatePrevious} disabled={!hasPrevious}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={onNavigateNext} disabled={!hasNext}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}
              <DrawerClose asChild>
                <Button variant="ghost" size="sm">
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>
          </div>
        </DrawerHeader>

        {account.isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading account details...</div>
          </div>
        ) : account.error ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-destructive">Failed to load account: {account.error.message}</div>
          </div>
        ) : account.data ? (
          <div className="space-y-6 mt-6 px-4 pb-4 overflow-y-auto text-selectable">
            {/* Account Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5" />
                  {account.data.name}
                </CardTitle>
                <CardDescription>
                  {account.data._count.transactions} transaction{account.data._count.transactions !== 1 ? "s" : ""} on record
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Account Type</label>
                    <div className="mt-1">
                      <Badge className={getAccountTypeColor(account.data.type)}>{getAccountTypeLabel(account.data.type)}</Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Transaction Count</label>
                    <p className="text-lg font-semibold">{account.data._count.transactions}</p>
                  </div>
                </div>

                {/* Public Key - only show for wallet accounts */}
                {account.data.type === "WALLET" && account.data.publicKey && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Public Key</label>
                    <CopyableFullPublicKey publicKey={account.data.publicKey} />
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                    <p className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      {formatDate(account.data.createdAt)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                    <p className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      {formatDate(account.data.updatedAt)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Transactions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Transaction History
                </CardTitle>
                <CardDescription>Recent transactions for this account</CardDescription>
              </CardHeader>
              <CardContent>
                {account.data.transactions.length > 0 ? (
                  <div className="overflow-hidden rounded-lg border">
                    <Table>
                      <TableHeader className="bg-muted">
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Transaction ID</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {account.data.transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>{formatDate(transaction.executedAt)}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{getTransactionTypeLabel(transaction)}</Badge>
                            </TableCell>
                            <TableCell className="font-mono text-sm">{transaction.id}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No transactions found for this account.</div>
                )}
              </CardContent>
            </Card>
          </div>
        ) : null}
      </DrawerContent>
    </Drawer>
  );
}
