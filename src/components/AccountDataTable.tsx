import * as React from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  SortingState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { IconEdit, IconTrash, IconEye, IconUpload, IconCopy, IconCheck, IconExternalLink } from "@tabler/icons-react";
import { AccountType } from "@/prisma/generated";
import { formatDate } from "@/lib/utils";
import { toast } from "sonner";

// Account type with transaction count
type AccountWithTransactions = {
  id: string;
  name: string;
  type: AccountType;
  publicKey?: string | null;
  createdAt: Date;
  updatedAt: Date;
  _count: {
    transactions: number;
  };
  transactions: Array<{
    id: string;
    executedAt: Date;
  }>;
};

interface AccountDataTableProps {
  data: AccountWithTransactions[];
  onViewAccount: (accountId: string) => void;
  onEditAccount: (accountId: string) => void;
  onDeleteAccount: (accountId: string) => void;
  onImportCSV: (accountId: string) => void;
}

const getAccountTypeColor = (type: AccountType) => {
  switch (type) {
    case "WALLET":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "BANK_ACCOUNT":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    case "EXCHANGE_ACCOUNT":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
    case "CREDIT_CARD":
      return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  }
};

const getAccountTypeLabel = (type: AccountType) => {
  switch (type) {
    case "WALLET":
      return "Wallet";
    case "BANK_ACCOUNT":
      return "Bank Account";
    case "EXCHANGE_ACCOUNT":
      return "Exchange Account";
    case "CREDIT_CARD":
      return "Credit Card";
    default:
      return type;
  }
};

// Copy to clipboard component
export const CopyablePublicKey = ({ publicKey }: { publicKey: string }) => {
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row click events
    try {
      await navigator.clipboard.writeText(publicKey);
      setCopied(true);
      toast.success("Public key copied to clipboard");
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy public key");
    }
  };

  const truncated = `${publicKey.slice(0, 6)}...${publicKey.slice(-4)}`;

  return (
    <div className="flex items-center gap-2">
      <span className="font-mono text-sm" title={publicKey}>
        {truncated}
      </span>
      <Button
        variant="ghost"
        size="sm"
        onClick={copyToClipboard}
        className="h-6 w-6 p-0 hover:bg-muted"
        title="Copy public key"
      >
        {copied ? (
          <IconCheck className="h-3 w-3 text-green-600" />
        ) : (
          <IconCopy className="h-3 w-3" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          window.open(`https://solscan.io/account/${publicKey}`, '_blank');
        }}
        className="h-6 w-6 p-0 hover:bg-muted"
        title="View on Solscan"
      >
        <IconExternalLink className="h-3 w-3" />
      </Button>
    </div>
  );
};

export function AccountDataTable({ data, onViewAccount, onEditAccount, onDeleteAccount, onImportCSV }: AccountDataTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  const columns: ColumnDef<AccountWithTransactions>[] = [
    {
      accessorKey: "name",
      header: "Account Name",
      cell: ({ row }) => {
        return (
          <Button variant="link" className="text-foreground w-fit px-0 text-left font-medium" onClick={() => onViewAccount(row.original.id)}>
            {row.original.name}
          </Button>
        );
      },
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => <Badge className={getAccountTypeColor(row.original.type)}>{getAccountTypeLabel(row.original.type)}</Badge>,
    },
    {
      accessorKey: "publicKey",
      header: "Public Key",
      cell: ({ row }) => {
        if (row.original.type !== "WALLET" || !row.original.publicKey) {
          return <span className="text-muted-foreground">—</span>;
        }
        return <CopyablePublicKey publicKey={row.original.publicKey} />;
      },
    },
    {
      accessorKey: "_count.transactions",
      header: "Transactions",
      cell: ({ row }) => (
        <div className="text-center">
          <span className="font-medium">{row.original._count.transactions}</span>
        </div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => <div className="text-muted-foreground">{formatDate(row.original.createdAt)}</div>,
    },
    {
      accessorKey: "updatedAt",
      header: "Last Updated",
      cell: ({ row }) => <div className="text-muted-foreground">{formatDate(row.original.updatedAt)}</div>,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2 [&>*]:cursor-pointer">
          <Button variant="ghost" size="sm" onClick={() => onViewAccount(row.original.id)} className="h-8 px-2">
            <IconEye className="h-4 w-4" />
            <span className="sr-only">View account</span>
          </Button>
          <Button variant="ghost" size="sm" onClick={() => onEditAccount(row.original.id)} className="h-8 px-2">
            <IconEdit className="h-4 w-4" />
            <span className="sr-only">Edit account</span>
          </Button>
          <Button variant="ghost" size="sm" onClick={() => onImportCSV(row.original.id)} className="h-8 px-2">
            <IconUpload className="h-4 w-4" />
            <span className="sr-only">Import CSV for account</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDeleteAccount(row.original.id)}
            className="h-8 px-2 text-destructive hover:text-destructive"
          >
            <IconTrash className="h-4 w-4" />
            <span className="sr-only">Delete account</span>
          </Button>
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });

  return (
    <div className="space-y-4">
      <div className="overflow-hidden rounded-lg border">
        <Table>
          <TableHeader className="bg-muted">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return <TableHead key={header.id}>{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}</TableHead>;
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No accounts found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-4">
        <div className="text-muted-foreground text-sm">
          Showing {table.getRowModel().rows.length} of {data.length} account(s)
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => table.previousPage()} disabled={!table.getCanPreviousPage()}>
            Previous
          </Button>
          <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
