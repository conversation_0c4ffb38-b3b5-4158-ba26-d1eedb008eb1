import * as React from "react"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface AmountRange {
  min?: number
  max?: number
}

interface AmountRangeFilterProps {
  amountRange?: AmountRange
  onAmountRangeChange?: (amountRange: AmountRange | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  currency?: string
}

export function AmountRangeFilter({
  amountRange,
  onAmountRangeChange,
  placeholder = "USD amount range",
  disabled = false,
  className,
  currency = "$",
}: AmountRangeFilterProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [localMin, setLocalMin] = React.useState(amountRange?.min?.toString() || "")
  const [localMax, setLocalMax] = React.useState(amountRange?.max?.toString() || "")

  // Update local values when external values change
  React.useEffect(() => {
    setLocalMin(amountRange?.min?.toString() || "")
    setLocalMax(amountRange?.max?.toString() || "")
  }, [amountRange])

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    setLocalMin("")
    setLocalMax("")
    onAmountRangeChange?.(undefined)
  }

  const handleApply = () => {
    const min = localMin ? parseFloat(localMin) : undefined
    const max = localMax ? parseFloat(localMax) : undefined
    
    if (min !== undefined || max !== undefined) {
      onAmountRangeChange?.({ min, max })
    } else {
      onAmountRangeChange?.(undefined)
    }
    setIsOpen(false)
  }

  const formatAmountRange = (range?: AmountRange) => {
    if (!range?.min && !range?.max) return placeholder
    if (range.min !== undefined && range.max !== undefined) {
      return `${currency}${range.min} - ${currency}${range.max}`
    }
    if (range.min !== undefined) {
      return `≥ ${currency}${range.min}`
    }
    if (range.max !== undefined) {
      return `≤ ${currency}${range.max}`
    }
    return placeholder
  }

  const hasValue = amountRange?.min !== undefined || amountRange?.max !== undefined

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !hasValue && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <span className="flex-1 truncate">{formatAmountRange(amountRange)}</span>
          {hasValue && (
            <X 
              className="ml-2 h-4 w-4 hover:text-destructive" 
              onClick={handleClear}
            />
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="start">
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">USD Amount Range</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Input
                  type="number"
                  placeholder="Min USD amount"
                  value={localMin}
                  onChange={(e) => setLocalMin(e.target.value)}
                  step="0.01"
                />
              </div>
              <div>
                <Input
                  type="number"
                  placeholder="Max USD amount"
                  value={localMax}
                  onChange={(e) => setLocalMax(e.target.value)}
                  step="0.01"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setLocalMin("")
                setLocalMax("")
              }}
            >
              Clear
            </Button>
            <Button size="sm" onClick={handleApply}>
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
