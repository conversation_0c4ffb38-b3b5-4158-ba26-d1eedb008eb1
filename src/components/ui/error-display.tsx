import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>gle, XCircle, RefreshCw, Info } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { TRPCClientError } from "@trpc/client";
import { AppRouter } from "@/server/routers/_app";

interface ErrorDisplayProps {
  error: Error | TRPCClientError<AppRouter> | string;
  title?: string;
  variant?: "default" | "destructive" | "warning";
  size?: "sm" | "md" | "lg";
  showRetry?: boolean;
  onRetry?: () => void;
  className?: string;
}

export function ErrorDisplay({ error, title, variant = "destructive", size = "md", showRetry = false, onRetry, className }: ErrorDisplayProps) {
  const errorMessage = typeof error === "string" ? error : error.message;
  const isClientError = error instanceof TRPCClientError;

  const getIcon = () => {
    switch (variant) {
      case "warning":
        return <AlertTriangle className="h-4 w-4" />;
      case "destructive":
        return <XCircle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getTitle = () => {
    if (title) return title;

    if (isClientError) {
      const clientError = error as TRPCClientError<AppRouter>;
      switch (clientError.data?.code) {
        case "UNAUTHORIZED":
          return "Authentication Required";
        case "FORBIDDEN":
          return "Access Denied";
        case "NOT_FOUND":
          return "Not Found";
        case "TIMEOUT":
          return "Request Timeout";
        case "TOO_MANY_REQUESTS":
          return "Too Many Requests";
        case "INTERNAL_SERVER_ERROR":
          return "Server Error";
        default:
          return "Error";
      }
    }

    return variant === "warning" ? "Warning" : "Error";
  };

  if (size === "sm") {
    return (
      <Alert variant={variant} className={cn("text-sm", className)}>
        {getIcon()}
        <AlertDescription className="flex items-center justify-between">
          <span>{errorMessage}</span>
          {showRetry && onRetry && (
            <Button variant="ghost" size="sm" onClick={onRetry} className="ml-2 h-6 px-2">
              <RefreshCw className="h-3 w-3" />
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  if (size === "lg") {
    return (
      <Card className={cn("border-destructive/50", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-destructive">
            {getIcon()}
            {getTitle()}
          </CardTitle>
          <CardDescription>{errorMessage}</CardDescription>
        </CardHeader>
        {showRetry && onRetry && (
          <CardContent className="pt-0">
            <Button onClick={onRetry} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </CardContent>
        )}
      </Card>
    );
  }

  // Default medium size
  return (
    <Alert variant={variant} className={className}>
      {getIcon()}
      <AlertTitle>{getTitle()}</AlertTitle>
      <AlertDescription className="flex items-center justify-between">
        <span>{errorMessage}</span>
        {showRetry && onRetry && (
          <Button variant="ghost" size="sm" onClick={onRetry} className="ml-2">
            <RefreshCw className="h-4 w-4" />
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}

interface QueryErrorDisplayProps {
  error: any;
  refetch?: () => void;
  className?: string;
}

export function QueryErrorDisplay({ error, refetch, className }: QueryErrorDisplayProps) {
  return <ErrorDisplay error={error} showRetry={!!refetch} onRetry={refetch} className={className} />;
}

interface MutationErrorDisplayProps {
  error: any;
  retry?: () => void;
  className?: string;
}

export function MutationErrorDisplay({ error, retry, className }: MutationErrorDisplayProps) {
  return <ErrorDisplay error={error} showRetry={!!retry} onRetry={retry} size="sm" className={className} />;
}

interface LoadingErrorProps {
  message?: string;
  onRetry?: () => void;
  className?: string;
}

export function LoadingError({ message = "Failed to load data", onRetry, className }: LoadingErrorProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center p-8 text-center", className)}>
      <XCircle className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
      <p className="text-muted-foreground mb-4">{message}</p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  );
}
