import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface CurrencyMultiSelectProps {
  currencies: string[];
  selected: string[];
  onChange: (selected: string[]) => void;
  className?: string;
}

export function CurrencyMultiSelect({ currencies, selected, onChange, className }: CurrencyMultiSelectProps) {
  const toggle = (code: string) => {
    if (selected.includes(code)) {
      onChange(selected.filter((c) => c !== code));
    } else {
      onChange([...selected, code]);
    }
  };

  const buttonLabel = () => {
    if (selected.length === 0) return "Filter by Currency";
    if (selected.length === 1) return selected[0];
    return `${selected.length} currencies`;
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className={cn("justify-start", className)}>
          {buttonLabel()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 p-2">
        <div className="max-h-60 overflow-y-auto">
          {currencies.map((code) => (
            <label key={code} className="flex items-center gap-2 py-1 cursor-pointer">
              <Checkbox checked={selected.includes(code)} onCheckedChange={() => toggle(code)} />
              <span className="text-sm">{code}</span>
            </label>
          ))}
        </div>
        <Separator className="my-2" />
        <div className="flex justify-end gap-2">
          <Button variant="ghost" size="sm" onClick={() => onChange([])}>
            Clear
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
