import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface YearSelectProps {
  years: number[];
  selectedYear: number | undefined;
  onChange: (year: number | undefined) => void;
  placeholder?: string;
  className?: string;
}

export function YearSelect({
  years,
  selectedYear,
  onChange,
  placeholder = "All years",
  className,
}: YearSelectProps) {
  const handleValueChange = (value: string) => {
    if (value === "all") {
      onChange(undefined);
    } else {
      onChange(parseInt(value, 10));
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">Year:</span>
      <Select
        value={selectedYear ? selectedYear.toString() : "all"}
        onValueChange={handleValueChange}
      >
        <SelectTrigger className={`w-32 ${className}`}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All years</SelectItem>
          {years.map((year) => (
            <SelectItem key={year} value={year.toString()}>
              {year}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
