import React, { useState } from "react";
import { trpc } from "@/utils/trpc";
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Calendar, Activity, ArrowUpDown, TrendingUp, Wallet, Copy, Check, ExternalLink } from "lucide-react";
import { currency } from "@/modules/core/currency";
import { AccountType } from "@/prisma/generated";

interface TransactionDetailsDrawerProps {
  transactionId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNavigateNext?: () => void;
  onNavigatePrevious?: () => void;
  hasNext?: boolean;
  hasPrevious?: boolean;
}

export function TransactionDetailsDrawer({
  transactionId,
  open,
  onOpenChange,
  onNavigateNext,
  onNavigatePrevious,
  hasNext,
  hasPrevious,
}: TransactionDetailsDrawerProps) {
  const [copiedId, setCopiedId] = useState<string | null>(null);

  // Fetch transaction details
  const { data: transaction, isLoading, error } = trpc.transactions.getById.useQuery(
    { id: transactionId! },
    { enabled: !!transactionId }
  );

  // Fetch related transactions if part of a group
  const { data: groupTransactions } = trpc.transactions.getByGroupId.useQuery(
    { transactionGroupId: transaction?.transactionGroupId! },
    { enabled: !!transaction?.transactionGroupId }
  );

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("de-DE", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getAccountTypeLabel = (type: AccountType) => {
    switch (type) {
      case "WALLET":
        return "Wallet";
      case "BANK_ACCOUNT":
        return "Bank Account";
      case "EXCHANGE_ACCOUNT":
        return "Exchange Account";
      case "CREDIT_CARD":
        return "Credit Card";
      default:
        return type;
    }
  };

  const getTransactionTypeIcon = (transaction: any) => {
    if (transaction.transfer) return <ArrowUpDown className="h-5 w-5 text-blue-600" />;
    if (transaction.trade) return <TrendingUp className="h-5 w-5 text-green-600" />;
    if (transaction.cryptoExpense) return <Wallet className="h-5 w-5 text-purple-600" />;
    return <Activity className="h-5 w-5 text-gray-600" />;
  };

  const getTransactionTypeLabel = (transaction: any) => {
    if (transaction.transfer) return "Transfer";
    if (transaction.trade) return "Trade";
    if (transaction.cryptoExpense) return "Crypto Expense";
    return "Unknown";
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="h-full !max-w-none !w-3/5 sm:!w-1/2 lg:!w-2/5">
        <DrawerHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <DrawerTitle className="text-xl">Transaction Details</DrawerTitle>
                <DrawerDescription>
                  {transaction ? `Transaction ${transaction.id.slice(0, 8)}...` : "Loading transaction details..."}
                </DrawerDescription>
              </div>
            </div>

            {/* Navigation Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onNavigatePrevious}
                disabled={!hasPrevious}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onNavigateNext}
                disabled={!hasNext}
                className="flex items-center gap-1"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-y-auto p-6 space-y-6 text-selectable">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Loading transaction details...</div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center py-8">
              <div className="text-destructive">Failed to load transaction: {error.message}</div>
            </div>
          )}

          {transaction && (
            <>
              {/* Transaction Header */}
              <Card>
                <CardContent className="space-y-6">
                  {/* Date & Type Row */}
                  <div className="flex flex-col sm:flex-row sm:items-start gap-6">
                    {/* Date */}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Date</label>
                      <p className="flex items-center gap-2 text-lg font-semibold">
                        <Calendar className="h-5 w-5 text-blue-600" />
                        {formatDate(transaction.executedAt)}
                      </p>
                    </div>

                    {/* Type */}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Type</label>
                      <p className="flex items-center gap-2 text-lg font-semibold">
                        {getTransactionTypeIcon(transaction)}
                        {getTransactionTypeLabel(transaction)}
                      </p>
                    </div>
                  </div>

                  {/* Account */}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Account</label>
                    <div className="mt-1">
                      <p className="text-lg font-semibold">{transaction.account?.name || "Unknown Account"}</p>
                      <p className="text-sm text-muted-foreground">
                        {getAccountTypeLabel(transaction.account?.type || "WALLET")}
                      </p>
                    </div>
                  </div>

                  {/* Transaction ID */}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Transaction ID</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="text-sm bg-muted px-2 py-1 rounded font-mono">{transaction.id}</code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(transaction.id, "transaction-id")}
                        className="h-8 w-8 p-0"
                      >
                        {copiedId === "transaction-id" ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Group ID */}
                  {transaction.transactionGroupId && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Group ID</label>
                      <div className="flex items-center gap-2 mt-1">
                        <code className="text-sm bg-muted px-2 py-1 rounded font-mono">{transaction.transactionGroupId}</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(transaction.transactionGroupId!, "group-id")}
                          className="h-8 w-8 p-0"
                        >
                          {copiedId === "group-id" ? (
                            <Check className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Transfer Details */}
              {transaction.transfer && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ArrowUpDown className="h-5 w-5" />
                      Transfer Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Amount</label>
                        <p className="text-lg font-semibold">
                          {currency.formatMonetary(transaction.transfer.amount, transaction.transfer.currencyCode)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Currency</label>
                        <p className="text-lg font-semibold">{transaction.transfer.currencyCode}</p>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Counterparty</label>
                      <p className="text-lg font-semibold">{transaction.transfer.counterparty}</p>
                    </div>

                    {transaction.transfer.description && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Description</label>
                        <p className="text-lg">{transaction.transfer.description}</p>
                      </div>
                    )}

                    {/* Reconciliation Status */}
                    {transaction.transfer.reconciliations && transaction.transfer.reconciliations.length > 0 && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Reconciliation</label>
                        <div className="mt-2 space-y-2">
                          {transaction.transfer.reconciliations.map((reconciliation) => (
                            <div key={reconciliation.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                              <div>
                                <p className="font-medium">
                                  Invoice: {reconciliation.invoice?.invoiceReference || "Unknown"}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {reconciliation.invoice?.vendor?.name || "Unknown Vendor"}
                                </p>
                              </div>
                              <div className="text-right">
                                <Badge variant={reconciliation.status === "AUTO_MATCHED" ? "default" : "secondary"}>
                                  {reconciliation.status.replace("_", " ")}
                                </Badge>
                                {reconciliation.confidenceScore && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    Confidence: {reconciliation.confidenceScore}%
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Trade Details */}
              {transaction.trade && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Trade Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">From</label>
                        <p className="text-lg font-semibold">
                          {currency.formatMonetary(transaction.trade.amountFrom, transaction.trade.tokenFrom)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">To</label>
                        <p className="text-lg font-semibold">
                          {currency.formatMonetary(transaction.trade.amountTo, transaction.trade.tokenTo)}
                        </p>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Pool ID</label>
                      <p className="text-lg font-semibold">{transaction.trade.poolId}</p>
                    </div>

                    {transaction.trade.description && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Description</label>
                        <p className="text-lg">{transaction.trade.description}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Crypto Expense Details */}
              {transaction.cryptoExpense && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Wallet className="h-5 w-5" />
                      Crypto Expense Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Type</label>
                        <p className="text-lg font-semibold">{transaction.cryptoExpense.type}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Amount Spent (EUR)</label>
                        <p className="text-lg font-semibold text-red-600">
                          {currency.formatMonetary(Number(transaction.cryptoExpense.amountUsd), "EUR")}
                        </p>
                      </div>
                    </div>

                    {/* Buyback-specific details */}
                    {transaction.cryptoExpense.type === "BUYBACK" && transaction.cryptoExpense.buyback && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">From Token</label>
                          <p className="text-lg font-semibold">
                            {currency.formatMonetary(Number(transaction.cryptoExpense.buyback.swapFromAmount), transaction.cryptoExpense.buyback.swapFromSymbol)}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">To Token</label>
                          <p className="text-lg font-semibold">
                            {currency.formatMonetary(Number(transaction.cryptoExpense.buyback.swapToAmount), transaction.cryptoExpense.buyback.swapToSymbol)}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Floor Sweep-specific details */}
                    {transaction.cryptoExpense.type === "FLOOR_SWEEP" && transaction.cryptoExpense.floorSweep && (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Collection</label>
                            <p className="text-lg font-semibold">
                              {transaction.cryptoExpense.floorSweep.collectionName || transaction.cryptoExpense.floorSweep.collectionId}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Price (USD)</label>
                            <p className="text-lg font-semibold">
                              {currency.formatMonetary(Number(transaction.cryptoExpense.floorSweep.priceUsd), "USD")}
                            </p>
                          </div>
                        </div>

                        {transaction.cryptoExpense.floorSweep.tokenId && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Token ID</label>
                            <p className="text-lg font-semibold">{transaction.cryptoExpense.floorSweep.tokenId}</p>
                          </div>
                        )}

                        {transaction.cryptoExpense.floorSweep.mintAddress && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">NFT Mint Address</label>
                            <div className="flex items-center gap-2 mt-1">
                              <div className="flex-1 p-3 bg-muted rounded-md">
                                <code className="text-sm font-mono break-all select-all">
                                  {transaction.cryptoExpense.floorSweep.mintAddress}
                                </code>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(transaction.cryptoExpense!.floorSweep!.mintAddress!, "nft-mint")}
                                className="h-8 w-8 p-0 flex-shrink-0"
                                title="Copy NFT mint address"
                              >
                                {copiedId === "nft-mint" ? (
                                  <Check className="h-4 w-4 text-green-600" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        )}
                      </>
                    )}

                    {/* Liquidity Pool-specific details */}
                    {transaction.cryptoExpense.type === "LIQUIDITY_POOL" && transaction.cryptoExpense.liquidityPool && (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Action</label>
                            <p className="text-lg font-semibold">{transaction.cryptoExpense.liquidityPool.action}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Token Pair</label>
                            <p className="text-lg font-semibold">
                              {transaction.cryptoExpense.liquidityPool.tokenASymbol}/{transaction.cryptoExpense.liquidityPool.tokenBSymbol}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Token A Amount</label>
                            <p className="text-lg font-semibold">
                              {currency.formatMonetary(Number(transaction.cryptoExpense.liquidityPool.tokenAAmount), transaction.cryptoExpense.liquidityPool.tokenASymbol)}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {currency.formatMonetary(Number(transaction.cryptoExpense.liquidityPool.tokenAAmountUsd), "USD")}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Token B Amount</label>
                            <p className="text-lg font-semibold">
                              {currency.formatMonetary(Number(transaction.cryptoExpense.liquidityPool.tokenBAmount), transaction.cryptoExpense.liquidityPool.tokenBSymbol)}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {currency.formatMonetary(Number(transaction.cryptoExpense.liquidityPool.tokenBAmountUsd), "USD")}
                            </p>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Pool Address</label>
                          <div className="flex items-center gap-2 mt-1">
                            <div className="flex-1 p-3 bg-muted rounded-md">
                              <code className="text-sm font-mono break-all select-all">
                                {transaction.cryptoExpense.liquidityPool.poolAddress}
                              </code>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(transaction.cryptoExpense!.liquidityPool!.poolAddress, "pool-address")}
                              className="h-8 w-8 p-0 flex-shrink-0"
                              title="Copy pool address"
                            >
                              {copiedId === "pool-address" ? (
                                <Check className="h-4 w-4 text-green-600" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Token A Mint</label>
                            <div className="flex items-center gap-2 mt-1">
                              <code className="text-sm bg-muted px-2 py-1 rounded font-mono flex-1 break-all">
                                {transaction.cryptoExpense.liquidityPool.tokenAMint}
                              </code>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(transaction.cryptoExpense!.liquidityPool!.tokenAMint, "token-a-mint")}
                                className="h-8 w-8 p-0 flex-shrink-0"
                                title="Copy Token A mint"
                              >
                                {copiedId === "token-a-mint" ? (
                                  <Check className="h-4 w-4 text-green-600" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Token B Mint</label>
                            <div className="flex items-center gap-2 mt-1">
                              <code className="text-sm bg-muted px-2 py-1 rounded font-mono flex-1 break-all">
                                {transaction.cryptoExpense.liquidityPool.tokenBMint}
                              </code>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(transaction.cryptoExpense!.liquidityPool!.tokenBMint, "token-b-mint")}
                                className="h-8 w-8 p-0 flex-shrink-0"
                                title="Copy Token B mint"
                              >
                                {copiedId === "token-b-mint" ? (
                                  <Check className="h-4 w-4 text-green-600" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </>
                    )}

                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Transaction ID</label>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex-1 p-3 bg-muted rounded-md">
                          <code className="text-sm font-mono break-all select-all">{transaction.cryptoExpense.txId}</code>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(transaction.cryptoExpense!.txId, "crypto-tx-id")}
                          className="h-8 w-8 p-0 flex-shrink-0"
                          title="Copy transaction ID"
                        >
                          {copiedId === "crypto-tx-id" ? (
                            <Check className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(`https://solscan.io/tx/${transaction.cryptoExpense!.txId}`, '_blank')}
                          className="h-8 w-8 p-0 flex-shrink-0"
                          title="View on Solscan"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Block Timestamp</label>
                      <p className="text-lg">{new Date(transaction.cryptoExpense.blockTimestamp).toLocaleString()}</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Group Transactions */}
              {transaction.transactionGroupId && groupTransactions && groupTransactions.length > 1 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Wallet className="h-5 w-5" />
                      Related Transactions ({groupTransactions.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {groupTransactions.map((groupTx) => (
                        <div
                          key={groupTx.id}
                          className={`p-3 rounded-lg border ${
                            groupTx.id === transaction.id ? "bg-primary/10 border-primary" : "bg-muted"
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">
                                {groupTx.transfer ? "Transfer" : groupTx.trade ? "Trade" : "Unknown"}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {groupTx.executedAt.toLocaleDateString("de-DE")}
                              </p>
                            </div>
                            <div className="text-right">
                              {groupTx.transfer && (
                                <p className="font-medium">
                                  {currency.formatMonetary(groupTx.transfer.amount, groupTx.transfer.currencyCode)}
                                </p>
                              )}
                              {groupTx.trade && (
                                <p className="font-medium text-sm">
                                  {groupTx.trade.tokenFrom} → {groupTx.trade.tokenTo}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Metadata */}
              {transaction.metadata && (
                <Card>
                  <CardHeader>
                    <CardTitle>Metadata</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto">
                      {JSON.stringify(transaction.metadata, null, 2)}
                    </pre>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
}
