import * as React from "react";
import {
  IconDashboard,
  IconFileDescription,
  IconHelp,
  IconInnerShadowTop,
  IconSearch,
  IconSettings,
  IconUsers,
  IconRobot,
  IconWallet,
  IconUpload,
  IconLink,
  IconCreditCard,
} from "@tabler/icons-react";

import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavUser } from "@/components/nav-user";
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";

const data = {
  user: {
    name: "SAC Accounting",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: IconDashboard,
    },
    {
      title: "Invoices",
      url: "/invoices",
      icon: IconFileDescription,
    },
    {
      title: "Transactions",
      url: "/transactions",
      icon: IconCreditCard,
    },

    {
      title: "Accounts",
      url: "/accounts",
      icon: IconWallet,
    },

    {
      title: "Reconciliation",
      url: "/reconciliation",
      icon: IconLink,
    },
    {
      title: "Vendors",
      url: "/vendors",
      icon: IconUsers,
    },
    // {
    //   title: "CSV Import",
    //   url: "/csv-import",
    //   icon: IconUpload,
    // },
    {
      title: "AI Assistant",
      url: "/chat",
      icon: IconRobot,
    },
  ],
  navSecondary: [
    {
      title: "Settings",
      url: "/settings",
      icon: IconSettings,
    },
    {
      title: "Help",
      url: "#",
      icon: IconHelp,
    },
    {
      title: "Search",
      url: "#",
      icon: IconSearch,
    },
  ],
};

export function AppSidebar({ currentPath, ...props }: React.ComponentProps<typeof Sidebar> & { currentPath?: string }) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <a href="/">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">SAC Accounting</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} currentPath={currentPath} />
        <NavSecondary items={data.navSecondary} currentPath={currentPath} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <p className="m-4 text-xs text-gray-500 ml-auto">Version 0.1.0</p>
    </Sidebar>
  );
}
