import React from "react";
import { useForm } from "@tanstack/react-form";
import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AccountType } from "@/prisma/generated";
import { toast } from "sonner";

interface AccountFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountId?: string;
  onSuccess?: () => void;
}

const getAccountTypeLabel = (type: AccountType) => {
  switch (type) {
    case "WALLET":
      return "Wallet";
    case "BANK_ACCOUNT":
      return "Bank Account";
    case "EXCHANGE_ACCOUNT":
      return "Exchange Account";
    case "CREDIT_CARD":
      return "Credit Card";
    default:
      return type;
  }
};

export function AccountForm({ open, onOpenChange, accountId, onSuccess }: AccountFormProps) {
  const utils = trpc.useUtils();
  const isEditing = !!accountId;

  // Fetch account data if editing
  const { data: accountData } = trpc.accounts.getById.useQuery({ id: accountId! }, { enabled: isEditing });

  const form = useForm({
    defaultValues: {
      name: "",
      type: "CREDIT_CARD" as AccountType,
      publicKey: "",
    },
    onSubmit: async ({ value }) => {
      if (isEditing) {
        updateMutation.mutate({
          id: accountId!,
          name: value.name,
          type: value.type,
          publicKey: value.publicKey || undefined,
        });
      } else {
        createMutation.mutate({
          name: value.name,
          type: value.type,
          publicKey: value.publicKey || undefined,
        });
      }
    },
  });

  // Update form when account data is loaded
  React.useEffect(() => {
    if (accountData && isEditing) {
      form.setFieldValue("name", accountData.name);
      form.setFieldValue("type", accountData.type);
      form.setFieldValue("publicKey", accountData.publicKey || "");
    } else if (!isEditing) {
      form.setFieldValue("name", "");
      form.setFieldValue("type", "CREDIT_CARD" as AccountType);
      form.setFieldValue("publicKey", "");
    }
  }, [accountData, isEditing, form]);

  const createMutation = trpc.accounts.create.useMutation({
    onSuccess: () => {
      toast.success("Account created successfully");
      utils.accounts.getAll.invalidate();
      utils.accounts.getStats.invalidate();
      onOpenChange(false);
      form.reset();
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(`Failed to create account: ${error.message}`);
    },
  });

  const updateMutation = trpc.accounts.update.useMutation({
    onSuccess: () => {
      toast.success("Account updated successfully");
      utils.accounts.getAll.invalidate();
      utils.accounts.getById.invalidate({ id: accountId! });
      utils.accounts.getStats.invalidate();
      onOpenChange(false);
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(`Failed to update account: ${error.message}`);
    },
  });

  const isLoading = createMutation.isPending || updateMutation.isPending;

  console.log(form.state.values);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account" : "Create New Account"}</DialogTitle>
          <DialogDescription>{isEditing ? "Update the account information below." : "Add a new account to track transactions."}</DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-4"
        >
          <form.Field
            name="name"
            validators={{
              onChange: ({ value }) => (!value || value.length < 1 ? "Account name is required" : value.length > 255 ? "Account name too long" : undefined),
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Account Name</Label>
                <Input
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Enter account name"
                  disabled={isLoading}
                />
                {field.state.meta.errors.length > 0 && <p className="text-sm font-medium text-destructive">{field.state.meta.errors[0]}</p>}
              </div>
            )}
          </form.Field>

          <form.Field
            name="type"
            validators={{
              onChange: ({ value }) => (!value ? "Please select an account type" : undefined),
            }}
          >
            {(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Account Type</Label>
                <Select value={field.state.value} onValueChange={(value) => field.handleChange(value as AccountType)} disabled={isLoading}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select account type" />
                  </SelectTrigger>
                  <SelectContent>
                    {(Object.values(AccountType) as AccountType[]).map((type) => (
                      <SelectItem key={type} value={type}>
                        {getAccountTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {field.state.meta.errors.length > 0 && <p className="text-sm font-medium text-destructive">{field.state.meta.errors[0]}</p>}
              </div>
            )}
          </form.Field>

          {/* Public Key field - only show for WALLET type */}
          <form.Subscribe
            selector={(state) => [state.values.type]}
          >
            {([type]) =>
              type === "WALLET" && (
                <form.Field
                  name="publicKey"
                  validators={{
                    onChange: ({ value }) => {
                      if (!value || value.length === 0) {
                        return "Public key is required for wallet accounts";
                      }
                      // Basic Solana public key validation
                      const solanaPublicKeyRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
                      if (!solanaPublicKeyRegex.test(value)) {
                        return "Invalid public key format";
                      }
                      return undefined;
                    },
                  }}
                >
                  {(field) => (
                    <div className="space-y-2">
                      <Label htmlFor={field.name}>Public Key</Label>
                      <Input
                        id={field.name}
                        name={field.name}
                        value={field.state.value}
                        onBlur={field.handleBlur}
                        onChange={(e) => field.handleChange(e.target.value)}
                        placeholder="Enter wallet public key (e.g., Solana address)"
                        disabled={isLoading}
                        className="font-mono text-sm"
                      />
                      {field.state.meta.errors.length > 0 && <p className="text-sm font-medium text-destructive">{field.state.meta.errors[0]}</p>}
                      <p className="text-xs text-muted-foreground">
                        Enter the public key/address for this wallet (32-44 characters)
                      </p>
                    </div>
                  )}
                </form.Field>
              )
            }
          </form.Subscribe>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : isEditing ? "Update Account" : "Create Account"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
