import * as React from "react"
import { <PERSON><PERSON><PERSON>Down, <PERSON>Up, ArrowDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DateRangePicker, DateRange } from "@/components/ui/date-range-picker"
import { AmountRangeFilter, AmountRange } from "@/components/ui/amount-range-filter"
import { VendorFilter, Vendor } from "@/components/ui/account-filter"
import { SearchInput } from "@/components/SearchInput"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ReconciliationStatus } from "@/prisma/generated"

export interface SortConfig {
  field: string
  direction: "asc" | "desc" | null
}

interface InvoiceTableHeaderProps {
  // Filter props
  dateRange?: DateRange
  onDateRangeChange?: (dateRange: DateRange | undefined) => void
  
  amountRange?: AmountRange
  onAmountRangeChange?: (amountRange: AmountRange | undefined) => void
  
  vendors: Vendor[]
  selectedVendorId?: string
  onVendorChange?: (vendorId: string | undefined) => void
  
  selectedReconciliationStatus?: ReconciliationStatus
  onReconciliationStatusChange?: (status: ReconciliationStatus | undefined) => void
  
  searchTerm: string
  onSearchChange: (search: string) => void
  
  // Sort props
  sortConfig?: SortConfig
  onSortChange?: (sort: SortConfig) => void
}

export function InvoiceTableHeader({
  dateRange,
  onDateRangeChange,
  amountRange,
  onAmountRangeChange,
  vendors,
  selectedVendorId,
  onVendorChange,
  selectedReconciliationStatus,
  onReconciliationStatusChange,
  searchTerm,
  onSearchChange,
  sortConfig,
  onSortChange,
}: InvoiceTableHeaderProps) {
  
  const handleSort = (field: string) => {
    if (!onSortChange) return
    
    let direction: "asc" | "desc" | null = "asc"
    
    if (sortConfig?.field === field) {
      if (sortConfig.direction === "asc") {
        direction = "desc"
      } else if (sortConfig.direction === "desc") {
        direction = null
      }
    }
    
    onSortChange({ field, direction })
  }

  const getSortIcon = (field: string) => {
    if (sortConfig?.field !== field || !sortConfig.direction) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />
    }
    
    return sortConfig.direction === "asc" 
      ? <ArrowUp className="ml-2 h-4 w-4" />
      : <ArrowDown className="ml-2 h-4 w-4" />
  }

  return (
    <>
      {/* Filter Row */}
      <TableRow className="border-b-2">
        <TableHead className="p-2">
          <DateRangePicker
            dateRange={dateRange}
            onDateRangeChange={onDateRangeChange}
            placeholder="Filter by date"
            className="w-full"
          />
        </TableHead>
        <TableHead className="p-2">
          <VendorFilter
            vendors={vendors}
            selectedVendorId={selectedVendorId}
            onVendorChange={onVendorChange}
            placeholder="All vendors"
            className="w-full"
          />
        </TableHead>
        <TableHead className="p-2">
          <AmountRangeFilter
            amountRange={amountRange}
            onAmountRangeChange={onAmountRangeChange}
            placeholder="Filter by amount"
            className="w-full"
          />
        </TableHead>
        <TableHead className="p-2">
          {/* Source Amount column - no filter for now */}
        </TableHead>
        <TableHead className="p-2">
          {/* VAT column - no filter for now */}
        </TableHead>
        <TableHead className="p-2">
          {/* Import Type column - no filter for now */}
        </TableHead>
        <TableHead className="p-2">
          <SearchInput
            value={searchTerm}
            onChange={onSearchChange}
            placeholder="Search invoice ref..."
            className="w-full"
          />
        </TableHead>
        <TableHead className="p-2">
          <Select 
            value={selectedReconciliationStatus || "all"} 
            onValueChange={(value) => onReconciliationStatusChange?.(value === "all" ? undefined : value as ReconciliationStatus)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="MATCHED">Matched</SelectItem>
              <SelectItem value="UNMATCHED">Unmatched</SelectItem>
            </SelectContent>
          </Select>
        </TableHead>
        <TableHead className="p-2">
          {/* Actions column - no filter */}
        </TableHead>
      </TableRow>

      {/* Header Row with Sort */}
      <TableRow>
        <TableHead>
          <Button
            variant="ghost"
            onClick={() => handleSort("date")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Date
            {getSortIcon("date")}
          </Button>
        </TableHead>
        <TableHead>
          <Button
            variant="ghost"
            onClick={() => handleSort("vendor")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Vendor
            {getSortIcon("vendor")}
          </Button>
        </TableHead>
        <TableHead>
          <Button
            variant="ghost"
            onClick={() => handleSort("amount")}
            className="h-auto p-0 font-semibold hover:bg-transparent"
          >
            Amount
            {getSortIcon("amount")}
          </Button>
        </TableHead>
        <TableHead>Source Amount</TableHead>
        <TableHead>VAT</TableHead>
        <TableHead>Import Type</TableHead>
        <TableHead>Invoice Ref</TableHead>
        <TableHead>Reconciliation</TableHead>
        <TableHead className="text-right">Actions</TableHead>
      </TableRow>
    </>
  )
}
