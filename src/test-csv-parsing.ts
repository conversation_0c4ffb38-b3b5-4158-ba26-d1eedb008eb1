import { csvUtils } from "./modules/core/utils/csvUtils";
import { binanceUtils } from "./modules/core/utils/binanceUtils";
import fs from "fs";
import path from "path";

async function testBinanceCSVParsing() {
  try {
    // Read the actual Binance CSV file
    const csvPath = path.join(__dirname, "../data-ai/binance_card_2022 - sheet1.csv");
    const csvContent = fs.readFileSync(csvPath, "utf-8");

    console.log("🔍 Testing CSV parsing...");
    
    // Test basic CSV parsing
    const parseResult = csvUtils.parseCSV(csvContent);
    if (parseResult.isErr()) {
      console.error("❌ CSV parsing failed:", parseResult.error);
      return;
    }

    const parsed = parseResult.value;
    console.log(`✅ CSV parsed successfully: ${parsed.rowCount} rows, ${parsed.headers.length} columns`);
    console.log("📋 Headers:", parsed.headers);

    // Test Binance-specific parsing
    const binanceResult = csvUtils.parseBinanceCSV(csvContent);
    if (binanceResult.isErr()) {
      console.error("❌ Binance CSV parsing failed:", binanceResult.error);
      return;
    }

    const transactions = binanceResult.value;
    console.log(`✅ Binance CSV parsed successfully: ${transactions.length} transactions`);

    // Test a few sample transactions
    console.log("\n🔍 Testing transaction processing...");
    
    const sampleTransactions = transactions.slice(0, 5);
    for (let i = 0; i < sampleTransactions.length; i++) {
      const tx = sampleTransactions[i];
      console.log(`\n📝 Transaction ${i + 1}:`);
      console.log(`  Description: ${tx.description}`);
      console.log(`  Paid Out: ${tx.paidOut || "N/A"} EUR`);
      console.log(`  Paid In: ${tx.paidIn || "N/A"} EUR`);
      console.log(`  Assets Used: ${tx.assetsUsed}`);
      console.log(`  Exchange Rates: ${tx.exchangeRates}`);

      // Test asset parsing
      const assets = binanceUtils.parseAssetsUsed(tx.assetsUsed);
      console.log(`  Parsed Assets: ${JSON.stringify(assets)}`);

      // Test exchange rate parsing
      const rates = binanceUtils.parseExchangeRates(tx.exchangeRates);
      console.log(`  Parsed Rates: ${JSON.stringify(rates)}`);

      // Test transaction processing
      const processResult = binanceUtils.processBinanceTransaction(tx, "Test Binance Card");
      if (processResult.isOk()) {
        const processed = processResult.value;
        console.log(`  ✅ Processed: 1 transfer, ${processed.trades.length} trades`);
        console.log(`  Transfer: ${processed.transfer.from} → ${processed.transfer.to} (${processed.transfer.amount} ${processed.transfer.currencyCode})`);
        processed.trades.forEach((trade, idx) => {
          console.log(`  Trade ${idx + 1}: ${trade.amountFrom} ${trade.tokenFrom} → ${trade.amountTo} ${trade.tokenTo}`);
        });
      } else {
        console.log(`  ❌ Processing failed: ${processResult.error.message}`);
      }
    }

    // Test batch processing
    console.log("\n🔍 Testing batch processing...");
    const batchResult = binanceUtils.processBinanceTransactions(transactions, "Test Binance Card");
    if (batchResult.isOk()) {
      const processed = batchResult.value;
      console.log(`✅ Batch processed successfully: ${processed.length} transactions`);
      
      const totalTrades = processed.reduce((sum, t) => sum + t.trades.length, 0);
      console.log(`📊 Summary: ${processed.length} transfers, ${totalTrades} trades`);
      
      // Calculate total amounts
      const totalOut = processed
        .filter(t => t.transfer.from === "Test Binance Card")
        .reduce((sum, t) => sum + parseFloat(t.transfer.amount.toString()), 0);
      
      const totalIn = processed
        .filter(t => t.transfer.to === "Test Binance Card")
        .reduce((sum, t) => sum + parseFloat(t.transfer.amount.toString()), 0);
      
      console.log(`💰 Total Out: €${totalOut.toFixed(2)}`);
      console.log(`💰 Total In: €${totalIn.toFixed(2)}`);
      console.log(`💰 Net: €${(totalIn - totalOut).toFixed(2)}`);

      // Show currency breakdown
      const currencies = new Set(processed.flatMap(t => [
        t.transfer.currencyCode,
        ...t.trades.flatMap(trade => [trade.tokenFrom, trade.tokenTo])
      ]));
      console.log(`🪙 Currencies involved: ${Array.from(currencies).join(", ")}`);

    } else {
      console.error("❌ Batch processing failed:", batchResult.error);
    }

  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the test
testBinanceCSVParsing().then(() => {
  console.log("\n🎉 Test completed!");
}).catch(error => {
  console.error("💥 Test crashed:", error);
});
