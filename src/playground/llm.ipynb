{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["res DefaultGenerateObjectResult {\n", "  object: {\n", "    recipe: {\n", "      name: 'Classic Beef and Cheese Lasagna',\n", "      ingredients: [Array],\n", "      steps: [Array]\n", "    }\n", "  },\n", "  finishReason: 'stop',\n", "  usage: { promptTokens: 88, completionTokens: 916, totalTokens: 1004 },\n", "  warnings: [\n", "    {\n", "      type: 'unsupported-setting',\n", "      setting: 'temperature',\n", "      details: 'temperature is not supported for reasoning models'\n", "    }\n", "  ],\n", "  providerMetadata: {\n", "    openai: {\n", "      reasoningTokens: 384,\n", "      acceptedPredictionTokens: 0,\n", "      rejectedPredictionTokens: 0,\n", "      cachedPromptTokens: 0\n", "    }\n", "  },\n", "  experimental_providerMetadata: {\n", "    openai: {\n", "      reasoningTokens: 384,\n", "      acceptedPredictionTokens: 0,\n", "      rejectedPredictionTokens: 0,\n", "      cachedPromptTokens: 0\n", "    }\n", "  },\n", "  response: {\n", "    id: 'chatcmpl-BdbvXuRSrHgIWn3NTXMYgZAcYZutI',\n", "    timestamp: 2025-06-01T12:18:03.000Z,\n", "    modelId: 'o4-mini-2025-04-16',\n", "    headers: {\n", "      'access-control-expose-headers': 'X-Request-ID',\n", "      'alt-svc': 'h3=\":443\"; ma=86400',\n", "      'cf-cache-status': 'DYNAMIC',\n", "      'cf-ray': '948ea6c01b6b8f2b-VIE',\n", "      connection: 'keep-alive',\n", "      'content-encoding': 'br',\n", "      'content-type': 'application/json',\n", "      date: 'Sun, 01 Jun 2025 12:18:28 GMT',\n", "      'openai-organization': 'matt1dev',\n", "      'openai-processing-ms': '25289',\n", "      'openai-version': '2020-10-01',\n", "      server: 'cloudflare',\n", "      'set-cookie': '_cfuvid=6w8z97dxK4eCeTUY3JAFGz1iw69SpMcvmkr9_hs_CSA-1748780308319-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',\n", "      'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',\n", "      'transfer-encoding': 'chunked',\n", "      'x-content-type-options': 'nosniff',\n", "      'x-envoy-upstream-service-time': '25292',\n", "      'x-ratelimit-limit-requests': '10000',\n", "      'x-ratelimit-limit-tokens': '10000000',\n", "      'x-ratelimit-remaining-requests': '9999',\n", "      'x-ratelimit-remaining-tokens': '9999991',\n", "      'x-ratelimit-reset-requests': '6ms',\n", "      'x-ratelimit-reset-tokens': '0s',\n", "      'x-request-id': 'req_b2f92d5f6775199c907752a75ee02633'\n", "    },\n", "    body: {\n", "      id: 'chatcmpl-BdbvXuRSrHgIWn3NTXMYgZAcYZutI',\n", "      object: 'chat.completion',\n", "      created: 1748780283,\n", "      model: 'o4-mini-2025-04-16',\n", "      choices: [<PERSON><PERSON><PERSON>],\n", "      usage: [Object],\n", "      service_tier: 'default',\n", "      system_fingerprint: null\n", "    }\n", "  },\n", "  request: {\n", "    body: '{\"model\":\"o4-mini\",\"response_format\":{\"type\":\"json_schema\",\"json_schema\":{\"schema\":{\"type\":\"object\",\"properties\":{\"recipe\":{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\"},\"ingredients\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\"},\"amount\":{\"type\":\"string\"}},\"required\":[\"name\",\"amount\"],\"additionalProperties\":false}},\"steps\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}}},\"required\":[\"name\",\"ingredients\",\"steps\"],\"additionalProperties\":false}},\"required\":[\"recipe\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"},\"strict\":true,\"name\":\"response\"}},\"messages\":[{\"role\":\"user\",\"content\":\"Generate a lasagna recipe.\"}]}'\n", "  },\n", "  logprobs: undefined\n", "}\n"]}], "source": ["const { generateObject, generateText } = require(\"ai\") as typeof import(\"ai\");\n", "const { openai } = require(\"@ai-sdk/openai\") as typeof import(\"@ai-sdk/openai\");\n", "const { z } = require(\"zod\") as typeof import(\"zod\");\n", "\n", "const res = await generateObject({\n", "  model: openai(\"o4-mini\"),\n", "  schema: z.object({\n", "    recipe: z.object({\n", "      name: z.string(),\n", "      ingredients: z.array(z.object({ name: z.string(), amount: z.string() })),\n", "      steps: z.array(z.string()),\n", "    }),\n", "  }),\n", "  prompt: \"Generate a lasagna recipe.\",\n", "});\n", "console.log(\"res\", res);"]}, {"cell_type": "code", "execution_count": 12, "id": "OiVApUi-sFGaHZYjU5EqR", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  id: 'chatcmpl-BdbsqYv5GECQFrcfHzeTkDChoMaay',\n", "  object: 'chat.completion',\n", "  created: 1748780116,\n", "  model: 'gpt-4.1-2025-04-14',\n", "  choices: [\n", "    {\n", "      index: 0,\n", "      message: [Object],\n", "      logprobs: null,\n", "      finish_reason: 'stop'\n", "    }\n", "  ],\n", "  usage: {\n", "    prompt_tokens: 77,\n", "    completion_tokens: 382,\n", "    total_tokens: 459,\n", "    prompt_tokens_details: { cached_tokens: 0, audio_tokens: 0 },\n", "    completion_tokens_details: {\n", "      reasoning_tokens: 0,\n", "      audio_tokens: 0,\n", "      accepted_prediction_tokens: 0,\n", "      rejected_prediction_tokens: 0\n", "    }\n", "  },\n", "  service_tier: 'default',\n", "  system_fingerprint: 'fp_b3f1157249'\n", "}\n"]}], "source": ["res.response.body"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DefaultGenerateTextResult {\n", "  text: 'A classic lasagna isn’t just a random stack of noodles, meat sauce, béchamel and cheese—it’s a carefully balanced layering of flavors, textures and cooking science. Here’s why each component and step exists:\\n' +\n", "    '\\n' +\n", "    '1. Building the Ragù (<PERSON><PERSON><PERSON><PERSON>)  \\n' +\n", "    '  • Soffritto base (onion, carrot, celery):  gently sautéed in olive oil or butter, these aromatics provide sweetness, complexity and an underlying savory note.  \\n' +\n", "    '  • Dual meats (beef + pork or sausage):  pork brings fat and richness; beef brings a meaty backbone. Together they yield a sauce that’s both flavorful and unctuous.  \\n' +\n", "    '  • Deglazing with red wine:  adds acidity, tannins and depth. The alcohol burns off during simmering, leaving behind concentrated flavor.  \\n' +\n", "    '  • Crushed tomatoes (or passata):  give body, acidity and sweetness. Simmering melds flavors and reduces excess water so your lasagna layers don’t get watery.  \\n' +\n", "    '  • Seasoning & herbs (garlic, bay leaf, oregano or thyme):  layer in complementary herbal notes without overpowering the meat.\\n' +\n", "    '\\n' +\n", "    '2. Making the Béchamel (White) Sauce  \\n' +\n", "    '  • Equal parts butter + flour (the roux):  thickens milk into a silky sauce. Cooking the roux briefly removes that raw flour taste.  \\n' +\n", "    '  • Milk (warmed):  whisked in to create a smooth, creamy sauce that coats the back of a spoon.  \\n' +\n", "    '  • Nutmeg, salt, pepper:  nutmeg cuts through the milk’s sweetness; salt and white pepper season.  \\n' +\n", "    '  • Why béchamel?  It balances the acidity of the tomato sauce, adds a creamy texture, and helps “glue” the layers together so the lasagna slices holding their shape when cut.\\n' +\n", "    '\\n' +\n", "    '3. Choosing and Preparing Pasta Sheets  \\n' +\n", "    '  • No-boil (oven-ready) vs. boiled noodles:  no-boil noodles absorb moisture from sauces during baking, saving time. Traditional dried or fresh sheets may be briefly par-cooked to remove raw flour taste and ensure even cooking.  \\n' +\n", "    '  • Uniform layering:  same-size sheets make for neat layers and even cooking.\\n' +\n", "    '\\n' +\n", "    '4. Layering for Texture and Flavor  \\n' +\n", "    '  • Bottom sauce layer:  prevents sticking and ensures the first pasta sheet hydrates evenly.  \\n' +\n", "    '  • First pasta layer:  provides structure—think of this as the “floor” of each tier.  \\n' +\n", "    '  • Ragù layer:  meaty, tomatoey portion; imparts bold flavor in every bite.  \\n' +\n", "    '  • Béchamel layer:  creamy counterpoint to the meat sauce. The contrast keeps the mouthfeel from becoming too dry or too acidic.  \\n' +\n", "    '  • Cheese layer (mozzarella, ricotta or béchamel alone, plus grated Parmesan or Pecorino):  \\n' +\n", "    '     – <PERSON><PERSON><PERSON> melts into gooey strings.  \\n' +\n", "    '     – <PERSON><PERSON> (optional) adds a mild, fluffy richness.  \\n' +\n", "    '     – <PERSON><PERSON><PERSON>/<PERSON> on top gives a nutty, salty crust.  \\n' +\n", "    '  • Repeat tiers:  typically three tiers of pasta and two tiers of each sauce/cheese for height without becoming a skyscraper that won’t cook through.\\n' +\n", "    '\\n' +\n", "    '5. Baking and Finishing  \\n' +\n", "    '  • Moderate oven (about 180–190 °C / 350–375 °F):  allows the interior to heat through without scorching the cheese too quickly.  \\n' +\n", "    '  • Covered vs. uncovered:  start covered (tented foil) for even heat and to trap steam so the center cooks; remove the foil toward the end so the top cheese browns and bubbles.  \\n' +\n", "    '  • Resting time (10–15 minutes):  lets the sauces gel, so when you slice into it, the layers hold neatly rather than running into one another.\\n' +\n", "    '\\n' +\n", "    '6. The Science and the Art  \\n' +\n", "    '  • Moisture balance:  enough liquid in sauces to hydrate pasta, but not so much that lasagna becomes soupy.  \\n' +\n", "    '  • Flavor layering:  each element (aromatics, meat, wine, tomato, dairy, cheese) contributes distinct notes that combine into a rich, harmonious whole.  \\n' +\n", "    '  • Texture contrast:  tender pasta, creamy béchamel, and meaty ragù—plus a golden-brown cheese crust—deliver an ideal interplay of soft, silky and slightly crisp.\\n' +\n", "    '\\n' +\n", "    'In essence, classic lasagna is a dish of layered contrasts—rich yet balanced, structured yet luscious—brought to life through a sequence of steps that ensure every bite is tender, flavorful and perfectly set.',\n", "  files: [],\n", "  reasoning: undefined,\n", "  reasoningDetails: [],\n", "  toolCalls: [],\n", "  toolResults: [],\n", "  finishReason: 'stop',\n", "  usage: { promptTokens: 17, completionTokens: 1281, totalTokens: 1298 },\n", "  warnings: [\n", "    {\n", "      type: 'unsupported-setting',\n", "      setting: 'temperature',\n", "      details: 'temperature is not supported for reasoning models'\n", "    }\n", "  ],\n", "  request: {\n", "    body: '{\"model\":\"o4-mini\",\"messages\":[{\"role\":\"user\",\"content\":\"Explain the reasoning behind the recipe for Classic Lasagna.\"}]}'\n", "  },\n", "  response: {\n", "    id: 'chatcmpl-BdbpucnlnXS6G1zhlQ8SxD5zZUMu5',\n", "    timestamp: 2025-06-01T12:12:14.000Z,\n", "    modelId: 'o4-mini-2025-04-16',\n", "    headers: {\n", "      'access-control-expose-headers': 'X-Request-ID',\n", "      'alt-svc': 'h3=\":443\"; ma=86400',\n", "      'cf-cache-status': 'DYNAMIC',\n", "      'cf-ray': '948e9e3aea4f8f2b-VIE',\n", "      connection: 'keep-alive',\n", "      'content-encoding': 'br',\n", "      'content-type': 'application/json',\n", "      date: 'Sun, 01 Jun 2025 12:12:22 GMT',\n", "      'openai-organization': 'matt1dev',\n", "      'openai-processing-ms': '8337',\n", "      'openai-version': '2020-10-01',\n", "      server: 'cloudflare',\n", "      'set-cookie': '_cfuvid=YDM5NF4q8DmLCCaxrJx_Q6ryYujhfFv9aM1K7GEY5VU-1748779942399-*******-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',\n", "      'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',\n", "      'transfer-encoding': 'chunked',\n", "      'x-content-type-options': 'nosniff',\n", "      'x-envoy-upstream-service-time': '8365',\n", "      'x-ratelimit-limit-requests': '10000',\n", "      'x-ratelimit-limit-tokens': '10000000',\n", "      'x-ratelimit-remaining-requests': '9999',\n", "      'x-ratelimit-remaining-tokens': '9999982',\n", "      'x-ratelimit-reset-requests': '6ms',\n", "      'x-ratelimit-reset-tokens': '0s',\n", "      'x-request-id': 'req_97c12bfe63187b7d1c40bb40c7f95f05'\n", "    },\n", "    body: {\n", "      id: 'chatcmpl-BdbpucnlnXS6G1zhlQ8SxD5zZUMu5',\n", "      object: 'chat.completion',\n", "      created: 1748779934,\n", "      model: 'o4-mini-2025-04-16',\n", "      choices: [<PERSON><PERSON><PERSON>],\n", "      usage: [Object],\n", "      service_tier: 'default',\n", "      system_fingerprint: null\n", "    },\n", "    messages: [ [Object] ]\n", "  },\n", "  steps: [\n", "    {\n", "      stepType: 'initial',\n", "      text: 'A classic lasagna isn’t just a random stack of noodles, meat sauce, béchamel and cheese—it’s a carefully balanced layering of flavors, textures and cooking science. Here’s why each component and step exists:\\n' +\n", "        '\\n' +\n", "        '1. Building the Ragù (<PERSON><PERSON><PERSON><PERSON>)  \\n' +\n", "        '  • Soffritto base (onion, carrot, celery):  gently sautéed in olive oil or butter, these aromatics provide sweetness, complexity and an underlying savory note.  \\n' +\n", "        '  • Dual meats (beef + pork or sausage):  pork brings fat and richness; beef brings a meaty backbone. Together they yield a sauce that’s both flavorful and unctuous.  \\n' +\n", "        '  • Deglazing with red wine:  adds acidity, tannins and depth. The alcohol burns off during simmering, leaving behind concentrated flavor.  \\n' +\n", "        '  • Crushed tomatoes (or passata):  give body, acidity and sweetness. Simmering melds flavors and reduces excess water so your lasagna layers don’t get watery.  \\n' +\n", "        '  • Seasoning & herbs (garlic, bay leaf, oregano or thyme):  layer in complementary herbal notes without overpowering the meat.\\n' +\n", "        '\\n' +\n", "        '2. Making the Béchamel (White) Sauce  \\n' +\n", "        '  • Equal parts butter + flour (the roux):  thickens milk into a silky sauce. Cooking the roux briefly removes that raw flour taste.  \\n' +\n", "        '  • Milk (warmed):  whisked in to create a smooth, creamy sauce that coats the back of a spoon.  \\n' +\n", "        '  • Nutmeg, salt, pepper:  nutmeg cuts through the milk’s sweetness; salt and white pepper season.  \\n' +\n", "        '  • Why béchamel?  It balances the acidity of the tomato sauce, adds a creamy texture, and helps “glue” the layers together so the lasagna slices holding their shape when cut.\\n' +\n", "        '\\n' +\n", "        '3. Choosing and Preparing Pasta Sheets  \\n' +\n", "        '  • No-boil (oven-ready) vs. boiled noodles:  no-boil noodles absorb moisture from sauces during baking, saving time. Traditional dried or fresh sheets may be briefly par-cooked to remove raw flour taste and ensure even cooking.  \\n' +\n", "        '  • Uniform layering:  same-size sheets make for neat layers and even cooking.\\n' +\n", "        '\\n' +\n", "        '4. Layering for Texture and Flavor  \\n' +\n", "        '  • Bottom sauce layer:  prevents sticking and ensures the first pasta sheet hydrates evenly.  \\n' +\n", "        '  • First pasta layer:  provides structure—think of this as the “floor” of each tier.  \\n' +\n", "        '  • Ragù layer:  meaty, tomatoey portion; imparts bold flavor in every bite.  \\n' +\n", "        '  • Béchamel layer:  creamy counterpoint to the meat sauce. The contrast keeps the mouthfeel from becoming too dry or too acidic.  \\n' +\n", "        '  • Cheese layer (mozzarella, ricotta or béchamel alone, plus grated Parmesan or Pecorino):  \\n' +\n", "        '     – <PERSON><PERSON><PERSON> melts into gooey strings.  \\n' +\n", "        '     – <PERSON><PERSON> (optional) adds a mild, fluffy richness.  \\n' +\n", "        '     – <PERSON><PERSON><PERSON>/<PERSON> on top gives a nutty, salty crust.  \\n' +\n", "        '  • Repeat tiers:  typically three tiers of pasta and two tiers of each sauce/cheese for height without becoming a skyscraper that won’t cook through.\\n' +\n", "        '\\n' +\n", "        '5. Baking and Finishing  \\n' +\n", "        '  • Moderate oven (about 180–190 °C / 350–375 °F):  allows the interior to heat through without scorching the cheese too quickly.  \\n' +\n", "        '  • Covered vs. uncovered:  start covered (tented foil) for even heat and to trap steam so the center cooks; remove the foil toward the end so the top cheese browns and bubbles.  \\n' +\n", "        '  • Resting time (10–15 minutes):  lets the sauces gel, so when you slice into it, the layers hold neatly rather than running into one another.\\n' +\n", "        '\\n' +\n", "        '6. The Science and the Art  \\n' +\n", "        '  • Moisture balance:  enough liquid in sauces to hydrate pasta, but not so much that lasagna becomes soupy.  \\n' +\n", "        '  • Flavor layering:  each element (aromatics, meat, wine, tomato, dairy, cheese) contributes distinct notes that combine into a rich, harmonious whole.  \\n' +\n", "        '  • Texture contrast:  tender pasta, creamy béchamel, and meaty ragù—plus a golden-brown cheese crust—deliver an ideal interplay of soft, silky and slightly crisp.\\n' +\n", "        '\\n' +\n", "        'In essence, classic lasagna is a dish of layered contrasts—rich yet balanced, structured yet luscious—brought to life through a sequence of steps that ensure every bite is tender, flavorful and perfectly set.',\n", "      reasoning: undefined,\n", "      reasoningDetails: [],\n", "      files: [],\n", "      sources: [],\n", "      toolCalls: [],\n", "      toolResults: [],\n", "      finishReason: 'stop',\n", "      usage: [Object],\n", "      warnings: [Array],\n", "      logprobs: undefined,\n", "      request: [Object],\n", "      response: [Object],\n", "      providerMetadata: [Object],\n", "      experimental_providerMetadata: [Object],\n", "      isContinued: false\n", "    }\n", "  ],\n", "  experimental_providerMetadata: {\n", "    openai: {\n", "      reasoningTokens: 320,\n", "      acceptedPredictionTokens: 0,\n", "      rejectedPredictionTokens: 0,\n", "      cachedPromptTokens: 0\n", "    }\n", "  },\n", "  providerMetadata: {\n", "    openai: {\n", "      reasoningTokens: 320,\n", "      acceptedPredictionTokens: 0,\n", "      rejectedPredictionTokens: 0,\n", "      cachedPromptTokens: 0\n", "    }\n", "  },\n", "  logprobs: undefined,\n", "  outputResolver: [Function: outputResolver],\n", "  sources: []\n", "}\n"]}], "source": ["const { openai } = require(\"@ai-sdk/openai\");\n", "const resoningRes = await generateText({\n", "  model: openai(\"o4-mini\"),\n", "  prompt: `Explain the reasoning behind the recipe for ${object.recipe.name}.`,\n", "})\n", "3"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}