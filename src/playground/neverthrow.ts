import { err, ok, okAsync, Result, ResultAsync } from "neverthrow";
import { sleep } from "openai/core.mjs";
import yargs from "yargs";

function randomNumber(): Result<number, string> {
  const rand = Math.random();

  if (rand < 0.5) {
    return err("Error: Random number is too high");
  } else {
    return ok(rand);
  }
}

async function syncTest() {
  const result = randomNumber();

  const res = result.unwrapOr(10);

  const sq = (n: number): Result<number, number> => ok(n ** 2);

  result.andThen((value) => {
    const rand = Math.random();

    if (rand < 0.5) {
      return err("Error: Random number is too high");
    } else {
      return ok(rand);
    }
  });

  console.log("res", res);

  const debug = 1;
  // if (result.isOk()) {
  //   console.log("Success:", result.value);
  // } else {
  //   console.error("Failure:", result.error);
  // }
}

async function asyncRandomNumber() {
  await sleep(1000);

  const rand = Math.random();

  if (rand < 0.5) {
    return err("Error: Random number is too high");
  } else {
    return ok(rand);
  }
}

async function fetchUser() {
  console.log("Simulating a database call...");

  if (Math.random() < 1) {
    throw new Error("Database error: Random failure");
  }

  return {
    name: "John Doe",
    age: 30,
  };
}

async function fromPromise() {
  try {
    const result = await ResultAsync.fromPromise(
      fetchUser(),
      (err: any) =>
        ({
          type: "DatabaseError",
          message: err.message || "An error occurred while fetching the user",
        } as const)
    );

    if (result.isOk()) {
      console.log("User fetched successfully:", result.value);
    } else {
      console.error("nethrow error handling:", result.error.message);
    }
  } catch (error: any) {
    console.error("Caught an error:", error.message);
  }
}

export async function scafoldCli(functions: Record<string, Function>) {
  // parse with yargs
  const cliArgs = await yargs(process.argv.slice(2)).argv;

  const command = cliArgs._[0];

  if (!command || !functions[command]) {
    console.error(`command ${command} not found`);
    console.error("Available commands:", Object.keys(functions).join(", "));
    return;
  }

  const commandFn = functions[command];
  commandFn(cliArgs);
}

async function erorChaining() {
  let res = ResultAsync.fromPromise(
    (async () => {
      return 1;
    })(),
    (err) => {
      return {
        type: "FirstError",
      } as const;
    }
  )
    .mapErr((err) => {
      console.log("err", err);

      if (Math.random() < 0.5) {
        return {
          type: "SecondError",
        } as const;
      }

      return err;
    })
    .map((value) => {
      console.log("value", value);

      return value;
    })
    .mapErr((err) => {
      console.log("err", err);

      return err;
    });

  console.log("res", res);
}

function asyncResult() {
  return ResultAsync.fromSafePromise(sleep(1000)).map(() => {
    return 1;
  });
}

async function asyncTest() {
  const res = asyncResult().map((v) => {
    return v + 1;
  });

  const res2 = await asyncResult();

  console.log("res", res);
}

scafoldCli({
  syncTest,
  fromPromise,
});
