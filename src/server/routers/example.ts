import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '@/server/trpc';
import { ok, err } from 'neverthrow';

export const exampleRouter = createTRPCRouter({
  hello: publicProcedure
    .input(z.object({ text: z.string() }))
    .query(({ input }) => {
      return {
        greeting: `Hello ${input.text}`,
      };
    }),

  getAll: publicProcedure.query(({ ctx }) => {
    // Example using neverthrow for error handling
    try {
      return ok({
        message: 'This is a test endpoint',
        timestamp: new Date(),
      });
    } catch (error) {
      return err(new Error('Failed to get data'));
    }
  }),

  create: publicProcedure
    .input(z.object({ name: z.string().min(1) }))
    .mutation(async ({ input, ctx }) => {
      // Example mutation - you can replace this with actual business logic
      try {
        return ok({
          id: Math.random().toString(36).substr(2, 9),
          name: input.name,
          createdAt: new Date(),
        });
      } catch (error) {
        return err(new Error('Failed to create item'));
      }
    }),

  // Example using Prisma - you can uncomment and modify based on your schema
  // getInvoices: publicProcedure
  //   .input(z.object({ limit: z.number().min(1).max(100).default(10) }))
  //   .query(async ({ input, ctx }) => {
  //     try {
  //       const invoices = await ctx.prisma.invoiceImportItem.findMany({
  //         take: input.limit,
  //         orderBy: { createdAt: 'desc' },
  //       });
  //       return ok(invoices);
  //     } catch (error) {
  //       return err(new Error('Failed to fetch invoices'));
  //     }
  //   }),
});
