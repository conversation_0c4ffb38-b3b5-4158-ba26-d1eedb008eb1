import React from "react";
import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from "vitest";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createTRPCReact, httpBatchLink } from "@trpc/react-query";
import { createTRPCMsw } from "msw-trpc";
import { setupServer } from "msw/node";
import { http, HttpResponse } from "msw";
import "@testing-library/jest-dom";
import { AppRouter } from "@/server/routers/_app";
import TransactionsPage from "../pages/transactions";
import { AccountType } from "@/prisma/generated";

// Create tRPC client for testing
const testTrpc = createTRPCReact<AppRouter>();

// Mock data
const mockTransactions = [
  {
    id: "tx-1",
    executedAt: new Date("2024-01-15"),
    transactionGroupId: "group-1",
    account: {
      id: "acc-1",
      name: "Test Wallet",
      type: "WALLET" as AccountType,
    },
    transfer: {
      id: "transfer-1",
      amount: 100.50,
      currencyCode: "EUR",
      counterparty: "Test Counterparty",
      description: "Test transfer",
      reconciliations: [],
    },
    trade: null,
    metadata: null,
  },
  {
    id: "tx-2",
    executedAt: new Date("2024-01-14"),
    transactionGroupId: null,
    account: {
      id: "acc-2",
      name: "Test Exchange",
      type: "EXCHANGE_ACCOUNT" as AccountType,
    },
    transfer: null,
    trade: {
      id: "trade-1",
      tokenFrom: "BTC",
      tokenTo: "EUR",
      amountFrom: 0.001,
      amountTo: 45.50,
      poolId: "test-pool",
      description: "BTC to EUR trade",
    },
    metadata: null,
  },
];

const mockAccounts = [
  {
    id: "acc-1",
    name: "Test Wallet",
    type: "WALLET" as AccountType,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "acc-2",
    name: "Test Exchange",
    type: "EXCHANGE_ACCOUNT" as AccountType,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockStats = {
  total: 2,
  withTransfers: 1,
  withTrades: 1,
  withLiquidityActions: 0,
};

// Mock Next.js router
const mockPush = vi.fn();
const mockRouter = {
  push: mockPush,
  query: {},
  pathname: "/transactions",
  asPath: "/transactions",
  route: "/transactions",
  back: vi.fn(),
  forward: vi.fn(),
  prefetch: vi.fn(),
  replace: vi.fn(),
  reload: vi.fn(),
  beforePopState: vi.fn(),
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  isPreview: false,
};

vi.mock("next/router", () => ({
  useRouter: () => mockRouter,
}));

// Mock UI components to simplify rendering
vi.mock("@/components/TransactionDetailsDrawer", () => ({
  TransactionDetailsDrawer: ({ open, transactionId }: any) => (
    <div data-testid="transaction-drawer" data-open={open} data-transaction-id={transactionId}>
      Transaction Drawer
    </div>
  ),
}));

vi.mock("@/components/DashboardLayout", () => ({
  DashboardLayout: ({ children }: any) => <div data-testid="dashboard-layout">{children}</div>,
}));

vi.mock("@/components/SearchInput", () => ({
  SearchInput: ({ value, onChange, placeholder }: any) => (
    <input
      data-testid="search-input"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
    />
  ),
}));

// Mock currency formatting
vi.mock("@/modules/core/currency", () => ({
  currency: {
    formatMonetary: (amount: number, currency: string) => `${amount} ${currency}`,
  },
}));

// Setup MSW server for mocking tRPC requests
const trpcMsw = createTRPCMsw<AppRouter>();
const server = setupServer(
  trpcMsw.transactions.getAll.query(() => ({
    transactions: mockTransactions,
    nextCursor: null,
  })),
  trpcMsw.transactions.getStats.query(() => mockStats),
  trpcMsw.accounts.getAll.query(() => mockAccounts),
);

// Test wrapper component that provides tRPC context
function TestWrapper({ children }: { children: React.ReactNode }) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        cacheTime: 0,
      },
    },
  });

  const trpcClient = testTrpc.createClient({
    links: [
      httpBatchLink({
        url: "http://localhost:3000/api/trpc",
        fetch: fetch, // Use the mocked fetch from MSW
      }),
    ],
  });

  return (
    <testTrpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </testTrpc.Provider>
  );
}

beforeEach(() => {
  mockPush.mockClear();
});

describe("TransactionsPage Integration Tests", () => {
  beforeAll(() => {
    // Start MSW server
    server.listen();
  });

  afterAll(() => {
    // Stop MSW server
    server.close();
  });

  beforeEach(() => {
    // Reset handlers and clear mocks
    server.resetHandlers();
    mockPush.mockClear();
  });

  afterEach(() => {
    // Reset handlers after each test
    server.resetHandlers();
  });

  it("renders the page title and description", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    expect(screen.getByText("Transactions")).toBeInTheDocument();
    expect(screen.getByText("View and manage all transactions across accounts")).toBeInTheDocument();
  });

  it("displays stats cards", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    expect(screen.getByText("Total Transactions")).toBeInTheDocument();
    expect(screen.getByText("Transfers")).toBeInTheDocument();
    expect(screen.getByText("Trades")).toBeInTheDocument();
    expect(screen.getByText("Other Actions")).toBeInTheDocument();
  });

  it("renders transaction table headers", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    expect(screen.getByText("Date")).toBeInTheDocument();
    expect(screen.getByText("Account")).toBeInTheDocument();
    expect(screen.getByText("Type")).toBeInTheDocument();
    expect(screen.getByText("Amount")).toBeInTheDocument();
    expect(screen.getByText("Description")).toBeInTheDocument();
    expect(screen.getByText("Group")).toBeInTheDocument();
  });

  it("displays empty state when no transactions exist", async () => {
    // Override MSW to return empty transactions
    server.use(
      trpcMsw.transactions.getAll.query(() => ({
        transactions: [],
        nextCursor: null,
      }))
    );

    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Wait for the component to load and show empty state
    await waitFor(() => {
      expect(screen.getByText("No transactions found")).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it("displays transactions when they exist", async () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Wait for transactions to load from MSW mock
    await waitFor(() => {
      expect(screen.getByText("Test Wallet")).toBeInTheDocument();
      expect(screen.getByText("Test Exchange")).toBeInTheDocument();
    }, { timeout: 5000 });

    // Check that transaction types are displayed
    expect(screen.getByText("Transfer")).toBeInTheDocument();
    expect(screen.getByText("Trade")).toBeInTheDocument();
  });

  it("displays correct stats from API", async () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Wait for stats to load
    await waitFor(() => {
      expect(screen.getByText("2")).toBeInTheDocument(); // Total transactions
      expect(screen.getByText("1")).toBeInTheDocument(); // Transfers and trades
    }, { timeout: 5000 });
  });

  it("handles search input interaction", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    const searchInput = screen.getByTestId("search-input");
    expect(searchInput).toBeInTheDocument();

    fireEvent.change(searchInput, { target: { value: "test search" } });
    expect(searchInput).toHaveValue("test search");
  });

  it("renders pagination controls", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    const previousButton = screen.getByText("Previous");
    const nextButton = screen.getByText("Next");

    expect(previousButton).toBeInTheDocument();
    expect(nextButton).toBeInTheDocument();
  });

  it("opens transaction drawer when URL has transactionId", () => {
    // Mock router with transactionId in query
    mockRouter.query = { transactionId: "test-tx-id" };

    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    const drawer = screen.getByTestId("transaction-drawer");
    expect(drawer).toHaveAttribute("data-open", "true");
    expect(drawer).toHaveAttribute("data-transaction-id", "test-tx-id");
  });

  it("handles transaction row click", async () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Wait for transactions to load
    await waitFor(() => {
      expect(screen.getByText("Test Wallet")).toBeInTheDocument();
    }, { timeout: 5000 });

    // Click on a transaction row
    const transactionRow = screen.getByText("Test Wallet").closest("tr");
    if (transactionRow) {
      fireEvent.click(transactionRow);

      // Check that router.push was called with transaction ID
      expect(mockPush).toHaveBeenCalledWith(
        expect.stringContaining("/transactions?transactionId=tx-1"),
        undefined,
        { shallow: true }
      );
    }
  });

  it("displays loading state initially", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Should show loading state initially
    expect(screen.getByText("Loading transactions...")).toBeInTheDocument();
  });

  it("handles API errors gracefully", async () => {
    // Override MSW to throw an error
    server.use(
      trpcMsw.transactions.getAll.query(() => {
        throw new Error("Internal server error");
      })
    );

    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Wait for error state
    await waitFor(() => {
      expect(screen.getByText(/Failed to load transactions/)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it("handles account filter", () => {
    render(
      <TestWrapper>
        <TransactionsPage />
      </TestWrapper>
    );

    // Check that account filter dropdown exists
    const filterElements = screen.getAllByText("All accounts");
    expect(filterElements.length).toBeGreaterThan(0);
  });
});
