import { useState, useEffect } from "react";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Separator } from "@/components/ui/separator";
import { SaveIcon, SettingsIcon } from "lucide-react";
import Link from "next/link";
import { formatAccountingYearStart, ACCOUNTING_YEAR_PRESETS } from "@/modules/settings/settingsUtils";
import { DashboardLayout } from "@/components/DashboardLayout";

export default function SettingsPage() {
  const [accountingYearStartDate, setAccountingYearStartDate] = useState<Date | undefined>();
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch current settings
  const { data: settings, isLoading, refetch } = trpc.settings.get.useQuery();

  // Update settings mutation
  const updateSettings = trpc.settings.update.useMutation({
    onSuccess: () => {
      setHasChanges(false);
      refetch();
    },
    onError: (error: any) => {
      console.error("Failed to update settings:", error);
    },
  });

  // Initialize form with current settings
  useEffect(() => {
    if (settings?.accountingYearStartDay && settings?.accountingYearStartMonth) {
      // Create a date object from day and month (using current year as placeholder)
      const currentYear = new Date().getFullYear();
      const date = new Date(currentYear, settings.accountingYearStartMonth - 1, settings.accountingYearStartDay);
      setAccountingYearStartDate(date);
    }
  }, [settings]);

  // Track changes
  useEffect(() => {
    if (settings && accountingYearStartDate) {
      const currentDay = settings.accountingYearStartDay;
      const currentMonth = settings.accountingYearStartMonth;
      const selectedDay = accountingYearStartDate.getDate();
      const selectedMonth = accountingYearStartDate.getMonth() + 1; // getMonth() returns 0-11

      const hasChanged = currentDay !== selectedDay || currentMonth !== selectedMonth;
      setHasChanges(hasChanged);
    }
  }, [accountingYearStartDate, settings]);

  const handleSave = () => {
    if (accountingYearStartDate) {
      updateSettings.mutate({
        accountingYearStartDay: accountingYearStartDate.getDate(),
        accountingYearStartMonth: accountingYearStartDate.getMonth() + 1, // getMonth() returns 0-11
      });
    }
  };

  const handleDateChange = (date: Date | undefined) => {
    setAccountingYearStartDate(date);
  };

  if (isLoading) {
    return (
      <DashboardLayout title="Settings">
        <div className="container mx-auto py-8">
          <div className="flex items-center gap-2 mb-6">
            <SettingsIcon className="h-6 w-6" />
            <h1 className="text-2xl font-bold">Settings</h1>
          </div>
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-10 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Settings">
      <div className="container mx-auto py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <SettingsIcon className="h-6 w-6" />
            <h1 className="text-2xl font-bold">Settings</h1>
          </div>
        </div>

        {/* Settings Form */}
        <Card>
          <CardHeader>
            <CardTitle>Accounting Configuration</CardTitle>
            <CardDescription>Configure your accounting settings and preferences.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Accounting Year Start Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Accounting Year Start Date</label>
              <p className="text-sm text-muted-foreground">Select the date when your accounting year begins. Only the day and month will be stored.</p>
              <div className="text-xs text-muted-foreground space-y-1">
                <p>
                  <strong>Common options:</strong>
                </p>
                <p>• {formatAccountingYearStart(ACCOUNTING_YEAR_PRESETS.CALENDAR_YEAR)} - Calendar year</p>
                <p>• {formatAccountingYearStart(ACCOUNTING_YEAR_PRESETS.FISCAL_YEAR_US)} - US fiscal year</p>
                <p>• {formatAccountingYearStart(ACCOUNTING_YEAR_PRESETS.FISCAL_YEAR_UK)} - UK fiscal year</p>
                <p>• {formatAccountingYearStart(ACCOUNTING_YEAR_PRESETS.FISCAL_YEAR_CANADA)} - Canadian fiscal year</p>
              </div>
              <DatePicker
                date={accountingYearStartDate}
                onDateChange={handleDateChange}
                placeholder="Select accounting year start date"
                className="w-full max-w-[280px]"
              />
              {settings && (
                <div className="text-sm text-muted-foreground">
                  <strong>Current setting:</strong> Day {settings.accountingYearStartDay}, Month {settings.accountingYearStartMonth}
                  {accountingYearStartDate && (
                    <span> ({formatAccountingYearStart({ day: settings.accountingYearStartDay, month: settings.accountingYearStartMonth })})</span>
                  )}
                </div>
              )}
            </div>

            <Separator />

            {/* Save Button */}
            <div className="flex justify-end">
              <Button onClick={handleSave} disabled={!hasChanges || updateSettings.isPending} className="flex items-center gap-2">
                <SaveIcon className="h-4 w-4" />
                {updateSettings.isPending ? "Saving..." : "Save Settings"}
              </Button>
            </div>

            {/* Status Messages */}
            {updateSettings.isSuccess && <div className="text-sm text-green-600">Settings saved successfully!</div>}
            {updateSettings.isError && <div className="text-sm text-red-600">Failed to save settings. Please try again.</div>}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
