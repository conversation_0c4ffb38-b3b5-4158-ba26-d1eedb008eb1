import { trpc } from "@/utils/trpc";
import { DashboardLayout } from "@/components/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { FileText, Users, Settings } from "lucide-react";

export default function Home() {
  // Get basic stats
  const invoiceStats = trpc.invoices.getStats.useQuery();
  const vendorStats = trpc.vendors.getStats.useQuery();

  return (
    <DashboardLayout title="Dashboard">
      <div className="container mx-auto p-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Welcome to SAC Accounting</p>
        </div>
      </div>
    </DashboardLayout>
  );
}
