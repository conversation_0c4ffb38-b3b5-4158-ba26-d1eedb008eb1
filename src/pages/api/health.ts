// Health check endpoint - this will be public (not protected by HTTP auth)
import type { NextApiRequest, NextApiResponse } from "next";

type HealthData = {
  status: string;
  timestamp: string;
  environment: string;
  authEnabled: boolean;
};

export default function handler(req: NextApiRequest, res: NextApiResponse<HealthData>) {
  res.status(200).json({
    status: "ok",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "unknown",
    authEnabled: !!process.env.VERCEL,
  });
}
