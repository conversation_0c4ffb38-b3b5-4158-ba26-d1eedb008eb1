import { useChat } from "ai/react";
import { DashboardLayout } from "@/components/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Bot, User, Send, Loader2, Upload, FileText } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { currency } from "@/modules/core/currency";

export default function ChatPage() {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: "/api/chat",
    maxSteps: 5,
  });

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [files, setFiles] = useState<FileList | undefined>(undefined);
  const [suggestions] = useState([
    "Show me overall invoice statistics",
    "What are the top 5 vendors by amount?",
    "Get invoice stats for 2024",
    "Show monthly summary for 2024",
    "What currencies do we have invoices in?",
    "Show me recent invoices",
    "Search for invoices from 'Microsoft'",
    "Upload a CSV file to analyze invoice data",
  ]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSuggestionClick = (suggestion: string) => {
    handleInputChange({ target: { value: suggestion } } as any);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = event.target.files;
    if (!uploadedFiles || uploadedFiles.length === 0) return;

    const file = uploadedFiles[0];

    // Check if it's a CSV file
    if (!file.name.toLowerCase().endsWith(".csv") && file.type !== "text/csv") {
      alert("Please upload a CSV file");
      return;
    }

    // Set the files for the chat submission
    setFiles(uploadedFiles);

    // Auto-submit with a message about the CSV
    const message = `I've uploaded a CSV file "${file.name}". Please analyze its structure and suggest how to map the fields for invoice import.`;
    handleInputChange({ target: { value: message } } as any);
  };

  const renderToolInvocation = (toolInvocation: any) => {
    const { toolName, state, args, result } = toolInvocation;

    switch (state) {
      case "call":
        return (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 my-2">
            <div className="flex items-center gap-2 text-blue-700 font-medium mb-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Fetching {toolName.replace(/([A-Z])/g, " $1").toLowerCase()}...
            </div>
            {args && Object.keys(args).length > 0 && <div className="text-sm text-blue-600">Parameters: {JSON.stringify(args, null, 2)}</div>}
          </div>
        );

      case "result":
        return (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 my-2">
            <div className="text-green-700 font-medium mb-2">✓ {toolName.replace(/([A-Z])/g, " $1")}</div>
            <div className="text-sm">{renderToolResult(toolName, result)}</div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderToolResult = (toolName: string, result: any) => {
    switch (toolName) {
      case "getInvoiceStats":
      case "getInvoiceStatsByYear":
        return (
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              Total Invoices: <Badge variant="secondary">{result.total}</Badge>
            </div>
            <div>
              With VAT: <Badge variant="secondary">{result.withVAT}</Badge>
            </div>
            <div>
              Without VAT: <Badge variant="secondary">{result.withoutVAT}</Badge>
            </div>
            <div>
              Reverse Charge: <Badge variant="secondary">{result.reverseCharge}</Badge>
            </div>
            <div className="col-span-2">
              <Separator className="my-2" />
              <div>
                Total Gross: <Badge variant="outline">{currency.formatMonetary(result.totalAmountGross, "EUR")}</Badge>
              </div>
              <div>
                Total Net: <Badge variant="outline">{currency.formatMonetary(result.totalAmountNet, "EUR")}</Badge>
              </div>
              <div>
                Total VAT: <Badge variant="outline">{currency.formatMonetary(result.totalAmountVat, "EUR")}</Badge>
              </div>
            </div>
          </div>
        );

      case "getTopVendorsByCount":
      case "getTopVendorsByAmount":
        return (
          <div className="space-y-2">
            {result.map((vendor: any, index: number) => (
              <div key={vendor.id} className="flex justify-between items-center p-2 bg-white rounded border">
                <div>
                  <div className="font-medium">
                    {index + 1}. {vendor.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {vendor.city}, {vendor.country}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm">{vendor.invoiceCount} invoices</div>
                  {vendor.totalAmount && <div className="text-xs text-gray-600">{currency.formatMonetary(vendor.totalAmount, "EUR")}</div>}
                </div>
              </div>
            ))}
          </div>
        );

      case "getAvailableYears":
        return (
          <div className="flex flex-wrap gap-1">
            {result.map((year: number) => (
              <Badge key={year} variant="outline">
                {year}
              </Badge>
            ))}
          </div>
        );

      case "getMonthlyInvoiceSummary":
        return (
          <div className="space-y-1">
            {result.map((month: any) => (
              <div key={month.month} className="flex justify-between items-center p-2 bg-white rounded border text-sm">
                <div className="font-medium">{month.month}</div>
                <div className="text-right">
                  <div>{month.count} invoices</div>
                  <div className="text-xs text-gray-600">Gross: {currency.formatMonetary(month.totalGross, "EUR")}</div>
                </div>
              </div>
            ))}
          </div>
        );

      case "getInvoiceStatsByCurrency":
        return (
          <div className="space-y-1">
            {result.map((currencyData: any) => (
              <div key={currencyData.currencyCode} className="flex justify-between items-center p-2 bg-white rounded border text-sm">
                <div className="font-medium">{currencyData.currencyCode}</div>
                <div className="text-right">
                  <div>{currencyData.count} invoices</div>
                  <div className="text-xs text-gray-600">Total: {currency.formatMonetary(currencyData.totalGross, currencyData.currencyCode)}</div>
                </div>
              </div>
            ))}
          </div>
        );

      case "searchInvoices":
      case "getRecentInvoices":
        return (
          <div className="space-y-2">
            {result.map((invoice: any) => (
              <div key={invoice.id} className="p-2 bg-white rounded border text-sm">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium">{invoice.invoiceReference}</div>
                    <div className="text-xs text-gray-500">{invoice.vendorName}</div>
                    <div className="text-xs text-gray-500">{invoice.vendorLocation}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{currency.formatMonetary(invoice.amountGross, invoice.currencyCode)}</div>
                    <div className="text-xs text-gray-500">{invoice.date}</div>
                    <div className="flex gap-1 mt-1">
                      {invoice.hasVAT && (
                        <Badge variant="secondary" className="text-xs">
                          VAT
                        </Badge>
                      )}
                      {invoice.isReverseCharge && (
                        <Badge variant="secondary" className="text-xs">
                          RC
                        </Badge>
                      )}
                      {invoice.validatedAt && (
                        <Badge variant="outline" className="text-xs">
                          Validated
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );

      case "processCSVFile":
        return (
          <div className="space-y-3">
            {result.success ? (
              <>
                <div className="text-sm font-medium text-green-700">✓ CSV Analysis Complete</div>
                <div className="text-sm">{result.summary}</div>

                {result.headers && (
                  <div>
                    <div className="text-sm font-medium mb-1">Headers ({result.headers.length}):</div>
                    <div className="flex flex-wrap gap-1">
                      {result.headers.map((header: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {header}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {result.preview && result.preview.length > 0 && (
                  <div>
                    <div className="text-sm font-medium mb-1">Preview (first {result.preview.length} rows):</div>
                    <div className="bg-gray-50 p-2 rounded text-xs overflow-auto max-h-32">
                      <pre>{JSON.stringify(result.preview, null, 2)}</pre>
                    </div>
                  </div>
                )}

                {result.columnTypes && (
                  <div>
                    <div className="text-sm font-medium mb-1">Column Types:</div>
                    <div className="grid grid-cols-2 gap-1 text-xs">
                      {Object.entries(result.columnTypes).map(([column, type]) => (
                        <div key={column} className="flex justify-between">
                          <span className="font-medium">{column}:</span>
                          <span className="text-gray-600">{String(type)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-sm text-red-600">Error: {result.error}</div>
            )}
          </div>
        );

      case "mapCSVToInvoiceFields":
        return (
          <div className="space-y-3">
            <div className="text-sm font-medium text-blue-700">📋 Invoice Field Mapping Analysis</div>
            <div className="text-sm">{result.instructions}</div>

            {result.csvHeaders && (
              <div>
                <div className="text-sm font-medium mb-1">CSV Headers:</div>
                <div className="flex flex-wrap gap-1">
                  {result.csvHeaders.map((header: string, index: number) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {header}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {result.availableInvoiceFields && (
              <div>
                <div className="text-sm font-medium mb-1">Available Invoice Fields:</div>
                <div className="text-xs space-y-1">
                  {result.availableInvoiceFields.map((field: string, index: number) => (
                    <div key={index} className="text-gray-600">
                      • {field}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      default:
        return <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">{JSON.stringify(result, null, 2)}</pre>;
    }
  };

  return (
    <DashboardLayout title="AI Assistant">
      <div className="flex flex-col h-[calc(100vh-8rem)]">
        <div className="mb-4">
          <h1 className="text-3xl font-bold tracking-tight">AI Assistant</h1>
          <p className="text-muted-foreground">Ask questions about your invoice data and get instant insights</p>
        </div>

        {/* Chat Messages */}
        <Card className="flex-1 flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Chat</CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col p-0">
            <ScrollArea className="flex-1 px-6" ref={scrollAreaRef}>
              <div className="space-y-4 pb-4">
                {messages.length === 0 && (
                  <div className="text-center py-8">
                    <Bot className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to your AI Assistant</h3>
                    <p className="text-gray-500 mb-6">Ask me anything about your invoices and I'll help you find the information you need.</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-w-2xl mx-auto">
                      {suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="text-left justify-start h-auto py-2 px-3"
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {messages.map((message) => (
                  <div key={message.id} className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                    <div className={`flex gap-3 max-w-[80%] ${message.role === "user" ? "flex-row-reverse" : "flex-row"}`}>
                      <div
                        className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                          message.role === "user" ? "bg-blue-500" : "bg-gray-500"
                        }`}
                      >
                        {message.role === "user" ? <User className="h-4 w-4 text-white" /> : <Bot className="h-4 w-4 text-white" />}
                      </div>
                      <div className={`rounded-lg px-4 py-2 ${message.role === "user" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-900"}`}>
                        {message.parts?.map((part, index) => {
                          if (part.type === "text") {
                            return (
                              <div key={index} className="whitespace-pre-wrap">
                                {part.text}
                              </div>
                            );
                          } else if (part.type === "tool-invocation") {
                            return <div key={index}>{renderToolInvocation(part.toolInvocation)}</div>;
                          }
                          return null;
                        }) || <div className="whitespace-pre-wrap">{message.content}</div>}
                      </div>
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="flex gap-3 max-w-[80%]">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-gray-500">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                      <div className="rounded-lg px-4 py-2 bg-gray-100 text-gray-900">
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Thinking...
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Input Form */}
            <div className="border-t p-4">
              <form
                onSubmit={(event) => {
                  handleSubmit(event, {
                    experimental_attachments: files,
                  });
                  setFiles(undefined);
                  if (fileInputRef.current) {
                    fileInputRef.current.value = "";
                  }
                }}
                className="space-y-2"
              >
                {/* File Upload */}
                <div className="flex gap-2">
                  <input type="file" ref={fileInputRef} onChange={handleFileUpload} accept=".csv,text/csv" className="hidden" id="csv-upload" />
                  <label
                    htmlFor="csv-upload"
                    className="flex items-center gap-2 px-3 py-2 text-sm border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50"
                  >
                    <Upload className="h-4 w-4" />
                    Upload CSV
                  </label>
                  {files && files.length > 0 && (
                    <div className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 border border-blue-200 rounded-md">
                      <FileText className="h-4 w-4 text-blue-600" />
                      <span className="text-blue-700">{files[0].name}</span>
                    </div>
                  )}
                </div>

                {/* Text Input */}
                <div className="flex gap-2">
                  <Input
                    value={input}
                    onChange={handleInputChange}
                    placeholder="Ask me about your invoices or upload a CSV file..."
                    disabled={isLoading}
                    className="flex-1"
                  />
                  <Button type="submit" disabled={isLoading || (!input.trim() && !files)}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </form>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
