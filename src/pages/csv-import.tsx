import React, { useState } from "react";
import { NextPage } from "next";
import Head from "next/head";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CSVUploadForm } from "@/components/CSVUploadForm";
import { DashboardLayout } from "@/components/DashboardLayout";
import { trpc } from "@/utils/trpc";
import { Upload, FileText, TrendingUp, DollarSign } from "lucide-react";

const CSVImportPage: NextPage = () => {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  // Fetch some stats to show on the page
  const { data: accountStats } = trpc.accounts.getStats.useQuery();
  const { data: transactionStats } = trpc.transactions.getStats.useQuery();

  return (
    <>
      <Head>
        <title>CSV Import - SAC Accounting</title>
        <meta name="description" content="Import transactions from CSV files" />
      </Head>

      <DashboardLayout title="CSV Import">
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Accounts</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{accountStats?.total || 0}</div>
                <p className="text-xs text-muted-foreground">{accountStats?.withTransactions || 0} with transactions</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{transactionStats?.total || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {transactionStats?.withTransfers || 0} transfers, {transactionStats?.withTrades || 0} trades
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Account Types</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{accountStats?.byType ? Object.keys(accountStats.byType).length : 0}</div>
                <p className="text-xs text-muted-foreground">Different account types</p>
              </CardContent>
            </Card>
          </div>

          {/* Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle>Import CSV Files</CardTitle>
              <CardDescription>
                Upload CSV files to import transactions. Each row will be mapped to transfers and trades based on the file format.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Button onClick={() => setUploadDialogOpen(true)} className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    Upload CSV Files
                  </Button>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Supported Formats</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>
                      • <strong>Binance Credit Card:</strong> Automatically detects Binance card transaction format
                    </li>
                    <li>
                      • <strong>Multiple Files:</strong> Upload multiple CSV files at once
                    </li>
                    <li>
                      • <strong>Account Selection:</strong> Choose existing account or create new one
                    </li>
                    <li>
                      • <strong>Transaction Mapping:</strong> Each row creates transfers and trades from assets/exchange rates
                    </li>
                  </ul>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-900 mb-2">Expected CSV Structure (Binance)</h4>
                  <div className="text-sm text-yellow-800 space-y-1">
                    <p>
                      <strong>Required columns:</strong>
                    </p>
                    <ul className="ml-4 space-y-1">
                      <li>• Timestamp</li>
                      <li>• Description</li>
                      <li>• Paid OUT (EUR) / Paid IN (EUR)</li>
                      <li>• Transaction Fee (EUR)</li>
                      <li>• Assets Used</li>
                      <li>• Exchange Rates</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest imported transactions will appear here</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No recent transactions</p>
                <p className="text-sm">Import some CSV files to see activity here</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>

      <CSVUploadForm
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        onSuccess={() => {
          // Refresh stats after successful import
          // The trpc queries will automatically refetch
        }}
      />
    </>
  );
};

export default CSVImportPage;
