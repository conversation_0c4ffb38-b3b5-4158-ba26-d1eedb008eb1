import React, { useState } from "react";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Wallet, TrendingUp, Activity, Upload, Filter, X, Trash2 } from "lucide-react";
import { AccountDataTable } from "@/components/AccountDataTable";
import { AccountDetailsDrawer } from "@/components/AccountDetailsDrawer";
import { AccountForm } from "@/components/AccountForm";
import { CSVUploadForm } from "@/components/CSVUploadForm";
import { DashboardLayout } from "@/components/DashboardLayout";
import { AccountType } from "@/prisma/generated";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const getAccountTypeLabel = (type: AccountType) => {
  switch (type) {
    case "WALLET":
      return "Wallets";
    case "BANK_ACCOUNT":
      return "Bank Accounts";
    case "EXCHANGE_ACCOUNT":
      return "Exchange Accounts";
    case "CREDIT_CARD":
      return "Credit Cards";
    default:
      return type;
  }
};

export default function AccountsPage() {
  const [limit, setLimit] = useState(20);
  const [cursor, setCursor] = useState<string | undefined>(undefined);
  const [cursors, setCursors] = useState<string[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [editingAccountId, setEditingAccountId] = useState<string | undefined>(undefined);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState<string | null>(null);
  const [csvUploadOpen, setCsvUploadOpen] = useState(false);
  const [csvImportAccountId, setCsvImportAccountId] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState<AccountType | undefined>(undefined);
  const [predefinedFilter, setPredefinedFilter] = useState<"all" | "wallets" | "exclude-wallets">("all");
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);

  const utils = trpc.useUtils();

  // Check if filters are active
  const hasFilters = predefinedFilter !== "all" || selectedType !== undefined;

  // Fetch account stats
  const stats = trpc.accounts.getStats.useQuery();

  // Fetch accounts with pagination
  const getQueryParams = () => {
    const baseParams = {
      limit,
      ...(cursor && { cursor }),
    };

    if (predefinedFilter === "wallets") {
      return { ...baseParams, type: "WALLET" as AccountType };
    }

    if (predefinedFilter === "exclude-wallets") {
      // For exclude wallets, we'll filter on the frontend since API doesn't support it
      return baseParams;
    }

    if (selectedType) {
      return { ...baseParams, type: selectedType };
    }

    return baseParams;
  };

  const accounts = trpc.accounts.getAll.useQuery(getQueryParams());

  // Filter accounts based on predefined filter
  const filteredAccounts = React.useMemo(() => {
    if (!accounts.data?.items) return accounts.data;

    if (predefinedFilter === "exclude-wallets") {
      return {
        ...accounts.data,
        items: accounts.data.items.filter((account) => account.type !== "WALLET"),
      };
    }

    return accounts.data;
  }, [accounts.data, predefinedFilter]);

  // Delete mutation
  const deleteMutation = trpc.accounts.delete.useMutation({
    onSuccess: () => {
      toast.success("Account deleted successfully");
      utils.accounts.getAll.invalidate();
      utils.accounts.getStats.invalidate();
      setDeleteDialogOpen(false);
      setAccountToDelete(null);
    },
    onError: (error) => {
      toast.error(`Failed to delete account: ${error.message}`);
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = trpc.accounts.bulkDelete.useMutation({
    onSuccess: (result) => {
      toast.success(`Successfully deleted ${result.deletedCount} account${result.deletedCount === 1 ? "" : "s"}`);
      utils.accounts.getAll.invalidate();
      utils.accounts.getStats.invalidate();
      setBulkDeleteDialogOpen(false);
      // Reset filters after bulk delete
      setPredefinedFilter("all");
      setSelectedType(undefined);
      setCursor(undefined);
      setCursors([]);
    },
    onError: (error) => {
      toast.error(`Failed to delete accounts: ${error.message}`);
    },
  });

  const handleNextPage = () => {
    if (accounts.data?.nextCursor) {
      setCursors((prev) => [...prev, cursor || ""]);
      setCursor(accounts.data.nextCursor);
    }
  };

  const handlePrevPage = () => {
    if (cursors.length > 0) {
      const newCursors = [...cursors];
      const prevCursor = newCursors.pop();
      setCursors(newCursors);
      setCursor(prevCursor === "" ? undefined : prevCursor);
    }
  };

  const handleLimitChange = (newLimit: string) => {
    setLimit(Number(newLimit));
    setCursor(undefined);
    setCursors([]);
  };

  const handleViewAccount = (accountId: string) => {
    setSelectedAccountId(accountId);
    setDrawerOpen(true);
  };

  const handleEditAccount = (accountId: string) => {
    setEditingAccountId(accountId);
    setFormOpen(true);
  };

  const handleDeleteAccount = (accountId: string) => {
    setAccountToDelete(accountId);
    setDeleteDialogOpen(true);
  };

  const handleCreateAccount = () => {
    setEditingAccountId(undefined);
    setFormOpen(true);
  };

  const handleImportCSV = (accountId: string) => {
    setCsvImportAccountId(accountId);
    setCsvUploadOpen(true);
  };

  const confirmDelete = () => {
    if (accountToDelete) {
      deleteMutation.mutate({ id: accountToDelete });
    }
  };

  const handleTypeFilter = (type: string) => {
    if (type === "all") {
      setSelectedType(undefined);
    } else {
      setSelectedType(type as AccountType);
    }
    setPredefinedFilter("all"); // Reset predefined filter when using dropdown
    setCursor(undefined);
    setCursors([]);
  };

  const handlePredefinedFilter = (filter: "all" | "wallets" | "exclude-wallets") => {
    setPredefinedFilter(filter);
    setSelectedType(undefined); // Reset dropdown filter when using predefined
    setCursor(undefined);
    setCursors([]);
  };

  const handleBulkDelete = () => {
    setBulkDeleteDialogOpen(true);
  };

  const confirmBulkDelete = () => {
    const deleteParams: { type?: AccountType; excludeWallets?: boolean } = {};

    if (predefinedFilter === "wallets") {
      deleteParams.type = "WALLET";
    } else if (predefinedFilter === "exclude-wallets") {
      deleteParams.excludeWallets = true;
    } else if (selectedType) {
      deleteParams.type = selectedType;
    }

    bulkDeleteMutation.mutate(deleteParams);
  };

  return (
    <DashboardLayout title="Accounts">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Accounts</h1>
            <p className="text-muted-foreground">Manage your financial accounts and track transactions</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleCreateAccount}>
              <Plus className="mr-2 h-4 w-4" />
              Add Account
            </Button>
            <Button variant="outline" onClick={() => setCsvUploadOpen(true)}>
              <Upload className="mr-2 h-4 w-4" />
              Import CSV
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Accounts</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.total || 0}</div>
              <p className="text-xs text-muted-foreground">All account types</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">With Transactions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withTransactions || 0}</div>
              <p className="text-xs text-muted-foreground">Active accounts</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bank Accounts</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.byType?.BANK_ACCOUNT || 0}</div>
              <p className="text-xs text-muted-foreground">Traditional banking</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Wallets</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.byType?.WALLET || 0}</div>
              <p className="text-xs text-muted-foreground">Digital wallets</p>
            </CardContent>
          </Card>
        </div>

        {/* Predefined Filters */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Quick filters:</span>
          </div>
          <div className="flex gap-2">
            <Button variant={predefinedFilter === "all" ? "default" : "outline"} size="sm" onClick={() => handlePredefinedFilter("all")} className="text-xs">
              All Accounts
            </Button>
            <Button
              variant={predefinedFilter === "wallets" ? "default" : "outline"}
              size="sm"
              onClick={() => handlePredefinedFilter("wallets")}
              className="text-xs"
            >
              <Wallet className="mr-1 h-3 w-3" />
              Wallets Only
            </Button>
            <Button
              variant={predefinedFilter === "exclude-wallets" ? "default" : "outline"}
              size="sm"
              onClick={() => handlePredefinedFilter("exclude-wallets")}
              className="text-xs"
            >
              <X className="mr-1 h-3 w-3" />
              Exclude Wallets
            </Button>
          </div>
          {(predefinedFilter !== "all" || selectedType) && (
            <div className="text-xs text-muted-foreground">
              Active filter:{" "}
              {predefinedFilter === "wallets"
                ? "Wallets Only"
                : predefinedFilter === "exclude-wallets"
                ? "Exclude Wallets"
                : selectedType
                ? getAccountTypeLabel(selectedType)
                : "All"}
            </div>
          )}

          {/* Bulk Delete Button - only show when filters are active */}
          {hasFilters && (
            <Button variant="destructive" size="sm" onClick={handleBulkDelete} disabled={bulkDeleteMutation.isPending} className="ml-2">
              <Trash2 className="h-4 w-4 mr-2" />
              {bulkDeleteMutation.isPending ? "Deleting..." : "Delete Filtered"}
            </Button>
          )}
        </div>

        {/* Filters */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <label htmlFor="type-filter" className="text-sm font-medium">
              Filter by type:
            </label>
            <Select value={predefinedFilter !== "all" ? "all" : selectedType || "all"} onValueChange={handleTypeFilter} disabled={predefinedFilter !== "all"}>
              <SelectTrigger className="w-48" id="type-filter">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {Object.values(AccountType).map((type) => (
                  <SelectItem key={type} value={type}>
                    {getAccountTypeLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <label htmlFor="limit-select" className="text-sm font-medium">
              Show:
            </label>
            <Select value={limit.toString()} onValueChange={handleLimitChange}>
              <SelectTrigger className="w-20" id="limit-select">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Accounts Table */}
        <Card>
          <CardHeader>
            <CardTitle>Accounts</CardTitle>
            <CardDescription>
              {filteredAccounts?.items.length || 0} account{filteredAccounts?.items.length !== 1 ? "s" : ""} found
            </CardDescription>
          </CardHeader>
          <CardContent>
            {accounts.isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading accounts...</div>
              </div>
            ) : accounts.error ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-destructive">Failed to load accounts: {accounts.error.message}</div>
              </div>
            ) : (
              <AccountDataTable
                data={filteredAccounts?.items || []}
                onViewAccount={handleViewAccount}
                onEditAccount={handleEditAccount}
                onDeleteAccount={handleDeleteAccount}
                onImportCSV={handleImportCSV}
              />
            )}

            {/* Pagination */}
            {filteredAccounts && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredAccounts.items.length} account{filteredAccounts.items.length !== 1 ? "s" : ""}
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={cursors.length === 0}>
                    Previous
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleNextPage} disabled={!accounts.data?.nextCursor}>
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Account Details Drawer */}
      <AccountDetailsDrawer accountId={selectedAccountId} open={drawerOpen} onOpenChange={setDrawerOpen} />

      {/* Account Form Dialog */}
      <AccountForm open={formOpen} onOpenChange={setFormOpen} accountId={editingAccountId} />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the account. Note: Accounts with existing transactions cannot be deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Filtered Accounts</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete all accounts matching the current filters and all their associated data.
              {filteredAccounts && (
                <div className="mt-2 p-2 bg-muted rounded">
                  <strong>
                    This will delete {filteredAccounts.items.length} account{filteredAccounts.items.length === 1 ? "" : "s"}
                  </strong>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {bulkDeleteMutation.isPending ? "Deleting..." : "Delete All Filtered"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* CSV Upload Dialog */}
      <CSVUploadForm
        open={csvUploadOpen}
        onOpenChange={(open) => {
          setCsvUploadOpen(open);
          if (!open) {
            setCsvImportAccountId(null);
          }
        }}
        preselectedAccountId={csvImportAccountId || undefined}
        onSuccess={() => {
          // Refresh accounts and stats after successful import
          utils.accounts.getAll.invalidate();
          utils.accounts.getStats.invalidate();
        }}
      />
    </DashboardLayout>
  );
}
