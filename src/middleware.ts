import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  // Only apply HTTP auth when running on Vercel
  if (!process.env.VERCEL) {
    return NextResponse.next();
  }

  // Skip auth for API routes that might need to be public
  // You can customize this list based on your needs
  const publicPaths = ["/api/health", "/api/webhook"];

  if (publicPaths.some((path) => request.nextUrl.pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // Get HTTP auth credentials from environment variables
  const username = process.env.HTTP_AUTH_USERNAME || "admin";
  const password = process.env.HTTP_AUTH_PASSWORD || "password";

  // Check for Authorization header
  const authHeader = request.headers.get("authorization");

  if (!authHeader || !authHeader.startsWith("Basic ")) {
    return new NextResponse("Authentication required", {
      status: 401,
      headers: {
        "WWW-Authenticate": 'Basic realm="Secure Area"',
      },
    });
  }

  // Decode and verify credentials
  const base64Credentials = authHeader.split(" ")[1];
  const credentials = Buffer.from(base64Credentials, "base64").toString("ascii");
  const [providedUsername, providedPassword] = credentials.split(":");

  if (providedUsername !== username || providedPassword !== password) {
    return new NextResponse("Invalid credentials", {
      status: 401,
      headers: {
        "WWW-Authenticate": 'Basic realm="Secure Area"',
      },
    });
  }

  // Authentication successful, continue to the requested page
  return NextResponse.next();
}

export const config = {
  // Apply middleware to all routes except static files and Next.js internals
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
