import { useState, useEffect } from 'react';

const STORAGE_KEY = 'invoice-pdf-display-setting';

export function useInvoicePdfSetting() {
  const [showPdfAlongside, setShowPdfAlongside] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load setting from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved !== null) {
        setShowPdfAlongside(JSON.parse(saved));
      }
    } catch (error) {
      console.warn('Failed to load PDF display setting from localStorage:', error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save setting to localStorage when it changes
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(showPdfAlongside));
      } catch (error) {
        console.warn('Failed to save PDF display setting to localStorage:', error);
      }
    }
  }, [showPdfAlongside, isLoaded]);

  return {
    showPdfAlongside,
    setShowPdfAlongside,
    isLoaded,
  };
}
