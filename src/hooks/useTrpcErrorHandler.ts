import { toast } from "sonner";
import { TRPCClientError } from "@trpc/client";
import { AppRouter } from "@/server/routers/_app";

/**
 * Custom hook for handling tRPC errors with beautiful toast messages
 */
export function useTrpcErrorHandler() {
  const handleError = (error: any, customMessage?: string) => {
    // Extract error details
    const errorMessage = error.message;
    const errorCode = error.data?.code;
    const zodErrors = error.data?.zodError;

    // Create a user-friendly error message
    let displayMessage = customMessage || "An error occurred";
    let description = errorMessage;

    // Handle specific error types
    switch (errorCode) {
      case "BAD_REQUEST":
        displayMessage = customMessage || "Invalid request";
        break;
      case "UNAUTHORIZED":
        displayMessage = customMessage || "Authentication required";
        description = "Please log in to continue";
        break;
      case "FORBIDDEN":
        displayMessage = customMessage || "Access denied";
        description = "You don't have permission to perform this action";
        break;
      case "NOT_FOUND":
        displayMessage = customMessage || "Resource not found";
        description = "The requested item could not be found";
        break;
      case "TIMEOUT":
        displayMessage = customMessage || "Request timeout";
        description = "The request took too long to complete. Please try again.";
        break;
      case "CONFLICT":
        displayMessage = customMessage || "Conflict detected";
        description = "This action conflicts with existing data";
        break;
      case "PRECONDITION_FAILED":
        displayMessage = customMessage || "Precondition failed";
        break;
      case "PAYLOAD_TOO_LARGE":
        displayMessage = customMessage || "File too large";
        description = "The uploaded file exceeds the size limit";
        break;
      case "TOO_MANY_REQUESTS":
        displayMessage = customMessage || "Too many requests";
        description = "Please wait a moment before trying again";
        break;
      case "INTERNAL_SERVER_ERROR":
        displayMessage = customMessage || "Server error";
        description = "Something went wrong on our end. Please try again later.";
        break;
      case "PARSE_ERROR":
        displayMessage = customMessage || "Invalid data format";
        description = "The data format is not valid";
        break;
      default:
        displayMessage = customMessage || "Something went wrong";
    }

    // Handle Zod validation errors
    if (zodErrors) {
      displayMessage = customMessage || "Validation error";
      const fieldErrors = zodErrors.fieldErrors;
      if (fieldErrors && Object.keys(fieldErrors).length > 0) {
        const firstField = Object.keys(fieldErrors)[0];
        const firstError = fieldErrors[firstField]?.[0];
        if (firstError) {
          description = `${firstField}: ${firstError}`;
        }
      }
    }

    // Show error toast with beautiful styling
    toast.error(displayMessage, {
      description,
      duration: 5000,
      action: {
        label: "Dismiss",
        onClick: () => {},
      },
    });

    // Log error for debugging in development
    if (process.env.NODE_ENV === "development") {
      console.error("tRPC Error:", {
        message: errorMessage,
        code: errorCode,
        zodErrors,
        fullError: error,
      });
    }
  };

  return { handleError };
}

/**
 * Utility function to create error handlers for mutations
 */
export function createMutationErrorHandler(customMessage?: string) {
  return (error: TRPCClientError<AppRouter>) => {
    const { handleError } = useTrpcErrorHandler();
    handleError(error, customMessage);
  };
}

/**
 * Utility function to create error handlers for queries
 */
export function createQueryErrorHandler(customMessage?: string) {
  return (error: TRPCClientError<AppRouter>) => {
    const { handleError } = useTrpcErrorHandler();
    handleError(error, customMessage);
  };
}
