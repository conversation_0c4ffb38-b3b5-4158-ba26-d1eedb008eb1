{"cells": [{"language": "typescript", "source": ["import { gmail_v1, google } from \"googleapis\";\n\ngoogle._options\n\nconst product = \"sdfdsf\""], "outputs": []}, {"language": "typescript", "source": ["const { gmail_v1, google } = require(\"googleapis\") as typeof import(\"googleapis\");\nconst { JWT } = require(\"google-auth-library\") as typeof import(\"google-auth-library\");\nconst fs = require(\"fs\") as typeof import(\"fs\");\nconst PQueue = require(\"p-queue\") as typeof import(\"p-queue\");\nconst R = require(\"remeda\") as typeof import(\"remeda\");\nconst pMap = require(\"p-map\") as typeof import(\"p-map\");\n\n// ---- service-account credentials JSON ----\nconst currentFilepath = __dirname;\n\nconsole.log(currentFilepath);\n\nconst accountFilePath = `../../../misc/strange-bloom-377908-acc3f0e78791.json`;\n\nconst doesFileExist = fs.existsSync(accountFilePath);\n\nconst serviceAccount = JSON.parse(fs.readFileSync(accountFilePath, \"utf8\"));\n\n\n\nconst SCOPES = [\"https://www.googleapis.com/auth/gmail.readonly\", \"https://www.googleapis.com/auth/admin.directory.user.readonly\"];\n\nconst adminUser = \"<EMAIL>\"; // the account that granted delegation\n\nconst auth = new JWT({\n  email: serviceAccount.client_email,\n  key: serviceAccount.private_key,\n  scopes: SCOPES,\n  subject: adminUser, // will be overwritten per-user below\n});\n\nconst gmail = google.gmail({ version: \"v1\", auth });\nconst directory = google.admin({ version: \"directory_v1\", auth });"], "outputs": [{"items": [{"mime": "application/vnd.code.notebook.stdout", "value": ["/Users/<USER>/desc/sac-accounting/sac-accounting-app", ""]}]}, {"items": [{"mime": "application/vnd.code.notebook.error", "value": {"name": "Error", "message": "ENOENT: no such file or directory, open '../../../misc/strange-bloom-377908-acc3f0e78791.json'", "stack": "    at Object.readFileSync (node:fs:442:20)\n    at <Cell 2> [17, 33]\n    at <Cell 2> [24, 46]\n    at Script.runInContext (node:vm:149:12)\n    at Script.runInNewContext (node:vm:154:17)\n    at Object.runInNewContext (node:vm:310:38)\n    at C (/Users/<USER>/.cursor/extensions/donjayamanne.typescript-notebook-2.0.6/out/extension/server/index.js:2:113345)\n    at t.execCode (/Users/<USER>/.cursor/extensions/donjayamanne.typescript-notebook-2.0.6/out/extension/server/index.js:2:114312)\n    at k.<anonymous> (/Users/<USER>/.cursor/extensions/donjayamanne.typescript-notebook-2.0.6/out/extension/server/index.js:2:142156)\n    at k.emit (node:events:518:28)"}}]}]}, {"language": "typescript", "source": ["product."], "outputs": []}, {"language": "typescript", "source": ["const category =\"sdfsd\""], "outputs": []}, {"language": "typescript", "source": ["category."], "outputs": []}]}