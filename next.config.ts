import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  // webpack: (config, { isServer }) => {
  //   // Exclude test files from the build
  //   config.module.rules.push({
  //     test: /\.test\.(ts|tsx|js|jsx)$/,
  //     use: "null-loader",
  //   });

  //   // Also exclude files that start with 'test'
  //   config.module.rules.push({
  //     test: /\/test[^/]*\.(ts|tsx|js|jsx)$/,
  //     use: "null-loader",
  //   });

  //   return config;
  // },
};

export default nextConfig;
