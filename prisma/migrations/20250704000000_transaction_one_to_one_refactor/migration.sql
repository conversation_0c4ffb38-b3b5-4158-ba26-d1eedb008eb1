-- Migration to refactor Transaction-Transfer-Trade relationships from one-to-many to one-to-one

-- First, we need to handle existing data that might violate the unique constraint
-- This migration assumes that if there are multiple transfers/trades per transaction,
-- we need to create separate transactions for each

-- Step 1: Create temporary tables to store the data we need to restructure
CREATE TEMP TABLE temp_multi_transfers AS
SELECT 
    t.id as transaction_id,
    t.dbCreatedAt,
    t.executedAt,
    t.metadata,
    t.accountId,
    tr.id as transfer_id,
    tr.counterparty,
    tr.amount,
    tr.currencyCode,
    tr.description,
    tr.dbCreatedAt as transfer_created_at,
    ROW_NUMBER() OVER (PARTITION BY t.id ORDER BY tr.dbCreatedAt) as rn
FROM "Transaction" t
INNER JOIN "Transfer" tr ON t.id = tr.transactionId
WHERE t.id IN (
    SELECT transactionId 
    FROM "Transfer" 
    GROUP BY transactionId 
    HAVING COUNT(*) > 1
);

CREATE TEMP TABLE temp_multi_trades AS
SELECT 
    t.id as transaction_id,
    t.dbCreatedAt,
    t.executedAt,
    t.metadata,
    t.accountId,
    td.id as trade_id,
    td.poolId,
    td.description,
    td.tokenFrom,
    td.tokenTo,
    td.amountFrom,
    td.amountTo,
    td.dbCreatedAt as trade_created_at,
    ROW_NUMBER() OVER (PARTITION BY t.id ORDER BY td.dbCreatedAt) as rn
FROM "Transaction" t
INNER JOIN "Trade" td ON t.id = td.transactionId
WHERE t.id IN (
    SELECT transactionId 
    FROM "Trade" 
    GROUP BY transactionId 
    HAVING COUNT(*) > 1
);

-- Step 2: Create new transactions for additional transfers (keeping the first one with original transaction)
INSERT INTO "Transaction" (id, dbCreatedAt, executedAt, metadata, accountId)
SELECT 
    gen_random_uuid(),
    dbCreatedAt,
    executedAt,
    metadata,
    accountId
FROM temp_multi_transfers
WHERE rn > 1;

-- Step 3: Update transfer records to point to new transactions
WITH new_transactions AS (
    SELECT 
        t.id as new_transaction_id,
        mt.transfer_id,
        ROW_NUMBER() OVER (ORDER BY t.dbCreatedAt) as rn
    FROM "Transaction" t
    CROSS JOIN (SELECT transfer_id FROM temp_multi_transfers WHERE rn > 1 ORDER BY transaction_id, rn) mt
    WHERE t.id NOT IN (SELECT DISTINCT transaction_id FROM temp_multi_transfers)
    AND t.dbCreatedAt >= (SELECT MIN(dbCreatedAt) FROM temp_multi_transfers WHERE rn > 1)
)
UPDATE "Transfer" 
SET transactionId = nt.new_transaction_id
FROM new_transactions nt
WHERE "Transfer".id = nt.transfer_id;

-- Step 4: Create new transactions for additional trades (keeping the first one with original transaction)
INSERT INTO "Transaction" (id, dbCreatedAt, executedAt, metadata, accountId)
SELECT 
    gen_random_uuid(),
    dbCreatedAt,
    executedAt,
    metadata,
    accountId
FROM temp_multi_trades
WHERE rn > 1;

-- Step 5: Update trade records to point to new transactions
WITH new_transactions AS (
    SELECT 
        t.id as new_transaction_id,
        mt.trade_id,
        ROW_NUMBER() OVER (ORDER BY t.dbCreatedAt) as rn
    FROM "Transaction" t
    CROSS JOIN (SELECT trade_id FROM temp_multi_trades WHERE rn > 1 ORDER BY transaction_id, rn) mt
    WHERE t.id NOT IN (SELECT DISTINCT transaction_id FROM temp_multi_trades)
    AND t.id NOT IN (SELECT DISTINCT transaction_id FROM temp_multi_transfers)
    AND t.dbCreatedAt >= (SELECT MIN(dbCreatedAt) FROM temp_multi_trades WHERE rn > 1)
)
UPDATE "Trade" 
SET transactionId = nt.new_transaction_id
FROM new_transactions nt
WHERE "Trade".id = nt.trade_id;

-- Step 6: Add transactionGroupId column to Transaction table
ALTER TABLE "Transaction" ADD COLUMN "transactionGroupId" TEXT;

-- Step 7: Add unique constraints to enforce one-to-one relationships
ALTER TABLE "Transfer" ADD CONSTRAINT "Transfer_transactionId_key" UNIQUE ("transactionId");
ALTER TABLE "Trade" ADD CONSTRAINT "Trade_transactionId_key" UNIQUE ("transactionId");
