-- CreateEnum
CREATE TYPE "ExternalService" AS ENUM ('GMAIL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ImportItemType" AS ENUM ('GMAIL_ATTACHMENT');

-- CreateTable
CREATE TABLE "InvoiceImportItem" (
    "id" TEXT NOT NULL,
    "externalId" TEXT NOT NULL,
    "importItemType" "ImportItemType" NOT NULL,
    "text" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isInvoice" BOOLEAN NOT NULL DEFAULT false,
    "parsedAsInvoice" TIMESTAMP(3),
    "fileUrl" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "fileHash" TEXT NOT NULL,

    CONSTRAINT "InvoiceImportItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceBaseDetails" (
    "id" TEXT NOT NULL,
    "invoiceDate" TIMESTAMP(3) NOT NULL,
    "invoiceReference" TEXT NOT NULL,
    "vendor" JSONB NOT NULL,
    "recipient" JSONB NOT NULL,
    "invoiceImportItemId" TEXT,

    CONSTRAINT "InvoiceBaseDetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Vendor" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "street" TEXT NOT NULL,
    "houseNumber" TEXT NOT NULL,
    "zip" TEXT,
    "vatNumber" TEXT,
    "bankAccount" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "contactPerson" TEXT,

    CONSTRAINT "Vendor_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Invoice" (
    "id" TEXT NOT NULL,
    "importItemId" TEXT NOT NULL,
    "invoiceReference" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "totalAmountGross" DECIMAL(65,30) NOT NULL,
    "totalAmountNet" DECIMAL(65,30),
    "totalAmountVat" DECIMAL(65,30),
    "hasVAT" BOOLEAN NOT NULL,
    "isReverseCharge" BOOLEAN NOT NULL DEFAULT false,
    "validatedAt" TIMESTAMP(3),
    "vendorId" TEXT NOT NULL,
    "recipient" JSONB NOT NULL,

    CONSTRAINT "Invoice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InvoiceLineItem" (
    "id" TEXT NOT NULL,
    "invoiceId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "priceGross" DECIMAL(65,30) NOT NULL,
    "priceNet" DECIMAL(65,30),
    "vatRate" DECIMAL(65,30),
    "quantity" INTEGER,

    CONSTRAINT "InvoiceLineItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceImportItem_externalId_key" ON "InvoiceImportItem"("externalId");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceBaseDetails_invoiceImportItemId_key" ON "InvoiceBaseDetails"("invoiceImportItemId");

-- CreateIndex
CREATE UNIQUE INDEX "Invoice_importItemId_key" ON "Invoice"("importItemId");

-- AddForeignKey
ALTER TABLE "InvoiceBaseDetails" ADD CONSTRAINT "InvoiceBaseDetails_invoiceImportItemId_fkey" FOREIGN KEY ("invoiceImportItemId") REFERENCES "InvoiceImportItem"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_importItemId_fkey" FOREIGN KEY ("importItemId") REFERENCES "InvoiceImportItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "Vendor"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceLineItem" ADD CONSTRAINT "InvoiceLineItem_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "Invoice"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
