# SAC Accounting Application - Repository Information

## Overview

SAC Accounting is a comprehensive accounting and financial management application built with Next.js, TypeScript, and PostgreSQL. The application specializes in invoice management, transaction tracking, vendor management, and cryptocurrency expense tracking with blockchain data integration.

## Technology Stack

- **Frontend**: Next.js 15.3.3 with React 19, TypeScript
- **Backend**: tRPC for type-safe API endpoints
- **Database**: PostgreSQL with Prisma ORM
- **UI Framework**: shadcn/ui components with Tailwind CSS
- **Authentication**: Better Auth (configured but not fully implemented)
- **Blockchain Integration**: Flipside Crypto API for blockchain data
- **AI Integration**: Multiple AI providers (OpenAI, Mistral, Gemini, OpenRouter)
- **File Storage**: AWS S3 compatible storage
- **Deployment**: Vercel with HTTP authentication

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Next.js)     │◄──►│   (tRPC)        │◄──►│   (PostgreSQL)  │
│                 │    │                 │    │                 │
│ - Pages         │    │ - Routers       │    │ - Prisma Schema │
│ - Components    │    │ - Mo<PERSON>les       │    │ - Models        │
│ - Hooks         │    │ - Services      │    │ - Relations     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │              ┌─────────────────┐
         │              │  External APIs  │
         └──────────────┤                 │
                        │ - Flipside      │
                        │ - AI Providers  │
                        │ - S3 Storage    │
                        └─────────────────┘
```

## Database Schema (Prisma)

### Core Entities

#### **Invoice Management**
- `InvoiceImportItem`: Raw imported invoice data from Gmail/uploads
- `Invoice`: Processed invoice with financial details
- `InvoiceLineItem`: Individual line items within invoices
- `InvoiceBaseDetails`: Basic invoice information extracted during parsing
- `Vendor`: Vendor/supplier information

#### **Financial Transactions**
- `Account`: Financial accounts (wallets, bank accounts, exchanges, credit cards)
- `Transaction`: Base transaction entity
- `Transfer`: Money transfers between accounts
- `Trade`: Cryptocurrency trades
- `Reconciliation`: Links between invoices and transfers for accounting

#### **Cryptocurrency Expenses**
- `CryptoExpense`: Base crypto expense tracking
- `CryptoBuyback`: Token buyback transactions
- `CryptoFloorSweep`: NFT purchase transactions
- `CryptoLiquidityPool`: Liquidity pool operations

#### **System Configuration**
- `Settings`: Application settings (accounting year, etc.)
- `Wallet`: Cryptocurrency wallet tracking

### Key Relationships
- Invoices ↔ Vendors (many-to-one)
- Invoices ↔ Reconciliations ↔ Transfers (many-to-many through reconciliation)
- Transactions → Account (many-to-one)
- Transactions → Transfer/Trade/CryptoExpense (one-to-one polymorphic)

## Source Code Structure (`/src`)

### Pages (`/src/pages`)
- `index.tsx` - Dashboard with statistics overview
- `invoices.tsx` - Invoice management with filtering, pagination, PDF viewing
- `transactions.tsx` - Transaction listing with account filtering
- `accounts.tsx` - Account management and CSV import
- `vendors.tsx` - Vendor management and statistics
- `reconciliation.tsx` - Invoice-transfer reconciliation interface
- `settings.tsx` - Application configuration
- `chat.tsx` - AI assistant for data queries
- `csv-import.tsx` - CSV file import interface
- `error-demo.tsx` - Error handling demonstration

### Modules (`/src/modules`)

#### **Core Business Logic**
- `invoices/` - Invoice processing, parsing, and management
- `vendors/` - Vendor data management
- `accounts/` - Account management and validation
- `transactions/` - Transaction processing and categorization
- `reconciliation/` - Automatic and manual invoice-transfer matching
- `crypto-expenses/` - Cryptocurrency expense tracking and Flipside integration
- `settings/` - Application configuration management

#### **Integration Modules**
- `flipside/` - Blockchain data integration with comprehensive SDK
- `ai/` - AI agent capabilities for account management
- `csv/` - CSV file parsing and import functionality
- `integration/` - External service integrations (Gmail, etc.)
- `pdfParser/` - PDF document parsing for invoices

#### **Utility Modules**
- `core/` - Core utilities (currency, monetary calculations)
- `tracing/` - Application monitoring and tracing

### Components (`/src/components`)

#### **Layout Components**
- `DashboardLayout.tsx` - Main application layout wrapper
- `app-sidebar.tsx` - Navigation sidebar with menu items
- `site-header.tsx` - Top header with page title
- `nav-main.tsx`, `nav-secondary.tsx`, `nav-user.tsx` - Navigation components

#### **Feature Components**
- `InvoiceDetailsDrawer.tsx` - Invoice detail view with PDF display
- `TransactionDetailsDrawer.tsx` - Transaction detail view
- `AccountDetailsDrawer.tsx` - Account detail view
- `VendorDetailsDrawer.tsx` - Vendor detail view
- `AccountDataTable.tsx` - Account listing table
- `CSVUploadForm.tsx` - CSV file upload interface
- `ReconciliationStatusSelect.tsx` - Reconciliation status selector

#### **UI Components** (`/src/components/ui`)
- shadcn/ui component library (cards, buttons, tables, forms, etc.)
- Custom components built on Radix UI primitives

## API Routes (tRPC)

### Router Structure (`/src/server/routers/_app.ts`)
```typescript
appRouter = {
  example: exampleRouter,           // Demo endpoints
  invoices: invoiceRouter,          // Invoice CRUD operations
  vendors: vendorRouter,            // Vendor management
  settings: settingsRouter,         // App configuration
  flipside: flipsideRouter,         // Blockchain data queries
  accounts: accountRouter,          // Account management
  transactions: transactionRouter,  // Transaction operations
  csv: csvRouter,                   // CSV import/export
  reconciliation: reconciliationRouter, // Invoice-transfer matching
  cryptoExpenses: cryptoExpenseRouter   // Crypto expense tracking
}
```

### Key API Endpoints
- **Invoices**: CRUD, statistics, filtering, PDF generation, bulk operations
- **Transactions**: Listing, filtering, account-based queries
- **Reconciliation**: Auto-matching, manual matching, potential matches
- **Accounts**: CRUD, transaction import, CSV processing
- **Flipside**: Blockchain data queries, health checks, pre-built queries

## Frontend-Backend Communication

### tRPC Integration
- **Type Safety**: Full TypeScript type safety from backend to frontend
- **Real-time**: React Query integration for caching and real-time updates
- **Error Handling**: Centralized error handling with neverthrow pattern
- **Optimistic Updates**: UI updates before server confirmation

### Data Flow
1. **User Interaction** → Component state change
2. **tRPC Hook** → API call with automatic caching
3. **Router Handler** → Business logic in modules
4. **Prisma Query** → Database operation
5. **Response** → Automatic UI update via React Query

## Key Systems

### 1. Invoice Processing System
- **Import**: Gmail attachments and manual uploads
- **Parsing**: AI-powered PDF text extraction and structured data parsing
- **Validation**: Business rule validation and manual review
- **Storage**: Structured storage with line items and vendor linking

### 2. Reconciliation System
- **Auto-matching**: Algorithm-based invoice-transfer matching
- **Manual Review**: Interface for manual reconciliation decisions
- **Confidence Scoring**: ML-based confidence scores for matches
- **Audit Trail**: Complete history of reconciliation decisions

### 3. Cryptocurrency Integration
- **Flipside API**: Real-time blockchain data queries
- **Expense Categorization**: Buybacks, floor sweeps, liquidity operations
- **USD Conversion**: Automatic USD value calculation for accounting
- **Transaction Linking**: Connection to imported transaction data

### 4. AI Assistant System
- **Natural Language**: Chat interface for data queries
- **Tool Integration**: Automatic API calls based on user questions
- **Multi-provider**: Support for multiple AI providers
- **Structured Responses**: Formatted data display with charts and tables

### 5. File Management System
- **S3 Storage**: Secure file storage for invoices and documents
- **CSV Processing**: Bulk data import with validation
- **PDF Processing**: Text extraction and metadata parsing
- **File Deduplication**: Hash-based duplicate detection

## Development Workflow

### Environment Setup
```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Start development server
npm run dev
```

### Key Scripts
- `npm run dev` - Development server with Turbopack
- `npm run build` - Production build
- `npm run build:check` - TypeScript compilation check
- `npm run exc` - Execute test scripts

### Testing
- Unit tests with Vitest
- Integration tests for tRPC endpoints
- End-to-end testing for critical workflows

This application provides a comprehensive solution for modern accounting needs, combining traditional financial management with cryptocurrency tracking and AI-powered automation.
