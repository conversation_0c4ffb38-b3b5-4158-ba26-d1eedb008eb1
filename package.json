{"name": "sac-accounting-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:check": "tsc --noEmit", "start": "next start", "lint": "next lint", "exc": "tsx -r src/setup.ts src/test.ts", "postinstall": "prisma generate", "script:import-crypto-expenses": "tsx src/scripts/importCryptoExpenses.ts", "script:analyze-transfers": "tsx src/scripts/analyzeTransfers.ts", "script:cleanup-duplicate-transfers": "tsx src/scripts/cleanupDuplicateTransfers.ts"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@aws-sdk/client-s3": "^3.821.0", "@aws-sdk/s3-request-presigner": "^3.821.0", "@clack/prompts": "^0.11.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@flipsidecrypto/sdk": "^2.1.0", "@lmnr-ai/lmnr": "^0.6.10", "@mistralai/mistralai": "^1.7.1", "@openrouter/ai-sdk-provider": "^0.7.0", "@opentelemetry/api": "^1.9.0", "@prisma/client": "6.8.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.6", "@tanstack/react-store": "^0.7.1", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.3.1", "@trpc/next": "^11.3.1", "@trpc/react-query": "^11.3.1", "@trpc/server": "^11.3.1", "@types/inquirer": "^9.0.8", "@types/papaparse": "^5.3.16", "FileReader": "^0.10.2", "ai": "^4.3.16", "apache-arrow": "^20.0.0", "arktype": "^2.1.20", "arquero": "^8.0.3", "async-batch": "^1.1.2", "better-auth": "^1.2.8", "braintrust": "^0.0.205", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "core-js": "3.43.0", "cryptocompare": "^1.0.0", "currency-symbol-map": "^5.1.0", "danfojs": "^1.2.0", "danfojs-node": "^1.2.0", "date-fns": "^4.1.0", "decimal.js-light": "^2.5.1", "dotenv": "^16.5.0", "file-type": "^21.0.0", "fs-jetpack": "^5.1.0", "google-auth-library": "^9.15.1", "googleapis": "^149.0.0", "inquirer": "^12.7.0", "json5": "^2.2.3", "ky": "^1.8.1", "lfi": "^3.8.0", "limit-concur": "^3.0.0", "lucide-react": "^0.514.0", "minimist": "^1.2.8", "neverthrow": "^8.2.0", "next": "15.3.3", "next-themes": "^0.4.6", "openai": "^4.104.0", "p-map": "^7.0.3", "p-queue": "^8.1.0", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.3.31", "prisma": "^6.8.2", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-pdf": "^9.2.1", "recharts": "^3.0.2", "remeda": "^2.22.3", "sonner": "^2.0.5", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "termost": "^1.4.0", "tiktoken": "^1.0.21", "vaul": "^1.1.2", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@githubnext/vitale": "^0.0.19", "@tailwindcss/postcss": "^4", "@types/date-fns": "^2.6.3", "@types/minimist": "^1.2.5", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/yargs": "^17.0.33", "null-loader": "^4.0.1", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vitest": "^3.1.4", "yargs": "^18.0.0"}}